#!/bin/bash
# Cleanup WSL environment and finalize kernel build

echo "========================================"
echo "  🎉 KERNEL BUILD SUCCESS - CLEANUP"
echo "========================================"
echo ""

WSL_DIR="$HOME/kernel-build-wsl"
WINDOWS_DIR="/mnt/c/Sviluppo/kernelsu-galaxys9"
FINAL_DIR="$WINDOWS_DIR/kernel-build-final"

cd "$WSL_DIR"
echo "Working in: $(pwd)"
echo ""

echo "=== Step 1: Locating Built Kernel Files ==="

# Find the kernel image
if [[ -f "arch/arm64/boot/Image" ]]; then
    IMAGE_SIZE=$(ls -lh arch/arm64/boot/Image | awk '{print $5}')
    echo "✅ Kernel Image found: arch/arm64/boot/Image ($IMAGE_SIZE)"
    KERNEL_FOUND=true
else
    echo "❌ Kernel Image not found"
    KERNEL_FOUND=false
fi

# Find AnyKernel3 or ZIP files
echo ""
echo "Looking for kernel packages..."
ZIP_FILES=$(find . -name "*.zip" -type f 2>/dev/null)
if [[ -n "$ZIP_FILES" ]]; then
    echo "✅ ZIP files found:"
    echo "$ZIP_FILES" | while read zip; do
        ZIP_SIZE=$(ls -lh "$zip" | awk '{print $5}')
        echo "   📦 $zip ($ZIP_SIZE)"
    done
    PACKAGE_FOUND=true
else
    echo "❌ No ZIP packages found"
    PACKAGE_FOUND=false
fi

# Check for AnyKernel3 directory
if [[ -d "AnyKernel3" ]]; then
    echo "✅ AnyKernel3 directory found"
    ls -la AnyKernel3/
    ANYKERNEL_FOUND=true
else
    echo "❌ AnyKernel3 directory not found"
    ANYKERNEL_FOUND=false
fi

echo ""
echo "=== Step 2: Creating Final Kernel Package ==="

# Ensure we have the final organized directory
mkdir -p "$FINAL_DIR"/{kernel-final,build-artifacts,documentation,cleanup-logs}

if [[ $KERNEL_FOUND == true ]]; then
    echo "Copying kernel files to final location..."
    
    # Copy kernel image
    cp arch/arm64/boot/Image "$FINAL_DIR/kernel-final/"
    echo "✅ Copied kernel Image"
    
    # Copy device tree if available
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        cp arch/arm64/boot/dtb.img "$FINAL_DIR/kernel-final/"
        echo "✅ Copied device tree"
    fi
    
    # Copy any ZIP packages
    if [[ $PACKAGE_FOUND == true ]]; then
        find . -name "*.zip" -type f -exec cp {} "$FINAL_DIR/kernel-final/" \;
        echo "✅ Copied ZIP packages"
    fi
    
    # Copy AnyKernel3 if available
    if [[ $ANYKERNEL_FOUND == true ]]; then
        cp -r AnyKernel3 "$FINAL_DIR/kernel-final/"
        echo "✅ Copied AnyKernel3 directory"
    fi
    
    echo "✅ All kernel files copied to final location"
else
    echo "❌ No kernel files to copy"
fi

echo ""
echo "=== Step 3: Identifying Files for Cleanup ==="

# List of troubleshooting scripts that can be removed after copying
CLEANUP_SCRIPTS=(
    "fix_kernel_build.sh"
    "fix_wireguard.sh"
    "fix_ssl_issues.sh"
    "comprehensive_kernel_fix.sh"
    "quick_fix.sh"
    "wsl_diagnostic.sh"
    "wsl_kernel_fix.sh"
    "fix_defconfig_issues.sh"
    "debug_defconfig_failure.sh"
    "fix_kconfig_build.sh"
    "fix_kbuild_include_issue.sh"
    "test_fixes.sh"
    "test_kernel_build_complete.sh"
    "organize_and_find_kernel.sh"
    "complete_kernel_build.sh"
    "fix_final_build_issues.sh"
    "final_organization_and_next_steps.sh"
    "cleanup_and_finalize.sh"
)

# Build artifacts that can be cleaned
CLEANUP_ARTIFACTS=(
    "*.log"
    "*.o"
    "*.cmd"
    ".tmp_versions"
    "include/generated"
    "arch/arm64/include/generated"
    "scripts/basic/fixdep"
    "scripts/kconfig/conf"
    "scripts/dtc/dtc"
    "vmlinux"
    "System.map"
    "modules.builtin"
    "modules.order"
)

# Backup files that can be removed
CLEANUP_BACKUPS=(
    "scripts/fetch-latest-wireguard.sh.backup"
    "scripts/Kbuild.include.backup"
)

echo "Files identified for cleanup:"
echo "  📜 Troubleshooting scripts: ${#CLEANUP_SCRIPTS[@]} files"
echo "  🔧 Build artifacts: ${#CLEANUP_ARTIFACTS[@]} patterns"
echo "  💾 Backup files: ${#CLEANUP_BACKUPS[@]} files"

echo ""
echo "=== Step 4: Moving Files Before Cleanup ==="

# Copy troubleshooting scripts to final location before removing
echo "Preserving troubleshooting scripts in final location..."
for script in "${CLEANUP_SCRIPTS[@]}"; do
    if [[ -f "$script" ]]; then
        cp "$script" "$FINAL_DIR/build-artifacts/" 2>/dev/null || true
    fi
done

# Copy important logs
echo "Preserving build logs..."
find . -name "*.log" -type f -exec cp {} "$FINAL_DIR/cleanup-logs/" \; 2>/dev/null || true

# Copy backup files for reference
echo "Preserving backup files..."
for backup in "${CLEANUP_BACKUPS[@]}"; do
    if [[ -f "$backup" ]]; then
        cp "$backup" "$FINAL_DIR/build-artifacts/" 2>/dev/null || true
    fi
done

echo "✅ Important files preserved in final location"

echo ""
echo "=== Step 5: Cleaning Up WSL Directory ==="

echo "⚠️  Starting cleanup of unnecessary files..."
echo "This will remove troubleshooting scripts and build artifacts."
echo ""

# Remove troubleshooting scripts
echo "Removing troubleshooting scripts..."
removed_scripts=0
for script in "${CLEANUP_SCRIPTS[@]}"; do
    if [[ -f "$script" ]]; then
        rm -f "$script"
        ((removed_scripts++))
        echo "  🗑️  Removed: $script"
    fi
done

# Remove build artifacts
echo ""
echo "Removing build artifacts..."
removed_artifacts=0

# Remove log files
find . -name "*.log" -type f -delete 2>/dev/null || true
((removed_artifacts++))

# Remove object files and build temporaries
find . -name "*.o" -type f -delete 2>/dev/null || true
find . -name "*.cmd" -type f -delete 2>/dev/null || true
((removed_artifacts+=2))

# Remove generated directories
rm -rf .tmp_versions 2>/dev/null || true
rm -rf include/generated 2>/dev/null || true
rm -rf arch/arm64/include/generated 2>/dev/null || true
((removed_artifacts+=3))

# Remove built tools
rm -f scripts/basic/fixdep 2>/dev/null || true
rm -f scripts/kconfig/conf 2>/dev/null || true
rm -f scripts/dtc/dtc 2>/dev/null || true
((removed_artifacts+=3))

# Remove large build files (keep kernel Image)
rm -f vmlinux 2>/dev/null || true
rm -f System.map 2>/dev/null || true
rm -f modules.builtin 2>/dev/null || true
rm -f modules.order 2>/dev/null || true
((removed_artifacts+=4))

# Remove backup files
echo ""
echo "Removing backup files..."
removed_backups=0
for backup in "${CLEANUP_BACKUPS[@]}"; do
    if [[ -f "$backup" ]]; then
        rm -f "$backup"
        ((removed_backups++))
        echo "  🗑️  Removed: $backup"
    fi
done

echo ""
echo "✅ Cleanup completed:"
echo "  📜 Scripts removed: $removed_scripts"
echo "  🔧 Artifact patterns cleaned: $removed_artifacts"
echo "  💾 Backups removed: $removed_backups"

echo ""
echo "=== Step 6: Final Directory Structure ==="

echo "Current WSL directory contents (cleaned):"
ls -la | head -20

echo ""
echo "Final organized directory structure:"
if [[ -d "$FINAL_DIR" ]]; then
    echo "📁 $FINAL_DIR/"
    for subdir in kernel-final build-artifacts documentation cleanup-logs; do
        if [[ -d "$FINAL_DIR/$subdir" ]]; then
            file_count=$(ls -1 "$FINAL_DIR/$subdir" 2>/dev/null | wc -l)
            echo "  📂 $subdir/: $file_count files"
            
            # Show contents of kernel-final
            if [[ "$subdir" == "kernel-final" ]] && [[ $file_count -gt 0 ]]; then
                echo "    📱 Kernel files:"
                ls -lh "$FINAL_DIR/$subdir/" | grep -E "\.(zip|Image|img)$" | while read line; do
                    echo "      $line"
                done
            fi
        fi
    done
fi

echo ""
echo "=== Step 7: Creating Final Summary ==="

cat > "$FINAL_DIR/KERNEL_BUILD_SUCCESS.md" << EOF
# 🎉 KERNEL BUILD SUCCESS!

## Build Information
- **Date**: $(date)
- **Kernel**: ArtPlus-NEXT-G960F-250827-Permissive-KSU
- **Device**: Samsung Galaxy S9 International (SM-G960F)
- **Compiler**: Clang 19.0.0-r530567
- **Build Method**: Apollo Script (WSL)
- **Status**: ✅ **SUCCESSFUL**

## Output Files
$(if [[ $KERNEL_FOUND == true ]]; then
    echo "- ✅ **Kernel Image**: kernel-final/Image ($IMAGE_SIZE)"
fi)
$(if [[ $PACKAGE_FOUND == true ]]; then
    echo "- ✅ **Flashable ZIP**: kernel-final/*.zip"
fi)
$(if [[ $ANYKERNEL_FOUND == true ]]; then
    echo "- ✅ **AnyKernel3**: kernel-final/AnyKernel3/"
fi)

## Installation Ready
Your custom kernel is ready for installation on Galaxy S9 (SM-G960F):

### Method 1: TWRP Recovery (Recommended)
1. Copy ZIP file to device storage
2. Boot to TWRP recovery  
3. Install → Select ZIP file
4. Swipe to confirm flash
5. Reboot system

### Method 2: ODIN
1. Create TAR with Image file
2. Flash via ODIN AP slot

## Post-Installation
1. Install KernelSU Manager APK
2. Verify root access working
3. Test all device functions

## Features
- ✅ **KernelSU Root**: Version 12030
- ✅ **SELinux**: Permissive mode
- ✅ **Optimizations**: Clang 19 with LLVM optimizations
- ✅ **Compatibility**: Android 10+ ROMs

## Cleanup Status
- ✅ WSL directory cleaned of unnecessary files
- ✅ All important files preserved in organized structure
- ✅ Troubleshooting scripts archived for future reference

**🚀 CONGRATULATIONS! Your Galaxy S9 kernel build is complete and ready for installation!**
EOF

echo "✅ Final summary created"

echo ""
echo "========================================"
echo "  🎉 CLEANUP AND FINALIZATION COMPLETE!"
echo "========================================"
echo ""

if [[ $KERNEL_FOUND == true ]]; then
    echo "🎉 **SUCCESS! Your kernel is ready for installation!**"
    echo ""
    echo "📱 **Kernel**: ArtPlus-NEXT-G960F-250827-Permissive-KSU"
    echo "📁 **Location**: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-final\\kernel-final\\"
    echo ""
    echo "📦 **Files ready for installation**:"
    if [[ $PACKAGE_FOUND == true ]]; then
        echo "   ✅ Flashable ZIP for TWRP"
    fi
    echo "   ✅ Raw kernel Image for ODIN"
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        echo "   ✅ Device tree blob"
    fi
    echo ""
    echo "🚀 **Next steps**:"
    echo "   1. Navigate to the kernel-final folder in Windows"
    echo "   2. Copy ZIP file to your Galaxy S9"
    echo "   3. Flash via TWRP recovery"
    echo "   4. Install KernelSU Manager"
    echo "   5. Enjoy your rooted Galaxy S9!"
    
else
    echo "⚠️  **Kernel files not found after cleanup**"
    echo "Check the kernel-final directory for available files."
fi

echo ""
echo "🧹 **Cleanup summary**:"
echo "   📜 Removed $removed_scripts troubleshooting scripts"
echo "   🔧 Cleaned $removed_artifacts build artifacts"  
echo "   💾 Removed $removed_backups backup files"
echo "   ✅ WSL directory is now clean and organized"
echo ""
echo "All important files are preserved in:"
echo "**C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-final\\**"
