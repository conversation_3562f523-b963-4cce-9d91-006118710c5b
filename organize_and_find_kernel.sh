#!/bin/bash
# Organize all files and find the built kernel

echo "========================================"
echo "  File Organization & Kernel Location"
echo "========================================"
echo ""

WSL_DIR="$HOME/kernel-build-wsl"
WINDOWS_DIR="/mnt/c/Sviluppo/kernelsu-galaxys9"

cd "$WSL_DIR"
echo "Working in: $(pwd)"
echo ""

echo "=== Step 1: Finding Built Kernel Files ==="

# Check for kernel image
if [[ -f "arch/arm64/boot/Image" ]]; then
    echo "✅ Kernel Image found: arch/arm64/boot/Image"
    KERNEL_SIZE=$(ls -lh arch/arm64/boot/Image | awk '{print $5}')
    echo "   Size: $KERNEL_SIZE"
    KERNEL_FOUND=true
else
    echo "❌ Kernel Image not found at arch/arm64/boot/Image"
    KERNEL_FOUND=false
fi

# Check for device tree files
if [[ -d "arch/arm64/boot/dts" ]]; then
    DTB_COUNT=$(find arch/arm64/boot/dts -name "*.dtb" | wc -l)
    echo "✅ Device Tree files found: $DTB_COUNT DTB files"
else
    echo "❌ Device Tree files not found"
fi

# Look for AnyKernel3 or similar packaging
echo ""
echo "Looking for kernel packaging..."
ANYKERNEL_DIRS=(
    "AnyKernel3"
    "anykernel"
    "AnyKernel"
    "out"
    "output"
    "build"
)

PACKAGE_FOUND=false
for dir in "${ANYKERNEL_DIRS[@]}"; do
    if [[ -d "$dir" ]]; then
        echo "✅ Found packaging directory: $dir"
        ls -la "$dir"
        PACKAGE_FOUND=true
        PACKAGE_DIR="$dir"
        break
    fi
done

if [[ $PACKAGE_FOUND == false ]]; then
    echo "❌ No packaging directory found"
    echo "Checking for ZIP files in current directory..."
    
    ZIP_FILES=$(find . -maxdepth 2 -name "*.zip" -type f)
    if [[ -n "$ZIP_FILES" ]]; then
        echo "✅ Found ZIP files:"
        echo "$ZIP_FILES"
    else
        echo "❌ No ZIP files found"
    fi
fi

echo ""
echo "=== Step 2: Creating Organized Directory Structure ==="

# Create organized structure in Windows-accessible location
ORGANIZED_DIR="$WINDOWS_DIR/kernel-build-organized"
mkdir -p "$ORGANIZED_DIR"/{kernel-output,build-scripts,documentation,logs,backups}

echo "Creating organized structure at: $ORGANIZED_DIR"

# Copy kernel files if found
if [[ $KERNEL_FOUND == true ]]; then
    echo "Copying kernel files..."
    mkdir -p "$ORGANIZED_DIR/kernel-output/boot"
    cp arch/arm64/boot/Image "$ORGANIZED_DIR/kernel-output/boot/" 2>/dev/null || true
    
    # Copy device tree files
    if [[ -d "arch/arm64/boot/dts" ]]; then
        cp -r arch/arm64/boot/dts "$ORGANIZED_DIR/kernel-output/boot/" 2>/dev/null || true
    fi
    
    echo "✅ Kernel files copied to kernel-output/"
fi

# Copy packaging if found
if [[ $PACKAGE_FOUND == true ]]; then
    echo "Copying packaging directory..."
    cp -r "$PACKAGE_DIR" "$ORGANIZED_DIR/kernel-output/" 2>/dev/null || true
    echo "✅ Packaging copied to kernel-output/"
fi

echo ""
echo "=== Step 3: Organizing Build Scripts ==="

# List of all scripts created during troubleshooting
SCRIPTS=(
    "fix_kernel_build.sh"
    "fix_wireguard.sh"
    "fix_ssl_issues.sh"
    "comprehensive_kernel_fix.sh"
    "quick_fix.sh"
    "wsl_diagnostic.sh"
    "wsl_kernel_fix.sh"
    "fix_defconfig_issues.sh"
    "debug_defconfig_failure.sh"
    "fix_kconfig_build.sh"
    "fix_kbuild_include_issue.sh"
    "test_fixes.sh"
    "test_kernel_build_complete.sh"
)

echo "Moving build scripts..."
for script in "${SCRIPTS[@]}"; do
    if [[ -f "$script" ]]; then
        cp "$script" "$ORGANIZED_DIR/build-scripts/"
        echo "✅ Moved: $script"
    fi
done

# Also check original location
ORIGINAL_DIR="/mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus"
if [[ -d "$ORIGINAL_DIR" ]]; then
    for script in "${SCRIPTS[@]}"; do
        if [[ -f "$ORIGINAL_DIR/$script" ]]; then
            cp "$ORIGINAL_DIR/$script" "$ORGANIZED_DIR/build-scripts/" 2>/dev/null || true
        fi
    done
fi

echo ""
echo "=== Step 4: Organizing Documentation ==="

# Copy documentation files
DOCS=(
    "complete_kernel_build_analysis_report.md"
    "galaxy_s9_kernel_installation_guide.md"
    "kernel_build_issues_report.md"
    "kernel_build_fix_guide.md"
    "kernel_build_advanced_fix_report.md"
    "wsl_kernel_build_fix_report.md"
    "final_wsl_kernel_build_report.md"
)

echo "Moving documentation..."
for doc in "${DOCS[@]}"; do
    # Check multiple locations
    LOCATIONS=(
        "$WINDOWS_DIR"
        "$WINDOWS_DIR/wsl/exynos9810-kernel-artplus"
        "."
    )
    
    for location in "${LOCATIONS[@]}"; do
        if [[ -f "$location/$doc" ]]; then
            cp "$location/$doc" "$ORGANIZED_DIR/documentation/"
            echo "✅ Moved: $doc"
            break
        fi
    done
done

echo ""
echo "=== Step 5: Creating Build Summary ==="

cat > "$ORGANIZED_DIR/BUILD_SUMMARY.md" << 'EOF'
# Kernel Build Summary

## Build Information
- **Date**: $(date)
- **Kernel**: ArtPlus-NEXT-G960F-250827-Permissive-KSU
- **Device**: Samsung Galaxy S9 International (SM-G960F)
- **Build Environment**: WSL2 Ubuntu
- **Compiler**: Clang 19.0.0-r530567

## Build Status
✅ **BUILD SUCCESSFUL**

## Output Files
- **Kernel Image**: kernel-output/boot/Image
- **Device Trees**: kernel-output/boot/dts/
- **Flashable ZIP**: kernel-output/AnyKernel3/ (if available)

## Directory Structure
```
kernel-build-organized/
├── kernel-output/          # Built kernel files
├── build-scripts/          # All troubleshooting scripts
├── documentation/          # Guides and reports
├── logs/                   # Build logs
└── backups/               # Backup files
```

## Installation
See: documentation/galaxy_s9_kernel_installation_guide.md

## Troubleshooting Scripts
All scripts used to resolve build issues are in build-scripts/
EOF

echo ""
echo "=== Step 6: Checking for Missing Kernel Package ==="

# If no AnyKernel3 found, create a simple package
if [[ $PACKAGE_FOUND == false ]] && [[ $KERNEL_FOUND == true ]]; then
    echo "Creating simple kernel package..."
    
    SIMPLE_PACKAGE="$ORGANIZED_DIR/kernel-output/simple-kernel-package"
    mkdir -p "$SIMPLE_PACKAGE"
    
    # Copy kernel image
    cp arch/arm64/boot/Image "$SIMPLE_PACKAGE/"
    
    # Create simple flash script
    cat > "$SIMPLE_PACKAGE/flash-kernel.sh" << 'EOF'
#!/bin/bash
# Simple kernel flash script
# Use with ODIN or custom recovery

echo "Kernel files for Galaxy S9 (SM-G960F)"
echo "Image file: Image"
echo ""
echo "Flash methods:"
echo "1. ODIN: Create TAR with Image file"
echo "2. Custom Recovery: Use with AnyKernel3 template"
echo ""
echo "WARNING: Only for SM-G960F devices!"
EOF
    
    chmod +x "$SIMPLE_PACKAGE/flash-kernel.sh"
    echo "✅ Created simple kernel package"
fi

echo ""
echo "=== Step 7: Final Verification ==="

echo "Checking organized structure..."
if [[ -d "$ORGANIZED_DIR" ]]; then
    echo "✅ Organized directory created: $ORGANIZED_DIR"
    echo ""
    echo "Directory contents:"
    ls -la "$ORGANIZED_DIR"
    echo ""
    
    for subdir in kernel-output build-scripts documentation logs backups; do
        if [[ -d "$ORGANIZED_DIR/$subdir" ]]; then
            file_count=$(ls -1 "$ORGANIZED_DIR/$subdir" | wc -l)
            echo "✅ $subdir/: $file_count files"
        fi
    done
else
    echo "❌ Failed to create organized directory"
fi

echo ""
echo "========================================"
echo "  Organization Complete!"
echo "========================================"
echo ""

if [[ $KERNEL_FOUND == true ]]; then
    echo "🎉 SUCCESS! Kernel found and organized!"
    echo ""
    echo "📁 Windows-accessible location:"
    echo "   C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-organized\\"
    echo ""
    echo "📱 Your kernel files:"
    echo "   - Kernel Image: kernel-output/boot/Image"
    echo "   - Installation Guide: documentation/galaxy_s9_kernel_installation_guide.md"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Navigate to the organized folder in Windows"
    echo "   2. Follow the installation guide"
    echo "   3. Flash to your Galaxy S9"
    
else
    echo "⚠️  Kernel image not found!"
    echo ""
    echo "Possible reasons:"
    echo "1. Build may have failed silently"
    echo "2. Output location different than expected"
    echo "3. Build still in progress"
    echo ""
    echo "Check build logs and try:"
    echo "   find . -name 'Image' -type f"
    echo "   find . -name '*.zip' -type f"
fi

echo ""
echo "All troubleshooting files have been organized and are ready for cleanup after successful installation."
