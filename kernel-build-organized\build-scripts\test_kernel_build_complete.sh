#!/bin/bash
# Complete kernel build test after fixes

echo "========================================"
echo "  Complete Kernel Build Test"
echo "========================================"
echo ""

WSL_DIR="$HOME/kernel-build-wsl"
if [[ ! -d "$WSL_DIR" ]]; then
    echo "❌ WSL kernel directory not found: $WSL_DIR"
    exit 1
fi

cd "$WSL_DIR"
echo "Testing in: $(pwd)"
echo ""

# Set environment
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export ANDROID_MAJOR_VERSION=q

echo "Environment:"
echo "  ARCH=$ARCH"
echo "  CROSS_COMPILE=$CROSS_COMPILE"
echo "  ANDROID_MAJOR_VERSION=$ANDROID_MAJOR_VERSION"
echo ""

echo "=== Test 1: Basic Make Functionality ==="
if make help > /dev/null 2>&1; then
    echo "✅ Basic make works"
else
    echo "❌ Basic make fails"
    make help 2>&1 | head -10
fi
echo ""

echo "=== Test 2: Defconfig Generation ==="
DEFCONFIGS=("exynos9810_defconfig" "starlte_defconfig" "star2lte_defconfig" "crownlte_defconfig")
WORKING_CONFIGS=()

for config in "${DEFCONFIGS[@]}"; do
    echo "Testing $config..."
    if make $config > /dev/null 2>&1; then
        echo "✅ $config works"
        WORKING_CONFIGS+=("$config")
    else
        echo "❌ $config fails"
    fi
done

if [[ ${#WORKING_CONFIGS[@]} -gt 0 ]]; then
    echo ""
    echo "✅ Working configurations: ${WORKING_CONFIGS[*]}"
    BEST_CONFIG="${WORKING_CONFIGS[0]}"
else
    echo ""
    echo "❌ No working configurations found"
    exit 1
fi
echo ""

echo "=== Test 3: Build Preparation ==="
echo "Using configuration: $BEST_CONFIG"
make $BEST_CONFIG > /dev/null 2>&1

if make prepare > /dev/null 2>&1; then
    echo "✅ Build preparation successful"
    PREP_SUCCESS=true
else
    echo "❌ Build preparation failed"
    PREP_SUCCESS=false
fi
echo ""

echo "=== Test 4: Apollo Script Test ==="
echo "Testing Apollo script functionality..."

# Create Apollo test input
cat > apollo_input.txt << 'EOF'
1
4
1
y
n
EOF

# Test Apollo script with timeout
timeout 60s ./apollo.sh < apollo_input.txt > apollo_full_test.log 2>&1 || true

# Analyze Apollo output
if [[ -f apollo_full_test.log ]]; then
    echo "Apollo script output analysis:"
    
    if grep -q "missing separator" apollo_full_test.log; then
        echo "❌ Apollo still has Kbuild.include issues"
        APOLLO_SUCCESS=false
    elif grep -q "Compiler Downloaded" apollo_full_test.log; then
        echo "✅ Apollo successfully downloaded compiler"
        APOLLO_SUCCESS=true
    elif grep -q "config generated" apollo_full_test.log; then
        echo "✅ Apollo successfully generated config"
        APOLLO_SUCCESS=true
    else
        echo "⚠️  Apollo test inconclusive"
        APOLLO_SUCCESS=false
    fi
    
    echo ""
    echo "Last 10 lines of Apollo output:"
    tail -10 apollo_full_test.log
else
    echo "❌ Apollo test log not created"
    APOLLO_SUCCESS=false
fi

# Cleanup
rm -f apollo_input.txt apollo_full_test.log
echo ""

echo "=== Test 5: Manual Kernel Build Test ==="
if [[ $PREP_SUCCESS == true ]]; then
    echo "Attempting partial kernel build (first 100 files)..."
    
    # Try building just a few files to test compilation
    timeout 300s make -j2 scripts/ > build_test.log 2>&1 || true
    
    if grep -q "error:" build_test.log; then
        echo "❌ Build errors detected"
        echo "First few errors:"
        grep "error:" build_test.log | head -5
        BUILD_SUCCESS=false
    elif grep -q "warning:" build_test.log; then
        echo "⚠️  Build has warnings but may succeed"
        echo "Sample warnings:"
        grep "warning:" build_test.log | head -3
        BUILD_SUCCESS=true
    else
        echo "✅ Build test successful (no errors detected)"
        BUILD_SUCCESS=true
    fi
    
    rm -f build_test.log
else
    echo "❌ Skipping build test (preparation failed)"
    BUILD_SUCCESS=false
fi
echo ""

echo "=== Test 6: Toolchain Verification ==="
if command -v $CROSS_COMPILE"gcc" > /dev/null; then
    echo "✅ Cross-compiler available: $($CROSS_COMPILE"gcc" --version | head -1)"
    TOOLCHAIN_OK=true
else
    echo "❌ Cross-compiler not found: $CROSS_COMPILE"gcc""
    TOOLCHAIN_OK=false
fi

# Test compilation
echo "Testing cross-compilation..."
cat > test_compile.c << 'EOF'
#include <linux/module.h>
#include <linux/kernel.h>
static int __init test_init(void) { return 0; }
static void __exit test_exit(void) { }
module_init(test_init);
module_exit(test_exit);
MODULE_LICENSE("GPL");
EOF

if $CROSS_COMPILE"gcc" -c test_compile.c -o test_compile.o > /dev/null 2>&1; then
    echo "✅ Cross-compilation test successful"
    COMPILE_TEST=true
else
    echo "❌ Cross-compilation test failed"
    COMPILE_TEST=false
fi

rm -f test_compile.c test_compile.o
echo ""

echo "========================================"
echo "  Complete Test Results Summary"
echo "========================================"
echo ""

# Count successful tests
TOTAL_TESTS=6
PASSED_TESTS=0

echo "Test Results:"
echo "1. Basic Make:           $([ $? -eq 0 ] && echo "✅ PASS" || echo "❌ FAIL")"
[[ ${#WORKING_CONFIGS[@]} -gt 0 ]] && ((PASSED_TESTS++))
echo "2. Defconfig:            $([ ${#WORKING_CONFIGS[@]} -gt 0 ] && echo "✅ PASS (${#WORKING_CONFIGS[@]}/4)" || echo "❌ FAIL")"
[[ $PREP_SUCCESS == true ]] && ((PASSED_TESTS++))
echo "3. Build Preparation:    $([ $PREP_SUCCESS == true ] && echo "✅ PASS" || echo "❌ FAIL")"
[[ $APOLLO_SUCCESS == true ]] && ((PASSED_TESTS++))
echo "4. Apollo Script:        $([ $APOLLO_SUCCESS == true ] && echo "✅ PASS" || echo "❌ FAIL")"
[[ $BUILD_SUCCESS == true ]] && ((PASSED_TESTS++))
echo "5. Build Test:           $([ $BUILD_SUCCESS == true ] && echo "✅ PASS" || echo "❌ FAIL")"
[[ $TOOLCHAIN_OK == true ]] && [[ $COMPILE_TEST == true ]] && ((PASSED_TESTS++))
echo "6. Toolchain:            $([ $TOOLCHAIN_OK == true ] && [ $COMPILE_TEST == true ] && echo "✅ PASS" || echo "❌ FAIL")"

echo ""
echo "Overall Score: $PASSED_TESTS/$TOTAL_TESTS tests passed"
echo ""

if [[ $PASSED_TESTS -ge 4 ]]; then
    echo "🎉 KERNEL BUILD ENVIRONMENT IS READY!"
    echo ""
    echo "✅ The fixes have been successful!"
    echo "✅ Your WSL environment can now build the kernel"
    echo ""
    echo "To build the complete kernel:"
    echo ""
    echo "Method 1 - Apollo Script (Recommended):"
    echo "  cd ~/kernel-build-wsl"
    echo "  ./apollo.sh"
    echo ""
    echo "Method 2 - Manual Build:"
    echo "  cd ~/kernel-build-wsl"
    echo "  export ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- ANDROID_MAJOR_VERSION=q"
    echo "  make $BEST_CONFIG"
    echo "  make -j\$(nproc)"
    echo ""
    echo "Expected build time: 30-60 minutes"
    echo "Output will be: arch/arm64/boot/Image"
    
elif [[ $PASSED_TESTS -ge 2 ]]; then
    echo "⚠️  PARTIAL SUCCESS"
    echo ""
    echo "Some tests passed but issues remain."
    echo "You may be able to build with manual intervention."
    echo ""
    echo "Try:"
    echo "  cd ~/kernel-build-wsl"
    echo "  make $BEST_CONFIG"
    echo "  make -j2  # Use fewer cores to avoid issues"
    
else
    echo "❌ SIGNIFICANT ISSUES REMAIN"
    echo ""
    echo "The kernel build environment still has problems."
    echo "Consider using the native Kali Linux build guide instead."
fi

echo ""
echo "Test completed at: $(date)"
echo "Log files created in: $(pwd)"
