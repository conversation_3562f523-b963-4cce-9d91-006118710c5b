// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright (C) 2015-2019 <PERSON> <<PERSON>@zx2c4.com>. All Rights Reserved.
 */

struct chacha20_testvec {
	const u8 *input, *output, *key;
	u64 nonce;
	size_t ilen;
};

struct hchacha20_testvec {
	u8 key[HCHACHA20_KEY_SIZE];
	u8 nonce[HCHACHA20_NONCE_SIZE];
	u8 output[CHACHA20_KEY_SIZE];
};

/* These test vectors are generated by reference implementations and are
 * designed to check chacha20 implementation block handling, as well as from
 * the draft-arciszewski-xchacha-01 document.
 */

static const u8 input01[] __initconst = { };
static const u8 output01[] __initconst = { };
static const u8 key01[] __initconst = {
	0x09, 0xf4, 0xe8, 0x57, 0x10, 0xf2, 0x12, 0xc3,
	0xc6, 0x91, 0xc4, 0x09, 0x97, 0x46, 0xef, 0xfe,
	0x02, 0x00, 0xe4, 0x5c, 0x82, 0xed, 0x16, 0xf3,
	0x32, 0xbe, 0xec, 0x7a, 0xe6, 0x68, 0x12, 0x26
};
enum { nonce01 = 0x3834e2afca3c66d3ULL };

static const u8 input02[] __initconst = {
	0x9d
};
static const u8 output02[] __initconst = {
	0x94
};
static const u8 key02[] __initconst = {
	0x8c, 0x01, 0xac, 0xaf, 0x62, 0x63, 0x56, 0x7a,
	0xad, 0x23, 0x4c, 0x58, 0x29, 0x29, 0xbe, 0xab,
	0xe9, 0xf8, 0xdf, 0x6c, 0x8c, 0x74, 0x4d, 0x7d,
	0x13, 0x94, 0x10, 0x02, 0x3d, 0x8e, 0x9f, 0x94
};
enum { nonce02 = 0x5d1b3bfdedd9f73aULL };

static const u8 input03[] __initconst = {
	0x04, 0x16
};
static const u8 output03[] __initconst = {
	0x92, 0x07
};
static const u8 key03[] __initconst = {
	0x22, 0x0c, 0x79, 0x2c, 0x38, 0x51, 0xbe, 0x99,
	0xa9, 0x59, 0x24, 0x50, 0xef, 0x87, 0x38, 0xa6,
	0xa0, 0x97, 0x20, 0xcb, 0xb4, 0x0c, 0x94, 0x67,
	0x1f, 0x98, 0xdc, 0xc4, 0x83, 0xbc, 0x35, 0x4d
};
enum { nonce03 = 0x7a3353ad720a3e2eULL };

static const u8 input04[] __initconst = {
	0xc7, 0xcc, 0xd0
};
static const u8 output04[] __initconst = {
	0xd8, 0x41, 0x80
};
static const u8 key04[] __initconst = {
	0x81, 0x5e, 0x12, 0x01, 0xc4, 0x36, 0x15, 0x03,
	0x11, 0xa0, 0xe9, 0x86, 0xbb, 0x5a, 0xdc, 0x45,
	0x7d, 0x5e, 0x98, 0xf8, 0x06, 0x76, 0x1c, 0xec,
	0xc0, 0xf7, 0xca, 0x4e, 0x99, 0xd9, 0x42, 0x38
};
enum { nonce04 = 0x6816e2fc66176da2ULL };

static const u8 input05[] __initconst = {
	0x48, 0xf1, 0x31, 0x5f
};
static const u8 output05[] __initconst = {
	0x48, 0xf7, 0x13, 0x67
};
static const u8 key05[] __initconst = {
	0x3f, 0xd6, 0xb6, 0x5e, 0x2f, 0xda, 0x82, 0x39,
	0x97, 0x06, 0xd3, 0x62, 0x4f, 0xbd, 0xcb, 0x9b,
	0x1d, 0xe6, 0x4a, 0x76, 0xab, 0xdd, 0x14, 0x50,
	0x59, 0x21, 0xe3, 0xb2, 0xc7, 0x95, 0xbc, 0x45
};
enum { nonce05 = 0xc41a7490e228cc42ULL };

static const u8 input06[] __initconst = {
	0xae, 0xa2, 0x85, 0x1d, 0xc8
};
static const u8 output06[] __initconst = {
	0xfa, 0xff, 0x45, 0x6b, 0x6f
};
static const u8 key06[] __initconst = {
	0x04, 0x8d, 0xea, 0x67, 0x20, 0x78, 0xfb, 0x8f,
	0x49, 0x80, 0x35, 0xb5, 0x7b, 0xe4, 0x31, 0x74,
	0x57, 0x43, 0x3a, 0x64, 0x64, 0xb9, 0xe6, 0x23,
	0x4d, 0xfe, 0xb8, 0x7b, 0x71, 0x4d, 0x9d, 0x21
};
enum { nonce06 = 0x251366db50b10903ULL };

static const u8 input07[] __initconst = {
	0x1a, 0x32, 0x85, 0xb6, 0xe8, 0x52
};
static const u8 output07[] __initconst = {
	0xd3, 0x5f, 0xf0, 0x07, 0x69, 0xec
};
static const u8 key07[] __initconst = {
	0xbf, 0x2d, 0x42, 0x99, 0x97, 0x76, 0x04, 0xad,
	0xd3, 0x8f, 0x6e, 0x6a, 0x34, 0x85, 0xaf, 0x81,
	0xef, 0x36, 0x33, 0xd5, 0x43, 0xa2, 0xaa, 0x08,
	0x0f, 0x77, 0x42, 0x83, 0x58, 0xc5, 0x42, 0x2a
};
enum { nonce07 = 0xe0796da17dba9b58ULL };

static const u8 input08[] __initconst = {
	0x40, 0xae, 0xcd, 0xe4, 0x3d, 0x22, 0xe0
};
static const u8 output08[] __initconst = {
	0xfd, 0x8a, 0x9f, 0x3d, 0x05, 0xc9, 0xd3
};
static const u8 key08[] __initconst = {
	0xdc, 0x3f, 0x41, 0xe3, 0x23, 0x2a, 0x8d, 0xf6,
	0x41, 0x2a, 0xa7, 0x66, 0x05, 0x68, 0xe4, 0x7b,
	0xc4, 0x58, 0xd6, 0xcc, 0xdf, 0x0d, 0xc6, 0x25,
	0x1b, 0x61, 0x32, 0x12, 0x4e, 0xf1, 0xe6, 0x29
};
enum { nonce08 = 0xb1d2536d9e159832ULL };

static const u8 input09[] __initconst = {
	0xba, 0x1d, 0x14, 0x16, 0x9f, 0x83, 0x67, 0x24
};
static const u8 output09[] __initconst = {
	0x7c, 0xe3, 0x78, 0x1d, 0xa2, 0xe7, 0xe9, 0x39
};
static const u8 key09[] __initconst = {
	0x17, 0x55, 0x90, 0x52, 0xa4, 0xce, 0x12, 0xae,
	0xd4, 0xfd, 0xd4, 0xfb, 0xd5, 0x18, 0x59, 0x50,
	0x4e, 0x51, 0x99, 0x32, 0x09, 0x31, 0xfc, 0xf7,
	0x27, 0x10, 0x8e, 0xa2, 0x4b, 0xa5, 0xf5, 0x62
};
enum { nonce09 = 0x495fc269536d003ULL };

static const u8 input10[] __initconst = {
	0x09, 0xfd, 0x3c, 0x0b, 0x3d, 0x0e, 0xf3, 0x9d,
	0x27
};
static const u8 output10[] __initconst = {
	0xdc, 0xe4, 0x33, 0x60, 0x0c, 0x07, 0xcb, 0x51,
	0x6b
};
static const u8 key10[] __initconst = {
	0x4e, 0x00, 0x72, 0x37, 0x0f, 0x52, 0x4d, 0x6f,
	0x37, 0x50, 0x3c, 0xb3, 0x51, 0x81, 0x49, 0x16,
	0x7e, 0xfd, 0xb1, 0x51, 0x72, 0x2e, 0xe4, 0x16,
	0x68, 0x5c, 0x5b, 0x8a, 0xc3, 0x90, 0x70, 0x04
};
enum { nonce10 = 0x1ad9d1114d88cbbdULL };

static const u8 input11[] __initconst = {
	0x70, 0x18, 0x52, 0x85, 0xba, 0x66, 0xff, 0x2c,
	0x9a, 0x46
};
static const u8 output11[] __initconst = {
	0xf5, 0x2a, 0x7a, 0xfd, 0x31, 0x7c, 0x91, 0x41,
	0xb1, 0xcf
};
static const u8 key11[] __initconst = {
	0x48, 0xb4, 0xd0, 0x7c, 0x88, 0xd1, 0x96, 0x0d,
	0x80, 0x33, 0xb4, 0xd5, 0x31, 0x9a, 0x88, 0xca,
	0x14, 0xdc, 0xf0, 0xa8, 0xf3, 0xac, 0xb8, 0x47,
	0x75, 0x86, 0x7c, 0x88, 0x50, 0x11, 0x43, 0x40
};
enum { nonce11 = 0x47c35dd1f4f8aa4fULL };

static const u8 input12[] __initconst = {
	0x9e, 0x8e, 0x3d, 0x2a, 0x05, 0xfd, 0xe4, 0x90,
	0x24, 0x1c, 0xd3
};
static const u8 output12[] __initconst = {
	0x97, 0x72, 0x40, 0x9f, 0xc0, 0x6b, 0x05, 0x33,
	0x42, 0x7e, 0x28
};
static const u8 key12[] __initconst = {
	0xee, 0xff, 0x33, 0x33, 0xe0, 0x28, 0xdf, 0xa2,
	0xb6, 0x5e, 0x25, 0x09, 0x52, 0xde, 0xa5, 0x9c,
	0x8f, 0x95, 0xa9, 0x03, 0x77, 0x0f, 0xbe, 0xa1,
	0xd0, 0x7d, 0x73, 0x2f, 0xf8, 0x7e, 0x51, 0x44
};
enum { nonce12 = 0xc22d044dc6ea4af3ULL };

static const u8 input13[] __initconst = {
	0x9c, 0x16, 0xa2, 0x22, 0x4d, 0xbe, 0x04, 0x9a,
	0xb3, 0xb5, 0xc6, 0x58
};
static const u8 output13[] __initconst = {
	0xf0, 0x81, 0xdb, 0x6d, 0xa3, 0xe9, 0xb2, 0xc6,
	0x32, 0x50, 0x16, 0x9f
};
static const u8 key13[] __initconst = {
	0x96, 0xb3, 0x01, 0xd2, 0x7a, 0x8c, 0x94, 0x09,
	0x4f, 0x58, 0xbe, 0x80, 0xcc, 0xa9, 0x7e, 0x2d,
	0xad, 0x58, 0x3b, 0x63, 0xb8, 0x5c, 0x17, 0xce,
	0xbf, 0x43, 0x33, 0x7a, 0x7b, 0x82, 0x28, 0x2f
};
enum { nonce13 = 0x2a5d05d88cd7b0daULL };

static const u8 input14[] __initconst = {
	0x57, 0x4f, 0xaa, 0x30, 0xe6, 0x23, 0x50, 0x86,
	0x91, 0xa5, 0x60, 0x96, 0x2b
};
static const u8 output14[] __initconst = {
	0x6c, 0x1f, 0x3b, 0x42, 0xb6, 0x2f, 0xf0, 0xbd,
	0x76, 0x60, 0xc7, 0x7e, 0x8d
};
static const u8 key14[] __initconst = {
	0x22, 0x85, 0xaf, 0x8f, 0xa3, 0x53, 0xa0, 0xc4,
	0xb5, 0x75, 0xc0, 0xba, 0x30, 0x92, 0xc3, 0x32,
	0x20, 0x5a, 0x8f, 0x7e, 0x93, 0xda, 0x65, 0x18,
	0xd1, 0xf6, 0x9a, 0x9b, 0x8f, 0x85, 0x30, 0xe6
};
enum { nonce14 = 0xf9946c166aa4475fULL };

static const u8 input15[] __initconst = {
	0x89, 0x81, 0xc7, 0xe2, 0x00, 0xac, 0x52, 0x70,
	0xa4, 0x79, 0xab, 0xeb, 0x74, 0xf7
};
static const u8 output15[] __initconst = {
	0xb4, 0xd0, 0xa9, 0x9d, 0x15, 0x5f, 0x48, 0xd6,
	0x00, 0x7e, 0x4c, 0x77, 0x5a, 0x46
};
static const u8 key15[] __initconst = {
	0x0a, 0x66, 0x36, 0xca, 0x5d, 0x82, 0x23, 0xb6,
	0xe4, 0x9b, 0xad, 0x5e, 0xd0, 0x7f, 0xf6, 0x7a,
	0x7b, 0x03, 0xa7, 0x4c, 0xfd, 0xec, 0xd5, 0xa1,
	0xfc, 0x25, 0x54, 0xda, 0x5a, 0x5c, 0xf0, 0x2c
};
enum { nonce15 = 0x9ab2b87a35e772c8ULL };

static const u8 input16[] __initconst = {
	0x5f, 0x09, 0xc0, 0x8b, 0x1e, 0xde, 0xca, 0xd9,
	0xb7, 0x5c, 0x23, 0xc9, 0x55, 0x1e, 0xcf
};
static const u8 output16[] __initconst = {
	0x76, 0x9b, 0x53, 0xf3, 0x66, 0x88, 0x28, 0x60,
	0x98, 0x80, 0x2c, 0xa8, 0x80, 0xa6, 0x48
};
static const u8 key16[] __initconst = {
	0x80, 0xb5, 0x51, 0xdf, 0x17, 0x5b, 0xb0, 0xef,
	0x8b, 0x5b, 0x2e, 0x3e, 0xc5, 0xe3, 0xa5, 0x86,
	0xac, 0x0d, 0x8e, 0x32, 0x90, 0x9d, 0x82, 0x27,
	0xf1, 0x23, 0x26, 0xc3, 0xea, 0x55, 0xb6, 0x63
};
enum { nonce16 = 0xa82e9d39e4d02ef5ULL };

static const u8 input17[] __initconst = {
	0x87, 0x0b, 0x36, 0x71, 0x7c, 0xb9, 0x0b, 0x80,
	0x4d, 0x77, 0x5c, 0x4f, 0xf5, 0x51, 0x0e, 0x1a
};
static const u8 output17[] __initconst = {
	0xf1, 0x12, 0x4a, 0x8a, 0xd9, 0xd0, 0x08, 0x67,
	0x66, 0xd7, 0x34, 0xea, 0x32, 0x3b, 0x54, 0x0e
};
static const u8 key17[] __initconst = {
	0xfb, 0x71, 0x5f, 0x3f, 0x7a, 0xc0, 0x9a, 0xc8,
	0xc8, 0xcf, 0xe8, 0xbc, 0xfb, 0x09, 0xbf, 0x89,
	0x6a, 0xef, 0xd5, 0xe5, 0x36, 0x87, 0x14, 0x76,
	0x00, 0xb9, 0x32, 0x28, 0xb2, 0x00, 0x42, 0x53
};
enum { nonce17 = 0x229b87e73d557b96ULL };

static const u8 input18[] __initconst = {
	0x38, 0x42, 0xb5, 0x37, 0xb4, 0x3d, 0xfe, 0x59,
	0x38, 0x68, 0x88, 0xfa, 0x89, 0x8a, 0x5f, 0x90,
	0x3c
};
static const u8 output18[] __initconst = {
	0xac, 0xad, 0x14, 0xe8, 0x7e, 0xd7, 0xce, 0x96,
	0x3d, 0xb3, 0x78, 0x85, 0x22, 0x5a, 0xcb, 0x39,
	0xd4
};
static const u8 key18[] __initconst = {
	0xe1, 0xc1, 0xa8, 0xe0, 0x91, 0xe7, 0x38, 0x66,
	0x80, 0x17, 0x12, 0x3c, 0x5e, 0x2d, 0xbb, 0xea,
	0xeb, 0x6c, 0x8b, 0xc8, 0x1b, 0x6f, 0x7c, 0xea,
	0x50, 0x57, 0x23, 0x1e, 0x65, 0x6f, 0x6d, 0x81
};
enum { nonce18 = 0xfaf5fcf8f30e57a9ULL };

static const u8 input19[] __initconst = {
	0x1c, 0x4a, 0x30, 0x26, 0xef, 0x9a, 0x32, 0xa7,
	0x8f, 0xe5, 0xc0, 0x0f, 0x30, 0x3a, 0xbf, 0x38,
	0x54, 0xba
};
static const u8 output19[] __initconst = {
	0x57, 0x67, 0x54, 0x4f, 0x31, 0xd6, 0xef, 0x35,
	0x0b, 0xd9, 0x52, 0xa7, 0x46, 0x7d, 0x12, 0x17,
	0x1e, 0xe3
};
static const u8 key19[] __initconst = {
	0x5a, 0x79, 0xc1, 0xea, 0x33, 0xb3, 0xc7, 0x21,
	0xec, 0xf8, 0xcb, 0xd2, 0x58, 0x96, 0x23, 0xd6,
	0x4d, 0xed, 0x2f, 0xdf, 0x8a, 0x79, 0xe6, 0x8b,
	0x38, 0xa3, 0xc3, 0x7a, 0x33, 0xda, 0x02, 0xc7
};
enum { nonce19 = 0x2b23b61840429604ULL };

static const u8 input20[] __initconst = {
	0xab, 0xe9, 0x32, 0xbb, 0x35, 0x17, 0xe0, 0x60,
	0x80, 0xb1, 0x27, 0xdc, 0xe6, 0x62, 0x9e, 0x0c,
	0x77, 0xf4, 0x50
};
static const u8 output20[] __initconst = {
	0x54, 0x6d, 0xaa, 0xfc, 0x08, 0xfb, 0x71, 0xa8,
	0xd6, 0x1d, 0x7d, 0xf3, 0x45, 0x10, 0xb5, 0x4c,
	0xcc, 0x4b, 0x45
};
static const u8 key20[] __initconst = {
	0xa3, 0xfd, 0x3d, 0xa9, 0xeb, 0xea, 0x2c, 0x69,
	0xcf, 0x59, 0x38, 0x13, 0x5b, 0xa7, 0x53, 0x8f,
	0x5e, 0xa2, 0x33, 0x86, 0x4c, 0x75, 0x26, 0xaf,
	0x35, 0x12, 0x09, 0x71, 0x81, 0xea, 0x88, 0x66
};
enum { nonce20 = 0x7459667a8fadff58ULL };

static const u8 input21[] __initconst = {
	0xa6, 0x82, 0x21, 0x23, 0xad, 0x27, 0x3f, 0xc6,
	0xd7, 0x16, 0x0d, 0x6d, 0x24, 0x15, 0x54, 0xc5,
	0x96, 0x72, 0x59, 0x8a
};
static const u8 output21[] __initconst = {
	0x5f, 0x34, 0x32, 0xea, 0x06, 0xd4, 0x9e, 0x01,
	0xdc, 0x32, 0x32, 0x40, 0x66, 0x73, 0x6d, 0x4a,
	0x6b, 0x12, 0x20, 0xe8
};
static const u8 key21[] __initconst = {
	0x96, 0xfd, 0x13, 0x23, 0xa9, 0x89, 0x04, 0xe6,
	0x31, 0xa5, 0x2c, 0xc1, 0x40, 0xd5, 0x69, 0x5c,
	0x32, 0x79, 0x56, 0xe0, 0x29, 0x93, 0x8f, 0xe8,
	0x5f, 0x65, 0x53, 0x7f, 0xc1, 0xe9, 0xaf, 0xaf
};
enum { nonce21 = 0xba8defee9d8e13b5ULL };

static const u8 input22[] __initconst = {
	0xb8, 0x32, 0x1a, 0x81, 0xd8, 0x38, 0x89, 0x5a,
	0xb0, 0x05, 0xbe, 0xf4, 0xd2, 0x08, 0xc6, 0xee,
	0x79, 0x7b, 0x3a, 0x76, 0x59
};
static const u8 output22[] __initconst = {
	0xb7, 0xba, 0xae, 0x80, 0xe4, 0x9f, 0x79, 0x84,
	0x5a, 0x48, 0x50, 0x6d, 0xcb, 0xd0, 0x06, 0x0c,
	0x15, 0x63, 0xa7, 0x5e, 0xbd
};
static const u8 key22[] __initconst = {
	0x0f, 0x35, 0x3d, 0xeb, 0x5f, 0x0a, 0x82, 0x0d,
	0x24, 0x59, 0x71, 0xd8, 0xe6, 0x2d, 0x5f, 0xe1,
	0x7e, 0x0c, 0xae, 0xf6, 0xdc, 0x2c, 0xc5, 0x4a,
	0x38, 0x88, 0xf2, 0xde, 0xd9, 0x5f, 0x76, 0x7c
};
enum { nonce22 = 0xe77f1760e9f5e192ULL };

static const u8 input23[] __initconst = {
	0x4b, 0x1e, 0x79, 0x99, 0xcf, 0xef, 0x64, 0x4b,
	0xb0, 0x66, 0xae, 0x99, 0x2e, 0x68, 0x97, 0xf5,
	0x5d, 0x9b, 0x3f, 0x7a, 0xa9, 0xd9
};
static const u8 output23[] __initconst = {
	0x5f, 0xa4, 0x08, 0x39, 0xca, 0xfa, 0x2b, 0x83,
	0x5d, 0x95, 0x70, 0x7c, 0x2e, 0xd4, 0xae, 0xfa,
	0x45, 0x4a, 0x77, 0x7f, 0xa7, 0x65
};
static const u8 key23[] __initconst = {
	0x4a, 0x06, 0x83, 0x64, 0xaa, 0xe3, 0x38, 0x32,
	0x28, 0x5d, 0xa4, 0xb2, 0x5a, 0xee, 0xcf, 0x8e,
	0x19, 0x67, 0xf1, 0x09, 0xe8, 0xc9, 0xf6, 0x40,
	0x02, 0x6d, 0x0b, 0xde, 0xfa, 0x81, 0x03, 0xb1
};
enum { nonce23 = 0x9b3f349158709849ULL };

static const u8 input24[] __initconst = {
	0xc6, 0xfc, 0x47, 0x5e, 0xd8, 0xed, 0xa9, 0xe5,
	0x4f, 0x82, 0x79, 0x35, 0xee, 0x3e, 0x7e, 0x3e,
	0x35, 0x70, 0x6e, 0xfa, 0x6d, 0x08, 0xe8
};
static const u8 output24[] __initconst = {
	0x3b, 0xc5, 0xf8, 0xc2, 0xbf, 0x2b, 0x90, 0x33,
	0xa6, 0xae, 0xf5, 0x5a, 0x65, 0xb3, 0x3d, 0xe1,
	0xcd, 0x5f, 0x55, 0xfa, 0xe7, 0xa5, 0x4a
};
static const u8 key24[] __initconst = {
	0x00, 0x24, 0xc3, 0x65, 0x5f, 0xe6, 0x31, 0xbb,
	0x6d, 0xfc, 0x20, 0x7b, 0x1b, 0xa8, 0x96, 0x26,
	0x55, 0x21, 0x62, 0x25, 0x7e, 0xba, 0x23, 0x97,
	0xc9, 0xb8, 0x53, 0xa8, 0xef, 0xab, 0xad, 0x61
};
enum { nonce24 = 0x13ee0b8f526177c3ULL };

static const u8 input25[] __initconst = {
	0x33, 0x07, 0x16, 0xb1, 0x34, 0x33, 0x67, 0x04,
	0x9b, 0x0a, 0xce, 0x1b, 0xe9, 0xde, 0x1a, 0xec,
	0xd0, 0x55, 0xfb, 0xc6, 0x33, 0xaf, 0x2d, 0xe3
};
static const u8 output25[] __initconst = {
	0x05, 0x93, 0x10, 0xd1, 0x58, 0x6f, 0x68, 0x62,
	0x45, 0xdb, 0x91, 0xae, 0x70, 0xcf, 0xd4, 0x5f,
	0xee, 0xdf, 0xd5, 0xba, 0x9e, 0xde, 0x68, 0xe6
};
static const u8 key25[] __initconst = {
	0x83, 0xa9, 0x4f, 0x5d, 0x74, 0xd5, 0x91, 0xb3,
	0xc9, 0x97, 0x19, 0x15, 0xdb, 0x0d, 0x0b, 0x4a,
	0x3d, 0x55, 0xcf, 0xab, 0xb2, 0x05, 0x21, 0x35,
	0x45, 0x50, 0xeb, 0xf8, 0xf5, 0xbf, 0x36, 0x35
};
enum { nonce25 = 0x7c6f459e49ebfebcULL };

static const u8 input26[] __initconst = {
	0xc2, 0xd4, 0x7a, 0xa3, 0x92, 0xe1, 0xac, 0x46,
	0x1a, 0x15, 0x38, 0xc9, 0xb5, 0xfd, 0xdf, 0x84,
	0x38, 0xbc, 0x6b, 0x1d, 0xb0, 0x83, 0x43, 0x04,
	0x39
};
static const u8 output26[] __initconst = {
	0x7f, 0xde, 0xd6, 0x87, 0xcc, 0x34, 0xf4, 0x12,
	0xae, 0x55, 0xa5, 0x89, 0x95, 0x29, 0xfc, 0x18,
	0xd8, 0xc7, 0x7c, 0xd3, 0xcb, 0x85, 0x95, 0x21,
	0xd2
};
static const u8 key26[] __initconst = {
	0xe4, 0xd0, 0x54, 0x1d, 0x7d, 0x47, 0xa8, 0xc1,
	0x08, 0xca, 0xe2, 0x42, 0x52, 0x95, 0x16, 0x43,
	0xa3, 0x01, 0x23, 0x03, 0xcc, 0x3b, 0x81, 0x78,
	0x23, 0xcc, 0xa7, 0x36, 0xd7, 0xa0, 0x97, 0x8d
};
enum { nonce26 = 0x524401012231683ULL };

static const u8 input27[] __initconst = {
	0x0d, 0xb0, 0xcf, 0xec, 0xfc, 0x38, 0x9d, 0x9d,
	0x89, 0x00, 0x96, 0xf2, 0x79, 0x8a, 0xa1, 0x8d,
	0x32, 0x5e, 0xc6, 0x12, 0x22, 0xec, 0xf6, 0x52,
	0xc1, 0x0b
};
static const u8 output27[] __initconst = {
	0xef, 0xe1, 0xf2, 0x67, 0x8e, 0x2c, 0x00, 0x9f,
	0x1d, 0x4c, 0x66, 0x1f, 0x94, 0x58, 0xdc, 0xbb,
	0xb9, 0x11, 0x8f, 0x74, 0xfd, 0x0e, 0x14, 0x01,
	0xa8, 0x21
};
static const u8 key27[] __initconst = {
	0x78, 0x71, 0xa4, 0xe6, 0xb2, 0x95, 0x44, 0x12,
	0x81, 0xaa, 0x7e, 0x94, 0xa7, 0x8d, 0x44, 0xea,
	0xc4, 0xbc, 0x01, 0xb7, 0x9e, 0xf7, 0x82, 0x9e,
	0x3b, 0x23, 0x9f, 0x31, 0xdd, 0xb8, 0x0d, 0x18
};
enum { nonce27 = 0xd58fe0e58fb254d6ULL };

static const u8 input28[] __initconst = {
	0xaa, 0xb7, 0xaa, 0xd9, 0xa8, 0x91, 0xd7, 0x8a,
	0x97, 0x9b, 0xdb, 0x7c, 0x47, 0x2b, 0xdb, 0xd2,
	0xda, 0x77, 0xb1, 0xfa, 0x2d, 0x12, 0xe3, 0xe9,
	0xc4, 0x7f, 0x54
};
static const u8 output28[] __initconst = {
	0x87, 0x84, 0xa9, 0xa6, 0xad, 0x8f, 0xe6, 0x0f,
	0x69, 0xf8, 0x21, 0xc3, 0x54, 0x95, 0x0f, 0xb0,
	0x4e, 0xc7, 0x02, 0xe4, 0x04, 0xb0, 0x6c, 0x42,
	0x8c, 0x63, 0xe3
};
static const u8 key28[] __initconst = {
	0x12, 0x23, 0x37, 0x95, 0x04, 0xb4, 0x21, 0xe8,
	0xbc, 0x65, 0x46, 0x7a, 0xf4, 0x01, 0x05, 0x3f,
	0xb1, 0x34, 0x73, 0xd2, 0x49, 0xbf, 0x6f, 0x20,
	0xbd, 0x23, 0x58, 0x5f, 0xd1, 0x73, 0x57, 0xa6
};
enum { nonce28 = 0x3a04d51491eb4e07ULL };

static const u8 input29[] __initconst = {
	0x55, 0xd0, 0xd4, 0x4b, 0x17, 0xc8, 0xc4, 0x2b,
	0xc0, 0x28, 0xbd, 0x9d, 0x65, 0x4d, 0xaf, 0x77,
	0x72, 0x7c, 0x36, 0x68, 0xa7, 0xb6, 0x87, 0x4d,
	0xb9, 0x27, 0x25, 0x6c
};
static const u8 output29[] __initconst = {
	0x0e, 0xac, 0x4c, 0xf5, 0x12, 0xb5, 0x56, 0xa5,
	0x00, 0x9a, 0xd6, 0xe5, 0x1a, 0x59, 0x2c, 0xf6,
	0x42, 0x22, 0xcf, 0x23, 0x98, 0x34, 0x29, 0xac,
	0x6e, 0xe3, 0x37, 0x6d
};
static const u8 key29[] __initconst = {
	0xda, 0x9d, 0x05, 0x0c, 0x0c, 0xba, 0x75, 0xb9,
	0x9e, 0xb1, 0x8d, 0xd9, 0x73, 0x26, 0x2c, 0xa9,
	0x3a, 0xb5, 0xcb, 0x19, 0x49, 0xa7, 0x4f, 0xf7,
	0x64, 0x35, 0x23, 0x20, 0x2a, 0x45, 0x78, 0xc7
};
enum { nonce29 = 0xc25ac9982431cbfULL };

static const u8 input30[] __initconst = {
	0x4e, 0xd6, 0x85, 0xbb, 0xe7, 0x99, 0xfa, 0x04,
	0x33, 0x24, 0xfd, 0x75, 0x18, 0xe3, 0xd3, 0x25,
	0xcd, 0xca, 0xae, 0x00, 0xbe, 0x52, 0x56, 0x4a,
	0x31, 0xe9, 0x4f, 0xae, 0x8a
};
static const u8 output30[] __initconst = {
	0x30, 0x36, 0x32, 0xa2, 0x3c, 0xb6, 0xf9, 0xf9,
	0x76, 0x70, 0xad, 0xa6, 0x10, 0x41, 0x00, 0x4a,
	0xfa, 0xce, 0x1b, 0x86, 0x05, 0xdb, 0x77, 0x96,
	0xb3, 0xb7, 0x8f, 0x61, 0x24
};
static const u8 key30[] __initconst = {
	0x49, 0x35, 0x4c, 0x15, 0x98, 0xfb, 0xc6, 0x57,
	0x62, 0x6d, 0x06, 0xc3, 0xd4, 0x79, 0x20, 0x96,
	0x05, 0x2a, 0x31, 0x63, 0xc0, 0x44, 0x42, 0x09,
	0x13, 0x13, 0xff, 0x1b, 0xc8, 0x63, 0x1f, 0x0b
};
enum { nonce30 = 0x4967f9c08e41568bULL };

static const u8 input31[] __initconst = {
	0x91, 0x04, 0x20, 0x47, 0x59, 0xee, 0xa6, 0x0f,
	0x04, 0x75, 0xc8, 0x18, 0x95, 0x44, 0x01, 0x28,
	0x20, 0x6f, 0x73, 0x68, 0x66, 0xb5, 0x03, 0xb3,
	0x58, 0x27, 0x6e, 0x7a, 0x76, 0xb8
};
static const u8 output31[] __initconst = {
	0xe8, 0x03, 0x78, 0x9d, 0x13, 0x15, 0x98, 0xef,
	0x64, 0x68, 0x12, 0x41, 0xb0, 0x29, 0x94, 0x0c,
	0x83, 0x35, 0x46, 0xa9, 0x74, 0xe1, 0x75, 0xf0,
	0xb6, 0x96, 0xc3, 0x6f, 0xd7, 0x70
};
static const u8 key31[] __initconst = {
	0xef, 0xcd, 0x5a, 0x4a, 0xf4, 0x7e, 0x6a, 0x3a,
	0x11, 0x88, 0x72, 0x94, 0xb8, 0xae, 0x84, 0xc3,
	0x66, 0xe0, 0xde, 0x4b, 0x00, 0xa5, 0xd6, 0x2d,
	0x50, 0xb7, 0x28, 0xff, 0x76, 0x57, 0x18, 0x1f
};
enum { nonce31 = 0xcb6f428fa4192e19ULL };

static const u8 input32[] __initconst = {
	0x90, 0x06, 0x50, 0x4b, 0x98, 0x14, 0x30, 0xf1,
	0xb8, 0xd7, 0xf0, 0xa4, 0x3e, 0x4e, 0xd8, 0x00,
	0xea, 0xdb, 0x4f, 0x93, 0x05, 0xef, 0x02, 0x71,
	0x1a, 0xcd, 0xa3, 0xb1, 0xae, 0xd3, 0x18
};
static const u8 output32[] __initconst = {
	0xcb, 0x4a, 0x37, 0x3f, 0xea, 0x40, 0xab, 0x86,
	0xfe, 0xcc, 0x07, 0xd5, 0xdc, 0xb2, 0x25, 0xb6,
	0xfd, 0x2a, 0x72, 0xbc, 0x5e, 0xd4, 0x75, 0xff,
	0x71, 0xfc, 0xce, 0x1e, 0x6f, 0x22, 0xc1
};
static const u8 key32[] __initconst = {
	0xfc, 0x6d, 0xc3, 0x80, 0xce, 0xa4, 0x31, 0xa1,
	0xcc, 0xfa, 0x9d, 0x10, 0x0b, 0xc9, 0x11, 0x77,
	0x34, 0xdb, 0xad, 0x1b, 0xc4, 0xfc, 0xeb, 0x79,
	0x91, 0xda, 0x59, 0x3b, 0x0d, 0xb1, 0x19, 0x3b
};
enum { nonce32 = 0x88551bf050059467ULL };

static const u8 input33[] __initconst = {
	0x88, 0x94, 0x71, 0x92, 0xe8, 0xd7, 0xf9, 0xbd,
	0x55, 0xe3, 0x22, 0xdb, 0x99, 0x51, 0xfb, 0x50,
	0xbf, 0x82, 0xb5, 0x70, 0x8b, 0x2b, 0x6a, 0x03,
	0x37, 0xa0, 0xc6, 0x19, 0x5d, 0xc9, 0xbc, 0xcc
};
static const u8 output33[] __initconst = {
	0xb6, 0x17, 0x51, 0xc8, 0xea, 0x8a, 0x14, 0xdc,
	0x23, 0x1b, 0xd4, 0xed, 0xbf, 0x50, 0xb9, 0x38,
	0x00, 0xc2, 0x3f, 0x78, 0x3d, 0xbf, 0xa0, 0x84,
	0xef, 0x45, 0xb2, 0x7d, 0x48, 0x7b, 0x62, 0xa7
};
static const u8 key33[] __initconst = {
	0xb9, 0x8f, 0x6a, 0xad, 0xb4, 0x6f, 0xb5, 0xdc,
	0x48, 0xfa, 0x43, 0x57, 0x62, 0x97, 0xef, 0x89,
	0x4c, 0x5a, 0x7b, 0x67, 0xb8, 0x9d, 0xf0, 0x42,
	0x2b, 0x8f, 0xf3, 0x18, 0x05, 0x2e, 0x48, 0xd0
};
enum { nonce33 = 0x31f16488fe8447f5ULL };

static const u8 input34[] __initconst = {
	0xda, 0x2b, 0x3d, 0x63, 0x9e, 0x4f, 0xc2, 0xb8,
	0x7f, 0xc2, 0x1a, 0x8b, 0x0d, 0x95, 0x65, 0x55,
	0x52, 0xba, 0x51, 0x51, 0xc0, 0x61, 0x9f, 0x0a,
	0x5d, 0xb0, 0x59, 0x8c, 0x64, 0x6a, 0xab, 0xf5,
	0x57
};
static const u8 output34[] __initconst = {
	0x5c, 0xf6, 0x62, 0x24, 0x8c, 0x45, 0xa3, 0x26,
	0xd0, 0xe4, 0x88, 0x1c, 0xed, 0xc4, 0x26, 0x58,
	0xb5, 0x5d, 0x92, 0xc4, 0x17, 0x44, 0x1c, 0xb8,
	0x2c, 0xf3, 0x55, 0x7e, 0xd6, 0xe5, 0xb3, 0x65,
	0xa8
};
static const u8 key34[] __initconst = {
	0xde, 0xd1, 0x27, 0xb7, 0x7c, 0xfa, 0xa6, 0x78,
	0x39, 0x80, 0xdf, 0xb7, 0x46, 0xac, 0x71, 0x26,
	0xd0, 0x2a, 0x56, 0x79, 0x12, 0xeb, 0x26, 0x37,
	0x01, 0x0d, 0x30, 0xe0, 0xe3, 0x66, 0xb2, 0xf4
};
enum { nonce34 = 0x92d0d9b252c24149ULL };

static const u8 input35[] __initconst = {
	0x3a, 0x15, 0x5b, 0x75, 0x6e, 0xd0, 0x52, 0x20,
	0x6c, 0x82, 0xfa, 0xce, 0x5b, 0xea, 0xf5, 0x43,
	0xc1, 0x81, 0x7c, 0xb2, 0xac, 0x16, 0x3f, 0xd3,
	0x5a, 0xaf, 0x55, 0x98, 0xf4, 0xc6, 0xba, 0x71,
	0x25, 0x8b
};
static const u8 output35[] __initconst = {
	0xb3, 0xaf, 0xac, 0x6d, 0x4d, 0xc7, 0x68, 0x56,
	0x50, 0x5b, 0x69, 0x2a, 0xe5, 0x90, 0xf9, 0x5f,
	0x99, 0x88, 0xff, 0x0c, 0xa6, 0xb1, 0x83, 0xd6,
	0x80, 0xa6, 0x1b, 0xde, 0x94, 0xa4, 0x2c, 0xc3,
	0x74, 0xfa
};
static const u8 key35[] __initconst = {
	0xd8, 0x24, 0xe2, 0x06, 0xd7, 0x7a, 0xce, 0x81,
	0x52, 0x72, 0x02, 0x69, 0x89, 0xc4, 0xe9, 0x53,
	0x3b, 0x08, 0x5f, 0x98, 0x1e, 0x1b, 0x99, 0x6e,
	0x28, 0x17, 0x6d, 0xba, 0xc0, 0x96, 0xf9, 0x3c
};
enum { nonce35 = 0x7baf968c4c8e3a37ULL };

static const u8 input36[] __initconst = {
	0x31, 0x5d, 0x4f, 0xe3, 0xac, 0xad, 0x17, 0xa6,
	0xb5, 0x01, 0xe2, 0xc6, 0xd4, 0x7e, 0xc4, 0x80,
	0xc0, 0x59, 0x72, 0xbb, 0x4b, 0x74, 0x6a, 0x41,
	0x0f, 0x9c, 0xf6, 0xca, 0x20, 0xb3, 0x73, 0x07,
	0x6b, 0x02, 0x2a
};
static const u8 output36[] __initconst = {
	0xf9, 0x09, 0x92, 0x94, 0x7e, 0x31, 0xf7, 0x53,
	0xe8, 0x8a, 0x5b, 0x20, 0xef, 0x9b, 0x45, 0x81,
	0xba, 0x5e, 0x45, 0x63, 0xc1, 0xc7, 0x9e, 0x06,
	0x0e, 0xd9, 0x62, 0x8e, 0x96, 0xf9, 0xfa, 0x43,
	0x4d, 0xd4, 0x28
};
static const u8 key36[] __initconst = {
	0x13, 0x30, 0x4c, 0x06, 0xae, 0x18, 0xde, 0x03,
	0x1d, 0x02, 0x40, 0xf5, 0xbb, 0x19, 0xe3, 0x88,
	0x41, 0xb1, 0x29, 0x15, 0x97, 0xc2, 0x69, 0x3f,
	0x32, 0x2a, 0x0c, 0x8b, 0xcf, 0x83, 0x8b, 0x6c
};
enum { nonce36 = 0x226d251d475075a0ULL };

static const u8 input37[] __initconst = {
	0x10, 0x18, 0xbe, 0xfd, 0x66, 0xc9, 0x77, 0xcc,
	0x43, 0xe5, 0x46, 0x0b, 0x08, 0x8b, 0xae, 0x11,
	0x86, 0x15, 0xc2, 0xf6, 0x45, 0xd4, 0x5f, 0xd6,
	0xb6, 0x5f, 0x9f, 0x3e, 0x97, 0xb7, 0xd4, 0xad,
	0x0b, 0xe8, 0x31, 0x94
};
static const u8 output37[] __initconst = {
	0x03, 0x2c, 0x1c, 0xee, 0xc6, 0xdd, 0xed, 0x38,
	0x80, 0x6d, 0x84, 0x16, 0xc3, 0xc2, 0x04, 0x63,
	0xcd, 0xa7, 0x6e, 0x36, 0x8b, 0xed, 0x78, 0x63,
	0x95, 0xfc, 0x69, 0x7a, 0x3f, 0x8d, 0x75, 0x6b,
	0x6c, 0x26, 0x56, 0x4d
};
static const u8 key37[] __initconst = {
	0xac, 0x84, 0x4d, 0xa9, 0x29, 0x49, 0x3c, 0x39,
	0x7f, 0xd9, 0xa6, 0x01, 0xf3, 0x7e, 0xfa, 0x4a,
	0x14, 0x80, 0x22, 0x74, 0xf0, 0x29, 0x30, 0x2d,
	0x07, 0x21, 0xda, 0xc0, 0x4d, 0x70, 0x56, 0xa2
};
enum { nonce37 = 0x167823ce3b64925aULL };

static const u8 input38[] __initconst = {
	0x30, 0x8f, 0xfa, 0x24, 0x29, 0xb1, 0xfb, 0xce,
	0x31, 0x62, 0xdc, 0xd0, 0x46, 0xab, 0xe1, 0x31,
	0xd9, 0xae, 0x60, 0x0d, 0xca, 0x0a, 0x49, 0x12,
	0x3d, 0x92, 0xe9, 0x91, 0x67, 0x12, 0x62, 0x18,
	0x89, 0xe2, 0xf9, 0x1c, 0xcc
};
static const u8 output38[] __initconst = {
	0x56, 0x9c, 0xc8, 0x7a, 0xc5, 0x98, 0xa3, 0x0f,
	0xba, 0xd5, 0x3e, 0xe1, 0xc9, 0x33, 0x64, 0x33,
	0xf0, 0xd5, 0xf7, 0x43, 0x66, 0x0e, 0x08, 0x9a,
	0x6e, 0x09, 0xe4, 0x01, 0x0d, 0x1e, 0x2f, 0x4b,
	0xed, 0x9c, 0x08, 0x8c, 0x03
};
static const u8 key38[] __initconst = {
	0x77, 0x52, 0x2a, 0x23, 0xf1, 0xc5, 0x96, 0x2b,
	0x89, 0x4f, 0x3e, 0xf3, 0xff, 0x0e, 0x94, 0xce,
	0xf1, 0xbd, 0x53, 0xf5, 0x77, 0xd6, 0x9e, 0x47,
	0x49, 0x3d, 0x16, 0x64, 0xff, 0x95, 0x42, 0x42
};
enum { nonce38 = 0xff629d7b82cef357ULL };

static const u8 input39[] __initconst = {
	0x38, 0x26, 0x27, 0xd0, 0xc2, 0xf5, 0x34, 0xba,
	0xda, 0x0f, 0x1c, 0x1c, 0x9a, 0x70, 0xe5, 0x8a,
	0x78, 0x2d, 0x8f, 0x9a, 0xbf, 0x89, 0x6a, 0xfd,
	0xd4, 0x9c, 0x33, 0xf1, 0xb6, 0x89, 0x16, 0xe3,
	0x6a, 0x00, 0xfa, 0x3a, 0x0f, 0x26
};
static const u8 output39[] __initconst = {
	0x0f, 0xaf, 0x91, 0x6d, 0x9c, 0x99, 0xa4, 0xf7,
	0x3b, 0x9d, 0x9a, 0x98, 0xca, 0xbb, 0x50, 0x48,
	0xee, 0xcb, 0x5d, 0xa1, 0x37, 0x2d, 0x36, 0x09,
	0x2a, 0xe2, 0x1c, 0x3d, 0x98, 0x40, 0x1c, 0x16,
	0x56, 0xa7, 0x98, 0xe9, 0x7d, 0x2b
};
static const u8 key39[] __initconst = {
	0x6e, 0x83, 0x15, 0x4d, 0xf8, 0x78, 0xa8, 0x0e,
	0x71, 0x37, 0xd4, 0x6e, 0x28, 0x5c, 0x06, 0xa1,
	0x2d, 0x6c, 0x72, 0x7a, 0xfd, 0xf8, 0x65, 0x1a,
	0xb8, 0xe6, 0x29, 0x7b, 0xe5, 0xb3, 0x23, 0x79
};
enum { nonce39 = 0xa4d8c491cf093e9dULL };

static const u8 input40[] __initconst = {
	0x8f, 0x32, 0x7c, 0x40, 0x37, 0x95, 0x08, 0x00,
	0x00, 0xfe, 0x2f, 0x95, 0x20, 0x12, 0x40, 0x18,
	0x5e, 0x7e, 0x5e, 0x99, 0xee, 0x8d, 0x91, 0x7d,
	0x50, 0x7d, 0x21, 0x45, 0x27, 0xe1, 0x7f, 0xd4,
	0x73, 0x10, 0xe1, 0x33, 0xbc, 0xf8, 0xdd
};
static const u8 output40[] __initconst = {
	0x78, 0x7c, 0xdc, 0x55, 0x2b, 0xd9, 0x2b, 0x3a,
	0xdd, 0x56, 0x11, 0x52, 0xd3, 0x2e, 0xe0, 0x0d,
	0x23, 0x20, 0x8a, 0xf1, 0x4f, 0xee, 0xf1, 0x68,
	0xf6, 0xdc, 0x53, 0xcf, 0x17, 0xd4, 0xf0, 0x6c,
	0xdc, 0x80, 0x5f, 0x1c, 0xa4, 0x91, 0x05
};
static const u8 key40[] __initconst = {
	0x0d, 0x86, 0xbf, 0x8a, 0xba, 0x9e, 0x39, 0x91,
	0xa8, 0xe7, 0x22, 0xf0, 0x0c, 0x43, 0x18, 0xe4,
	0x1f, 0xb0, 0xaf, 0x8a, 0x34, 0x31, 0xf4, 0x41,
	0xf0, 0x89, 0x85, 0xca, 0x5d, 0x05, 0x3b, 0x94
};
enum { nonce40 = 0xae7acc4f5986439eULL };

static const u8 input41[] __initconst = {
	0x20, 0x5f, 0xc1, 0x83, 0x36, 0x02, 0x76, 0x96,
	0xf0, 0xbf, 0x8e, 0x0e, 0x1a, 0xd1, 0xc7, 0x88,
	0x18, 0xc7, 0x09, 0xc4, 0x15, 0xd9, 0x4f, 0x5e,
	0x1f, 0xb3, 0xb4, 0x6d, 0xcb, 0xa0, 0xd6, 0x8a,
	0x3b, 0x40, 0x8e, 0x80, 0xf1, 0xe8, 0x8f, 0x5f
};
static const u8 output41[] __initconst = {
	0x0b, 0xd1, 0x49, 0x9a, 0x9d, 0xe8, 0x97, 0xb8,
	0xd1, 0xeb, 0x90, 0x62, 0x37, 0xd2, 0x99, 0x15,
	0x67, 0x6d, 0x27, 0x93, 0xce, 0x37, 0x65, 0xa2,
	0x94, 0x88, 0xd6, 0x17, 0xbc, 0x1c, 0x6e, 0xa2,
	0xcc, 0xfb, 0x81, 0x0e, 0x30, 0x60, 0x5a, 0x6f
};
static const u8 key41[] __initconst = {
	0x36, 0x27, 0x57, 0x01, 0x21, 0x68, 0x97, 0xc7,
	0x00, 0x67, 0x7b, 0xe9, 0x0f, 0x55, 0x49, 0xbb,
	0x92, 0x18, 0x98, 0xf5, 0x5e, 0xbc, 0xe7, 0x5a,
	0x9d, 0x3d, 0xc7, 0xbd, 0x59, 0xec, 0x82, 0x8e
};
enum { nonce41 = 0x5da05e4c8dfab464ULL };

static const u8 input42[] __initconst = {
	0xca, 0x30, 0xcd, 0x63, 0xf0, 0x2d, 0xf1, 0x03,
	0x4d, 0x0d, 0xf2, 0xf7, 0x6f, 0xae, 0xd6, 0x34,
	0xea, 0xf6, 0x13, 0xcf, 0x1c, 0xa0, 0xd0, 0xe8,
	0xa4, 0x78, 0x80, 0x3b, 0x1e, 0xa5, 0x32, 0x4c,
	0x73, 0x12, 0xd4, 0x6a, 0x94, 0xbc, 0xba, 0x80,
	0x5e
};
static const u8 output42[] __initconst = {
	0xec, 0x3f, 0x18, 0x31, 0xc0, 0x7b, 0xb5, 0xe2,
	0xad, 0xf3, 0xec, 0xa0, 0x16, 0x9d, 0xef, 0xce,
	0x05, 0x65, 0x59, 0x9d, 0x5a, 0xca, 0x3e, 0x13,
	0xb9, 0x5d, 0x5d, 0xb5, 0xeb, 0xae, 0xc0, 0x87,
	0xbb, 0xfd, 0xe7, 0xe4, 0x89, 0x5b, 0xd2, 0x6c,
	0x56
};
static const u8 key42[] __initconst = {
	0x7c, 0x6b, 0x7e, 0x77, 0xcc, 0x8c, 0x1b, 0x03,
	0x8b, 0x2a, 0xb3, 0x7c, 0x5a, 0x73, 0xcc, 0xac,
	0xdd, 0x53, 0x54, 0x0c, 0x85, 0xed, 0xcd, 0x47,
	0x24, 0xc1, 0xb8, 0x9b, 0x2e, 0x41, 0x92, 0x36
};
enum { nonce42 = 0xe4d7348b09682c9cULL };

static const u8 input43[] __initconst = {
	0x52, 0xf2, 0x4b, 0x7c, 0xe5, 0x58, 0xe8, 0xd2,
	0xb7, 0xf3, 0xa1, 0x29, 0x68, 0xa2, 0x50, 0x50,
	0xae, 0x9c, 0x1b, 0xe2, 0x67, 0x77, 0xe2, 0xdb,
	0x85, 0x55, 0x7e, 0x84, 0x8a, 0x12, 0x3c, 0xb6,
	0x2e, 0xed, 0xd3, 0xec, 0x47, 0x68, 0xfa, 0x52,
	0x46, 0x9d
};
static const u8 output43[] __initconst = {
	0x1b, 0xf0, 0x05, 0xe4, 0x1c, 0xd8, 0x74, 0x9a,
	0xf0, 0xee, 0x00, 0x54, 0xce, 0x02, 0x83, 0x15,
	0xfb, 0x23, 0x35, 0x78, 0xc3, 0xda, 0x98, 0xd8,
	0x9d, 0x1b, 0xb2, 0x51, 0x82, 0xb0, 0xff, 0xbe,
	0x05, 0xa9, 0xa4, 0x04, 0xba, 0xea, 0x4b, 0x73,
	0x47, 0x6e
};
static const u8 key43[] __initconst = {
	0xeb, 0xec, 0x0e, 0xa1, 0x65, 0xe2, 0x99, 0x46,
	0xd8, 0x54, 0x8c, 0x4a, 0x93, 0xdf, 0x6d, 0xbf,
	0x93, 0x34, 0x94, 0x57, 0xc9, 0x12, 0x9d, 0x68,
	0x05, 0xc5, 0x05, 0xad, 0x5a, 0xc9, 0x2a, 0x3b
};
enum { nonce43 = 0xe14f6a902b7827fULL };

static const u8 input44[] __initconst = {
	0x3e, 0x22, 0x3e, 0x8e, 0xcd, 0x18, 0xe2, 0xa3,
	0x8d, 0x8b, 0x38, 0xc3, 0x02, 0xa3, 0x31, 0x48,
	0xc6, 0x0e, 0xec, 0x99, 0x51, 0x11, 0x6d, 0x8b,
	0x32, 0x35, 0x3b, 0x08, 0x58, 0x76, 0x25, 0x30,
	0xe2, 0xfc, 0xa2, 0x46, 0x7d, 0x6e, 0x34, 0x87,
	0xac, 0x42, 0xbf
};
static const u8 output44[] __initconst = {
	0x08, 0x92, 0x58, 0x02, 0x1a, 0xf4, 0x1f, 0x3d,
	0x38, 0x7b, 0x6b, 0xf6, 0x84, 0x07, 0xa3, 0x19,
	0x17, 0x2a, 0xed, 0x57, 0x1c, 0xf9, 0x55, 0x37,
	0x4e, 0xf4, 0x68, 0x68, 0x82, 0x02, 0x4f, 0xca,
	0x21, 0x00, 0xc6, 0x66, 0x79, 0x53, 0x19, 0xef,
	0x7f, 0xdd, 0x74
};
static const u8 key44[] __initconst = {
	0x73, 0xb6, 0x3e, 0xf4, 0x57, 0x52, 0xa6, 0x43,
	0x51, 0xd8, 0x25, 0x00, 0xdb, 0xb4, 0x52, 0x69,
	0xd6, 0x27, 0x49, 0xeb, 0x9b, 0xf1, 0x7b, 0xa0,
	0xd6, 0x7c, 0x9c, 0xd8, 0x95, 0x03, 0x69, 0x26
};
enum { nonce44 = 0xf5e6dc4f35ce24e5ULL };

static const u8 input45[] __initconst = {
	0x55, 0x76, 0xc0, 0xf1, 0x74, 0x03, 0x7a, 0x6d,
	0x14, 0xd8, 0x36, 0x2c, 0x9f, 0x9a, 0x59, 0x7a,
	0x2a, 0xf5, 0x77, 0x84, 0x70, 0x7c, 0x1d, 0x04,
	0x90, 0x45, 0xa4, 0xc1, 0x5e, 0xdd, 0x2e, 0x07,
	0x18, 0x34, 0xa6, 0x85, 0x56, 0x4f, 0x09, 0xaf,
	0x2f, 0x83, 0xe1, 0xc6
};
static const u8 output45[] __initconst = {
	0x22, 0x46, 0xe4, 0x0b, 0x3a, 0x55, 0xcc, 0x9b,
	0xf0, 0xc0, 0x53, 0xcd, 0x95, 0xc7, 0x57, 0x6c,
	0x77, 0x46, 0x41, 0x72, 0x07, 0xbf, 0xa8, 0xe5,
	0x68, 0x69, 0xd8, 0x1e, 0x45, 0xc1, 0xa2, 0x50,
	0xa5, 0xd1, 0x62, 0xc9, 0x5a, 0x7d, 0x08, 0x14,
	0xae, 0x44, 0x16, 0xb9
};
static const u8 key45[] __initconst = {
	0x41, 0xf3, 0x88, 0xb2, 0x51, 0x25, 0x47, 0x02,
	0x39, 0xe8, 0x15, 0x3a, 0x22, 0x78, 0x86, 0x0b,
	0xf9, 0x1e, 0x8d, 0x98, 0xb2, 0x22, 0x82, 0xac,
	0x42, 0x94, 0xde, 0x64, 0xf0, 0xfd, 0xb3, 0x6c
};
enum { nonce45 = 0xf51a582daf4aa01aULL };

static const u8 input46[] __initconst = {
	0xf6, 0xff, 0x20, 0xf9, 0x26, 0x7e, 0x0f, 0xa8,
	0x6a, 0x45, 0x5a, 0x91, 0x73, 0xc4, 0x4c, 0x63,
	0xe5, 0x61, 0x59, 0xca, 0xec, 0xc0, 0x20, 0x35,
	0xbc, 0x9f, 0x58, 0x9c, 0x5e, 0xa1, 0x17, 0x46,
	0xcc, 0xab, 0x6e, 0xd0, 0x4f, 0x24, 0xeb, 0x05,
	0x4d, 0x40, 0x41, 0xe0, 0x9d
};
static const u8 output46[] __initconst = {
	0x31, 0x6e, 0x63, 0x3f, 0x9c, 0xe6, 0xb1, 0xb7,
	0xef, 0x47, 0x46, 0xd7, 0xb1, 0x53, 0x42, 0x2f,
	0x2c, 0xc8, 0x01, 0xae, 0x8b, 0xec, 0x42, 0x2c,
	0x6b, 0x2c, 0x9c, 0xb2, 0xf0, 0x29, 0x06, 0xa5,
	0xcd, 0x7e, 0xc7, 0x3a, 0x38, 0x98, 0x8a, 0xde,
	0x03, 0x29, 0x14, 0x8f, 0xf9
};
static const u8 key46[] __initconst = {
	0xac, 0xa6, 0x44, 0x4a, 0x0d, 0x42, 0x10, 0xbc,
	0xd3, 0xc9, 0x8e, 0x9e, 0x71, 0xa3, 0x1c, 0x14,
	0x9d, 0x65, 0x0d, 0x49, 0x4d, 0x8c, 0xec, 0x46,
	0xe1, 0x41, 0xcd, 0xf5, 0xfc, 0x82, 0x75, 0x34
};
enum { nonce46 = 0x25f85182df84dec5ULL };

static const u8 input47[] __initconst = {
	0xa1, 0xd2, 0xf2, 0x52, 0x2f, 0x79, 0x50, 0xb2,
	0x42, 0x29, 0x5b, 0x44, 0x20, 0xf9, 0xbd, 0x85,
	0xb7, 0x65, 0x77, 0x86, 0xce, 0x3e, 0x1c, 0xe4,
	0x70, 0x80, 0xdd, 0x72, 0x07, 0x48, 0x0f, 0x84,
	0x0d, 0xfd, 0x97, 0xc0, 0xb7, 0x48, 0x9b, 0xb4,
	0xec, 0xff, 0x73, 0x14, 0x99, 0xe4
};
static const u8 output47[] __initconst = {
	0xe5, 0x3c, 0x78, 0x66, 0x31, 0x1e, 0xd6, 0xc4,
	0x9e, 0x71, 0xb3, 0xd7, 0xd5, 0xad, 0x84, 0xf2,
	0x78, 0x61, 0x77, 0xf8, 0x31, 0xf0, 0x13, 0xad,
	0x66, 0xf5, 0x31, 0x7d, 0xeb, 0xdf, 0xaf, 0xcb,
	0xac, 0x28, 0x6c, 0xc2, 0x9e, 0xe7, 0x78, 0xa2,
	0xa2, 0x58, 0xce, 0x84, 0x76, 0x70
};
static const u8 key47[] __initconst = {
	0x05, 0x7f, 0xc0, 0x7f, 0x37, 0x20, 0x71, 0x02,
	0x3a, 0xe7, 0x20, 0x5a, 0x0a, 0x8f, 0x79, 0x5a,
	0xfe, 0xbb, 0x43, 0x4d, 0x2f, 0xcb, 0xf6, 0x9e,
	0xa2, 0x97, 0x00, 0xad, 0x0d, 0x51, 0x7e, 0x17
};
enum { nonce47 = 0xae707c60f54de32bULL };

static const u8 input48[] __initconst = {
	0x80, 0x93, 0x77, 0x2e, 0x8d, 0xe8, 0xe6, 0xc1,
	0x27, 0xe6, 0xf2, 0x89, 0x5b, 0x33, 0x62, 0x18,
	0x80, 0x6e, 0x17, 0x22, 0x8e, 0x83, 0x31, 0x40,
	0x8f, 0xc9, 0x5c, 0x52, 0x6c, 0x0e, 0xa5, 0xe9,
	0x6c, 0x7f, 0xd4, 0x6a, 0x27, 0x56, 0x99, 0xce,
	0x8d, 0x37, 0x59, 0xaf, 0xc0, 0x0e, 0xe1
};
static const u8 output48[] __initconst = {
	0x02, 0xa4, 0x2e, 0x33, 0xb7, 0x7c, 0x2b, 0x9a,
	0x18, 0x5a, 0xba, 0x53, 0x38, 0xaf, 0x00, 0xeb,
	0xd8, 0x3d, 0x02, 0x77, 0x43, 0x45, 0x03, 0x91,
	0xe2, 0x5e, 0x4e, 0xeb, 0x50, 0xd5, 0x5b, 0xe0,
	0xf3, 0x33, 0xa7, 0xa2, 0xac, 0x07, 0x6f, 0xeb,
	0x3f, 0x6c, 0xcd, 0xf2, 0x6c, 0x61, 0x64
};
static const u8 key48[] __initconst = {
	0xf3, 0x79, 0xe7, 0xf8, 0x0e, 0x02, 0x05, 0x6b,
	0x83, 0x1a, 0xe7, 0x86, 0x6b, 0xe6, 0x8f, 0x3f,
	0xd3, 0xa3, 0xe4, 0x6e, 0x29, 0x06, 0xad, 0xbc,
	0xe8, 0x33, 0x56, 0x39, 0xdf, 0xb0, 0xe2, 0xfe
};
enum { nonce48 = 0xd849b938c6569da0ULL };

static const u8 input49[] __initconst = {
	0x89, 0x3b, 0x88, 0x9e, 0x7b, 0x38, 0x16, 0x9f,
	0xa1, 0x28, 0xf6, 0xf5, 0x23, 0x74, 0x28, 0xb0,
	0xdf, 0x6c, 0x9e, 0x8a, 0x71, 0xaf, 0xed, 0x7a,
	0x39, 0x21, 0x57, 0x7d, 0x31, 0x6c, 0xee, 0x0d,
	0x11, 0x8d, 0x41, 0x9a, 0x5f, 0xb7, 0x27, 0x40,
	0x08, 0xad, 0xc6, 0xe0, 0x00, 0x43, 0x9e, 0xae
};
static const u8 output49[] __initconst = {
	0x4d, 0xfd, 0xdb, 0x4c, 0x77, 0xc1, 0x05, 0x07,
	0x4d, 0x6d, 0x32, 0xcb, 0x2e, 0x0e, 0xff, 0x65,
	0xc9, 0x27, 0xeb, 0xa9, 0x46, 0x5b, 0xab, 0x06,
	0xe6, 0xb6, 0x5a, 0x1e, 0x00, 0xfb, 0xcf, 0xe4,
	0xb9, 0x71, 0x40, 0x10, 0xef, 0x12, 0x39, 0xf0,
	0xea, 0x40, 0xb8, 0x9a, 0xa2, 0x85, 0x38, 0x48
};
static const u8 key49[] __initconst = {
	0xe7, 0x10, 0x40, 0xd9, 0x66, 0xc0, 0xa8, 0x6d,
	0xa3, 0xcc, 0x8b, 0xdd, 0x93, 0xf2, 0x6e, 0xe0,
	0x90, 0x7f, 0xd0, 0xf4, 0x37, 0x0c, 0x8b, 0x9b,
	0x4c, 0x4d, 0xe6, 0xf2, 0x1f, 0xe9, 0x95, 0x24
};
enum { nonce49 = 0xf269817bdae01bc0ULL };

static const u8 input50[] __initconst = {
	0xda, 0x5b, 0x60, 0xcd, 0xed, 0x58, 0x8e, 0x7f,
	0xae, 0xdd, 0xc8, 0x2e, 0x16, 0x90, 0xea, 0x4b,
	0x0c, 0x74, 0x14, 0x35, 0xeb, 0xee, 0x2c, 0xff,
	0x46, 0x99, 0x97, 0x6e, 0xae, 0xa7, 0x8e, 0x6e,
	0x38, 0xfe, 0x63, 0xe7, 0x51, 0xd9, 0xaa, 0xce,
	0x7b, 0x1e, 0x7e, 0x5d, 0xc0, 0xe8, 0x10, 0x06,
	0x14
};
static const u8 output50[] __initconst = {
	0xe4, 0xe5, 0x86, 0x1b, 0x66, 0x19, 0xac, 0x49,
	0x1c, 0xbd, 0xee, 0x03, 0xaf, 0x11, 0xfc, 0x1f,
	0x6a, 0xd2, 0x50, 0x5c, 0xea, 0x2c, 0xa5, 0x75,
	0xfd, 0xb7, 0x0e, 0x80, 0x8f, 0xed, 0x3f, 0x31,
	0x47, 0xac, 0x67, 0x43, 0xb8, 0x2e, 0xb4, 0x81,
	0x6d, 0xe4, 0x1e, 0xb7, 0x8b, 0x0c, 0x53, 0xa9,
	0x26
};
static const u8 key50[] __initconst = {
	0xd7, 0xb2, 0x04, 0x76, 0x30, 0xcc, 0x38, 0x45,
	0xef, 0xdb, 0xc5, 0x86, 0x08, 0x61, 0xf0, 0xee,
	0x6d, 0xd8, 0x22, 0x04, 0x8c, 0xfb, 0xcb, 0x37,
	0xa6, 0xfb, 0x95, 0x22, 0xe1, 0x87, 0xb7, 0x6f
};
enum { nonce50 = 0x3b44d09c45607d38ULL };

static const u8 input51[] __initconst = {
	0xa9, 0x41, 0x02, 0x4b, 0xd7, 0xd5, 0xd1, 0xf1,
	0x21, 0x55, 0xb2, 0x75, 0x6d, 0x77, 0x1b, 0x86,
	0xa9, 0xc8, 0x90, 0xfd, 0xed, 0x4a, 0x7b, 0x6c,
	0xb2, 0x5f, 0x9b, 0x5f, 0x16, 0xa1, 0x54, 0xdb,
	0xd6, 0x3f, 0x6a, 0x7f, 0x2e, 0x51, 0x9d, 0x49,
	0x5b, 0xa5, 0x0e, 0xf9, 0xfb, 0x2a, 0x38, 0xff,
	0x20, 0x8c
};
static const u8 output51[] __initconst = {
	0x18, 0xf7, 0x88, 0xc1, 0x72, 0xfd, 0x90, 0x4b,
	0xa9, 0x2d, 0xdb, 0x47, 0xb0, 0xa5, 0xc4, 0x37,
	0x01, 0x95, 0xc4, 0xb1, 0xab, 0xc5, 0x5b, 0xcd,
	0xe1, 0x97, 0x78, 0x13, 0xde, 0x6a, 0xff, 0x36,
	0xce, 0xa4, 0x67, 0xc5, 0x4a, 0x45, 0x2b, 0xd9,
	0xff, 0x8f, 0x06, 0x7c, 0x63, 0xbb, 0x83, 0x17,
	0xb4, 0x6b
};
static const u8 key51[] __initconst = {
	0x82, 0x1a, 0x79, 0xab, 0x9a, 0xb5, 0x49, 0x6a,
	0x30, 0x6b, 0x99, 0x19, 0x11, 0xc7, 0xa2, 0xf4,
	0xca, 0x55, 0xb9, 0xdd, 0xe7, 0x2f, 0xe7, 0xc1,
	0xdd, 0x27, 0xad, 0x80, 0xf2, 0x56, 0xad, 0xf3
};
enum { nonce51 = 0xe93aff94ca71a4a6ULL };

static const u8 input52[] __initconst = {
	0x89, 0xdd, 0xf3, 0xfa, 0xb6, 0xc1, 0xaa, 0x9a,
	0xc8, 0xad, 0x6b, 0x00, 0xa1, 0x65, 0xea, 0x14,
	0x55, 0x54, 0x31, 0x8f, 0xf0, 0x03, 0x84, 0x51,
	0x17, 0x1e, 0x0a, 0x93, 0x6e, 0x79, 0x96, 0xa3,
	0x2a, 0x85, 0x9c, 0x89, 0xf8, 0xd1, 0xe2, 0x15,
	0x95, 0x05, 0xf4, 0x43, 0x4d, 0x6b, 0xf0, 0x71,
	0x3b, 0x3e, 0xba
};
static const u8 output52[] __initconst = {
	0x0c, 0x42, 0x6a, 0xb3, 0x66, 0x63, 0x5d, 0x2c,
	0x9f, 0x3d, 0xa6, 0x6e, 0xc7, 0x5f, 0x79, 0x2f,
	0x50, 0xe3, 0xd6, 0x07, 0x56, 0xa4, 0x2b, 0x2d,
	0x8d, 0x10, 0xc0, 0x6c, 0xa2, 0xfc, 0x97, 0xec,
	0x3f, 0x5c, 0x8d, 0x59, 0xbe, 0x84, 0xf1, 0x3e,
	0x38, 0x47, 0x4f, 0x75, 0x25, 0x66, 0x88, 0x14,
	0x03, 0xdd, 0xde
};
static const u8 key52[] __initconst = {
	0x4f, 0xb0, 0x27, 0xb6, 0xdd, 0x24, 0x0c, 0xdb,
	0x6b, 0x71, 0x2e, 0xac, 0xfc, 0x3f, 0xa6, 0x48,
	0x5d, 0xd5, 0xff, 0x53, 0xb5, 0x62, 0xf1, 0xe0,
	0x93, 0xfe, 0x39, 0x4c, 0x9f, 0x03, 0x11, 0xa7
};
enum { nonce52 = 0xed8becec3bdf6f25ULL };

static const u8 input53[] __initconst = {
	0x68, 0xd1, 0xc7, 0x74, 0x44, 0x1c, 0x84, 0xde,
	0x27, 0x27, 0x35, 0xf0, 0x18, 0x0b, 0x57, 0xaa,
	0xd0, 0x1a, 0xd3, 0x3b, 0x5e, 0x5c, 0x62, 0x93,
	0xd7, 0x6b, 0x84, 0x3b, 0x71, 0x83, 0x77, 0x01,
	0x3e, 0x59, 0x45, 0xf4, 0x77, 0x6c, 0x6b, 0xcb,
	0x88, 0x45, 0x09, 0x1d, 0xc6, 0x45, 0x6e, 0xdc,
	0x6e, 0x51, 0xb8, 0x28
};
static const u8 output53[] __initconst = {
	0xc5, 0x90, 0x96, 0x78, 0x02, 0xf5, 0xc4, 0x3c,
	0xde, 0xd4, 0xd4, 0xc6, 0xa7, 0xad, 0x12, 0x47,
	0x45, 0xce, 0xcd, 0x8c, 0x35, 0xcc, 0xa6, 0x9e,
	0x5a, 0xc6, 0x60, 0xbb, 0xe3, 0xed, 0xec, 0x68,
	0x3f, 0x64, 0xf7, 0x06, 0x63, 0x9c, 0x8c, 0xc8,
	0x05, 0x3a, 0xad, 0x32, 0x79, 0x8b, 0x45, 0x96,
	0x93, 0x73, 0x4c, 0xe0
};
static const u8 key53[] __initconst = {
	0x42, 0x4b, 0x20, 0x81, 0x49, 0x50, 0xe9, 0xc2,
	0x43, 0x69, 0x36, 0xe7, 0x68, 0xae, 0xd5, 0x7e,
	0x42, 0x1a, 0x1b, 0xb4, 0x06, 0x4d, 0xa7, 0x17,
	0xb5, 0x31, 0xd6, 0x0c, 0xb0, 0x5c, 0x41, 0x0b
};
enum { nonce53 = 0xf44ce1931fbda3d7ULL };

static const u8 input54[] __initconst = {
	0x7b, 0xf6, 0x8b, 0xae, 0xc0, 0xcb, 0x10, 0x8e,
	0xe8, 0xd8, 0x2e, 0x3b, 0x14, 0xba, 0xb4, 0xd2,
	0x58, 0x6b, 0x2c, 0xec, 0xc1, 0x81, 0x71, 0xb4,
	0xc6, 0xea, 0x08, 0xc5, 0xc9, 0x78, 0xdb, 0xa2,
	0xfa, 0x44, 0x50, 0x9b, 0xc8, 0x53, 0x8d, 0x45,
	0x42, 0xe7, 0x09, 0xc4, 0x29, 0xd8, 0x75, 0x02,
	0xbb, 0xb2, 0x78, 0xcf, 0xe7
};
static const u8 output54[] __initconst = {
	0xaf, 0x2c, 0x83, 0x26, 0x6e, 0x7f, 0xa6, 0xe9,
	0x03, 0x75, 0xfe, 0xfe, 0x87, 0x58, 0xcf, 0xb5,
	0xbc, 0x3c, 0x9d, 0xa1, 0x6e, 0x13, 0xf1, 0x0f,
	0x9e, 0xbc, 0xe0, 0x54, 0x24, 0x32, 0xce, 0x95,
	0xe6, 0xa5, 0x59, 0x3d, 0x24, 0x1d, 0x8f, 0xb1,
	0x74, 0x6c, 0x56, 0xe7, 0x96, 0xc1, 0x91, 0xc8,
	0x2d, 0x0e, 0xb7, 0x51, 0x10
};
static const u8 key54[] __initconst = {
	0x00, 0x68, 0x74, 0xdc, 0x30, 0x9e, 0xe3, 0x52,
	0xa9, 0xae, 0xb6, 0x7c, 0xa1, 0xdc, 0x12, 0x2d,
	0x98, 0x32, 0x7a, 0x77, 0xe1, 0xdd, 0xa3, 0x76,
	0x72, 0x34, 0x83, 0xd8, 0xb7, 0x69, 0xba, 0x77
};
enum { nonce54 = 0xbea57d79b798b63aULL };

static const u8 input55[] __initconst = {
	0xb5, 0xf4, 0x2f, 0xc1, 0x5e, 0x10, 0xa7, 0x4e,
	0x74, 0x3d, 0xa3, 0x96, 0xc0, 0x4d, 0x7b, 0x92,
	0x8f, 0xdb, 0x2d, 0x15, 0x52, 0x6a, 0x95, 0x5e,
	0x40, 0x81, 0x4f, 0x70, 0x73, 0xea, 0x84, 0x65,
	0x3d, 0x9a, 0x4e, 0x03, 0x95, 0xf8, 0x5d, 0x2f,
	0x07, 0x02, 0x13, 0x13, 0xdd, 0x82, 0xe6, 0x3b,
	0xe1, 0x5f, 0xb3, 0x37, 0x9b, 0x88
};
static const u8 output55[] __initconst = {
	0xc1, 0x88, 0xbd, 0x92, 0x77, 0xad, 0x7c, 0x5f,
	0xaf, 0xa8, 0x57, 0x0e, 0x40, 0x0a, 0xdc, 0x70,
	0xfb, 0xc6, 0x71, 0xfd, 0xc4, 0x74, 0x60, 0xcc,
	0xa0, 0x89, 0x8e, 0x99, 0xf0, 0x06, 0xa6, 0x7c,
	0x97, 0x42, 0x21, 0x81, 0x6a, 0x07, 0xe7, 0xb3,
	0xf7, 0xa5, 0x03, 0x71, 0x50, 0x05, 0x63, 0x17,
	0xa9, 0x46, 0x0b, 0xff, 0x30, 0x78
};
static const u8 key55[] __initconst = {
	0x19, 0x8f, 0xe7, 0xd7, 0x6b, 0x7f, 0x6f, 0x69,
	0x86, 0x91, 0x0f, 0xa7, 0x4a, 0x69, 0x8e, 0x34,
	0xf3, 0xdb, 0xde, 0xaf, 0xf2, 0x66, 0x1d, 0x64,
	0x97, 0x0c, 0xcf, 0xfa, 0x33, 0x84, 0xfd, 0x0c
};
enum { nonce55 = 0x80aa3d3e2c51ef06ULL };

static const u8 input56[] __initconst = {
	0x6b, 0xe9, 0x73, 0x42, 0x27, 0x5e, 0x12, 0xcd,
	0xaa, 0x45, 0x12, 0x8b, 0xb3, 0xe6, 0x54, 0x33,
	0x31, 0x7d, 0xe2, 0x25, 0xc6, 0x86, 0x47, 0x67,
	0x86, 0x83, 0xe4, 0x46, 0xb5, 0x8f, 0x2c, 0xbb,
	0xe4, 0xb8, 0x9f, 0xa2, 0xa4, 0xe8, 0x75, 0x96,
	0x92, 0x51, 0x51, 0xac, 0x8e, 0x2e, 0x6f, 0xfc,
	0xbd, 0x0d, 0xa3, 0x9f, 0x16, 0x55, 0x3e
};
static const u8 output56[] __initconst = {
	0x42, 0x99, 0x73, 0x6c, 0xd9, 0x4b, 0x16, 0xe5,
	0x18, 0x63, 0x1a, 0xd9, 0x0e, 0xf1, 0x15, 0x2e,
	0x0f, 0x4b, 0xe4, 0x5f, 0xa0, 0x4d, 0xde, 0x9f,
	0xa7, 0x18, 0xc1, 0x0c, 0x0b, 0xae, 0x55, 0xe4,
	0x89, 0x18, 0xa4, 0x78, 0x9d, 0x25, 0x0d, 0xd5,
	0x94, 0x0f, 0xf9, 0x78, 0xa3, 0xa6, 0xe9, 0x9e,
	0x2c, 0x73, 0xf0, 0xf7, 0x35, 0xf3, 0x2b
};
static const u8 key56[] __initconst = {
	0x7d, 0x12, 0xad, 0x51, 0xd5, 0x6f, 0x8f, 0x96,
	0xc0, 0x5d, 0x9a, 0xd1, 0x7e, 0x20, 0x98, 0x0e,
	0x3c, 0x0a, 0x67, 0x6b, 0x1b, 0x88, 0x69, 0xd4,
	0x07, 0x8c, 0xaf, 0x0f, 0x3a, 0x28, 0xe4, 0x5d
};
enum { nonce56 = 0x70f4c372fb8b5984ULL };

static const u8 input57[] __initconst = {
	0x28, 0xa3, 0x06, 0xe8, 0xe7, 0x08, 0xb9, 0xef,
	0x0d, 0x63, 0x15, 0x99, 0xb2, 0x78, 0x7e, 0xaf,
	0x30, 0x50, 0xcf, 0xea, 0xc9, 0x91, 0x41, 0x2f,
	0x3b, 0x38, 0x70, 0xc4, 0x87, 0xb0, 0x3a, 0xee,
	0x4a, 0xea, 0xe3, 0x83, 0x68, 0x8b, 0xcf, 0xda,
	0x04, 0xa5, 0xbd, 0xb2, 0xde, 0x3c, 0x55, 0x13,
	0xfe, 0x96, 0xad, 0xc1, 0x61, 0x1b, 0x98, 0xde
};
static const u8 output57[] __initconst = {
	0xf4, 0x44, 0xe9, 0xd2, 0x6d, 0xc2, 0x5a, 0xe9,
	0xfd, 0x7e, 0x41, 0x54, 0x3f, 0xf4, 0x12, 0xd8,
	0x55, 0x0d, 0x12, 0x9b, 0xd5, 0x2e, 0x95, 0xe5,
	0x77, 0x42, 0x3f, 0x2c, 0xfb, 0x28, 0x9d, 0x72,
	0x6d, 0x89, 0x82, 0x27, 0x64, 0x6f, 0x0d, 0x57,
	0xa1, 0x25, 0xa3, 0x6b, 0x88, 0x9a, 0xac, 0x0c,
	0x76, 0x19, 0x90, 0xe2, 0x50, 0x5a, 0xf8, 0x12
};
static const u8 key57[] __initconst = {
	0x08, 0x26, 0xb8, 0xac, 0xf3, 0xa5, 0xc6, 0xa3,
	0x7f, 0x09, 0x87, 0xf5, 0x6c, 0x5a, 0x85, 0x6c,
	0x3d, 0xbd, 0xde, 0xd5, 0x87, 0xa3, 0x98, 0x7a,
	0xaa, 0x40, 0x3e, 0xf7, 0xff, 0x44, 0x5d, 0xee
};
enum { nonce57 = 0xc03a6130bf06b089ULL };

static const u8 input58[] __initconst = {
	0x82, 0xa5, 0x38, 0x6f, 0xaa, 0xb4, 0xaf, 0xb2,
	0x42, 0x01, 0xa8, 0x39, 0x3f, 0x15, 0x51, 0xa8,
	0x11, 0x1b, 0x93, 0xca, 0x9c, 0xa0, 0x57, 0x68,
	0x8f, 0xdb, 0x68, 0x53, 0x51, 0x6d, 0x13, 0x22,
	0x12, 0x9b, 0xbd, 0x33, 0xa8, 0x52, 0x40, 0x57,
	0x80, 0x9b, 0x98, 0xef, 0x56, 0x70, 0x11, 0xfa,
	0x36, 0x69, 0x7d, 0x15, 0x48, 0xf9, 0x3b, 0xeb,
	0x42
};
static const u8 output58[] __initconst = {
	0xff, 0x3a, 0x74, 0xc3, 0x3e, 0x44, 0x64, 0x4d,
	0x0e, 0x5f, 0x9d, 0xa8, 0xdb, 0xbe, 0x12, 0xef,
	0xba, 0x56, 0x65, 0x50, 0x76, 0xaf, 0xa4, 0x4e,
	0x01, 0xc1, 0xd3, 0x31, 0x14, 0xe2, 0xbe, 0x7b,
	0xa5, 0x67, 0xb4, 0xe3, 0x68, 0x40, 0x9c, 0xb0,
	0xb1, 0x78, 0xef, 0x49, 0x03, 0x0f, 0x2d, 0x56,
	0xb4, 0x37, 0xdb, 0xbc, 0x2d, 0x68, 0x1c, 0x3c,
	0xf1
};
static const u8 key58[] __initconst = {
	0x7e, 0xf1, 0x7c, 0x20, 0x65, 0xed, 0xcd, 0xd7,
	0x57, 0xe8, 0xdb, 0x90, 0x87, 0xdb, 0x5f, 0x63,
	0x3d, 0xdd, 0xb8, 0x2b, 0x75, 0x8e, 0x04, 0xb5,
	0xf4, 0x12, 0x79, 0xa9, 0x4d, 0x42, 0x16, 0x7f
};
enum { nonce58 = 0x92838183f80d2f7fULL };

static const u8 input59[] __initconst = {
	0x37, 0xf1, 0x9d, 0xdd, 0xd7, 0x08, 0x9f, 0x13,
	0xc5, 0x21, 0x82, 0x75, 0x08, 0x9e, 0x25, 0x16,
	0xb1, 0xd1, 0x71, 0x42, 0x28, 0x63, 0xac, 0x47,
	0x71, 0x54, 0xb1, 0xfc, 0x39, 0xf0, 0x61, 0x4f,
	0x7c, 0x6d, 0x4f, 0xc8, 0x33, 0xef, 0x7e, 0xc8,
	0xc0, 0x97, 0xfc, 0x1a, 0x61, 0xb4, 0x87, 0x6f,
	0xdd, 0x5a, 0x15, 0x7b, 0x1b, 0x95, 0x50, 0x94,
	0x1d, 0xba
};
static const u8 output59[] __initconst = {
	0x73, 0x67, 0xc5, 0x07, 0xbb, 0x57, 0x79, 0xd5,
	0xc9, 0x04, 0xdd, 0x88, 0xf3, 0x86, 0xe5, 0x70,
	0x49, 0x31, 0xe0, 0xcc, 0x3b, 0x1d, 0xdf, 0xb0,
	0xaf, 0xf4, 0x2d, 0xe0, 0x06, 0x10, 0x91, 0x8d,
	0x1c, 0xcf, 0x31, 0x0b, 0xf6, 0x73, 0xda, 0x1c,
	0xf0, 0x17, 0x52, 0x9e, 0x20, 0x2e, 0x9f, 0x8c,
	0xb3, 0x59, 0xce, 0xd4, 0xd3, 0xc1, 0x81, 0xe9,
	0x11, 0x36
};
static const u8 key59[] __initconst = {
	0xbd, 0x07, 0xd0, 0x53, 0x2c, 0xb3, 0xcc, 0x3f,
	0xc4, 0x95, 0xfd, 0xe7, 0x81, 0xb3, 0x29, 0x99,
	0x05, 0x45, 0xd6, 0x95, 0x25, 0x0b, 0x72, 0xd3,
	0xcd, 0xbb, 0x73, 0xf8, 0xfa, 0xc0, 0x9b, 0x7a
};
enum { nonce59 = 0x4a0db819b0d519e2ULL };

static const u8 input60[] __initconst = {
	0x58, 0x4e, 0xdf, 0x94, 0x3c, 0x76, 0x0a, 0x79,
	0x47, 0xf1, 0xbe, 0x88, 0xd3, 0xba, 0x94, 0xd8,
	0xe2, 0x8f, 0xe3, 0x2f, 0x2f, 0x74, 0x82, 0x55,
	0xc3, 0xda, 0xe2, 0x4e, 0x2c, 0x8c, 0x45, 0x1d,
	0x72, 0x8f, 0x54, 0x41, 0xb5, 0xb7, 0x69, 0xe4,
	0xdc, 0xd2, 0x36, 0x21, 0x5c, 0x28, 0x52, 0xf7,
	0x98, 0x8e, 0x72, 0xa7, 0x6d, 0x57, 0xed, 0xdc,
	0x3c, 0xe6, 0x6a
};
static const u8 output60[] __initconst = {
	0xda, 0xaf, 0xb5, 0xe3, 0x30, 0x65, 0x5c, 0xb1,
	0x48, 0x08, 0x43, 0x7b, 0x9e, 0xd2, 0x6a, 0x62,
	0x56, 0x7c, 0xad, 0xd9, 0xe5, 0xf6, 0x09, 0x71,
	0xcd, 0xe6, 0x05, 0x6b, 0x3f, 0x44, 0x3a, 0x5c,
	0xf6, 0xf8, 0xd7, 0xce, 0x7d, 0xd1, 0xe0, 0x4f,
	0x88, 0x15, 0x04, 0xd8, 0x20, 0xf0, 0x3e, 0xef,
	0xae, 0xa6, 0x27, 0xa3, 0x0e, 0xfc, 0x18, 0x90,
	0x33, 0xcd, 0xd3
};
static const u8 key60[] __initconst = {
	0xbf, 0xfd, 0x25, 0xb5, 0xb2, 0xfc, 0x78, 0x0c,
	0x8e, 0xb9, 0x57, 0x2f, 0x26, 0x4a, 0x7e, 0x71,
	0xcc, 0xf2, 0xe0, 0xfd, 0x24, 0x11, 0x20, 0x23,
	0x57, 0x00, 0xff, 0x80, 0x11, 0x0c, 0x1e, 0xff
};
enum { nonce60 = 0xf18df56fdb7954adULL };

static const u8 input61[] __initconst = {
	0xb0, 0xf3, 0x06, 0xbc, 0x22, 0xae, 0x49, 0x40,
	0xae, 0xff, 0x1b, 0x31, 0xa7, 0x98, 0xab, 0x1d,
	0xe7, 0x40, 0x23, 0x18, 0x4f, 0xab, 0x8e, 0x93,
	0x82, 0xf4, 0x56, 0x61, 0xfd, 0x2b, 0xcf, 0xa7,
	0xc4, 0xb4, 0x0a, 0xf4, 0xcb, 0xc7, 0x8c, 0x40,
	0x57, 0xac, 0x0b, 0x3e, 0x2a, 0x0a, 0x67, 0x83,
	0x50, 0xbf, 0xec, 0xb0, 0xc7, 0xf1, 0x32, 0x26,
	0x98, 0x80, 0x33, 0xb4
};
static const u8 output61[] __initconst = {
	0x9d, 0x23, 0x0e, 0xff, 0xcc, 0x7c, 0xd5, 0xcf,
	0x1a, 0xb8, 0x59, 0x1e, 0x92, 0xfd, 0x7f, 0xca,
	0xca, 0x3c, 0x18, 0x81, 0xde, 0xfa, 0x59, 0xc8,
	0x6f, 0x9c, 0x24, 0x3f, 0x3a, 0xe6, 0x0b, 0xb4,
	0x34, 0x48, 0x69, 0xfc, 0xb6, 0xea, 0xb2, 0xde,
	0x9f, 0xfd, 0x92, 0x36, 0x18, 0x98, 0x99, 0xaa,
	0x65, 0xe2, 0xea, 0xf4, 0xb1, 0x47, 0x8e, 0xb0,
	0xe7, 0xd4, 0x7a, 0x2c
};
static const u8 key61[] __initconst = {
	0xd7, 0xfd, 0x9b, 0xbd, 0x8f, 0x65, 0x0d, 0x00,
	0xca, 0xa1, 0x6c, 0x85, 0x85, 0xa4, 0x6d, 0xf1,
	0xb1, 0x68, 0x0c, 0x8b, 0x5d, 0x37, 0x72, 0xd0,
	0xd8, 0xd2, 0x25, 0xab, 0x9f, 0x7b, 0x7d, 0x95
};
enum { nonce61 = 0xd82caf72a9c4864fULL };

static const u8 input62[] __initconst = {
	0x10, 0x77, 0xf3, 0x2f, 0xc2, 0x50, 0xd6, 0x0c,
	0xba, 0xa8, 0x8d, 0xce, 0x0d, 0x58, 0x9e, 0x87,
	0xb1, 0x59, 0x66, 0x0a, 0x4a, 0xb3, 0xd8, 0xca,
	0x0a, 0x6b, 0xf8, 0xc6, 0x2b, 0x3f, 0x8e, 0x09,
	0xe0, 0x0a, 0x15, 0x85, 0xfe, 0xaa, 0xc6, 0xbd,
	0x30, 0xef, 0xe4, 0x10, 0x78, 0x03, 0xc1, 0xc7,
	0x8a, 0xd9, 0xde, 0x0b, 0x51, 0x07, 0xc4, 0x7b,
	0xe2, 0x2e, 0x36, 0x3a, 0xc2
};
static const u8 output62[] __initconst = {
	0xa0, 0x0c, 0xfc, 0xc1, 0xf6, 0xaf, 0xc2, 0xb8,
	0x5c, 0xef, 0x6e, 0xf3, 0xce, 0x15, 0x48, 0x05,
	0xb5, 0x78, 0x49, 0x51, 0x1f, 0x9d, 0xf4, 0xbf,
	0x2f, 0x53, 0xa2, 0xd1, 0x15, 0x20, 0x82, 0x6b,
	0xd2, 0x22, 0x6c, 0x4e, 0x14, 0x87, 0xe3, 0xd7,
	0x49, 0x45, 0x84, 0xdb, 0x5f, 0x68, 0x60, 0xc4,
	0xb3, 0xe6, 0x3f, 0xd1, 0xfc, 0xa5, 0x73, 0xf3,
	0xfc, 0xbb, 0xbe, 0xc8, 0x9d
};
static const u8 key62[] __initconst = {
	0x6e, 0xc9, 0xaf, 0xce, 0x35, 0xb9, 0x86, 0xd1,
	0xce, 0x5f, 0xd9, 0xbb, 0xd5, 0x1f, 0x7c, 0xcd,
	0xfe, 0x19, 0xaa, 0x3d, 0xea, 0x64, 0xc1, 0x28,
	0x40, 0xba, 0xa1, 0x28, 0xcd, 0x40, 0xb6, 0xf2
};
enum { nonce62 = 0xa1c0c265f900cde8ULL };

static const u8 input63[] __initconst = {
	0x7a, 0x70, 0x21, 0x2c, 0xef, 0xa6, 0x36, 0xd4,
	0xe0, 0xab, 0x8c, 0x25, 0x73, 0x34, 0xc8, 0x94,
	0x6c, 0x81, 0xcb, 0x19, 0x8d, 0x5a, 0x49, 0xaa,
	0x6f, 0xba, 0x83, 0x72, 0x02, 0x5e, 0xf5, 0x89,
	0xce, 0x79, 0x7e, 0x13, 0x3d, 0x5b, 0x98, 0x60,
	0x5d, 0xd9, 0xfb, 0x15, 0x93, 0x4c, 0xf3, 0x51,
	0x49, 0x55, 0xd1, 0x58, 0xdd, 0x7e, 0x6d, 0xfe,
	0xdd, 0x84, 0x23, 0x05, 0xba, 0xe9
};
static const u8 output63[] __initconst = {
	0x20, 0xb3, 0x5c, 0x03, 0x03, 0x78, 0x17, 0xfc,
	0x3b, 0x35, 0x30, 0x9a, 0x00, 0x18, 0xf5, 0xc5,
	0x06, 0x53, 0xf5, 0x04, 0x24, 0x9d, 0xd1, 0xb2,
	0xac, 0x5a, 0xb6, 0x2a, 0xa5, 0xda, 0x50, 0x00,
	0xec, 0xff, 0xa0, 0x7a, 0x14, 0x7b, 0xe4, 0x6b,
	0x63, 0xe8, 0x66, 0x86, 0x34, 0xfd, 0x74, 0x44,
	0xa2, 0x50, 0x97, 0x0d, 0xdc, 0xc3, 0x84, 0xf8,
	0x71, 0x02, 0x31, 0x95, 0xed, 0x54
};
static const u8 key63[] __initconst = {
	0x7d, 0x64, 0xb4, 0x12, 0x81, 0xe4, 0xe6, 0x8f,
	0xcc, 0xe7, 0xd1, 0x1f, 0x70, 0x20, 0xfd, 0xb8,
	0x3a, 0x7d, 0xa6, 0x53, 0x65, 0x30, 0x5d, 0xe3,
	0x1a, 0x44, 0xbe, 0x62, 0xed, 0x90, 0xc4, 0xd1
};
enum { nonce63 = 0xe8e849596c942276ULL };

static const u8 input64[] __initconst = {
	0x84, 0xf8, 0xda, 0x87, 0x23, 0x39, 0x60, 0xcf,
	0xc5, 0x50, 0x7e, 0xc5, 0x47, 0x29, 0x7c, 0x05,
	0xc2, 0xb4, 0xf4, 0xb2, 0xec, 0x5d, 0x48, 0x36,
	0xbf, 0xfc, 0x06, 0x8c, 0xf2, 0x0e, 0x88, 0xe7,
	0xc9, 0xc5, 0xa4, 0xa2, 0x83, 0x20, 0xa1, 0x6f,
	0x37, 0xe5, 0x2d, 0xa1, 0x72, 0xa1, 0x19, 0xef,
	0x05, 0x42, 0x08, 0xf2, 0x57, 0x47, 0x31, 0x1e,
	0x17, 0x76, 0x13, 0xd3, 0xcc, 0x75, 0x2c
};
static const u8 output64[] __initconst = {
	0xcb, 0xec, 0x90, 0x88, 0xeb, 0x31, 0x69, 0x20,
	0xa6, 0xdc, 0xff, 0x76, 0x98, 0xb0, 0x24, 0x49,
	0x7b, 0x20, 0xd9, 0xd1, 0x1b, 0xe3, 0x61, 0xdc,
	0xcf, 0x51, 0xf6, 0x70, 0x72, 0x33, 0x28, 0x94,
	0xac, 0x73, 0x18, 0xcf, 0x93, 0xfd, 0xca, 0x08,
	0x0d, 0xa2, 0xb9, 0x57, 0x1e, 0x51, 0xb6, 0x07,
	0x5c, 0xc1, 0x13, 0x64, 0x1d, 0x18, 0x6f, 0xe6,
	0x0b, 0xb7, 0x14, 0x03, 0x43, 0xb6, 0xaf
};
static const u8 key64[] __initconst = {
	0xbf, 0x82, 0x65, 0xe4, 0x50, 0xf9, 0x5e, 0xea,
	0x28, 0x91, 0xd1, 0xd2, 0x17, 0x7c, 0x13, 0x7e,
	0xf5, 0xd5, 0x6b, 0x06, 0x1c, 0x20, 0xc2, 0x82,
	0xa1, 0x7a, 0xa2, 0x14, 0xa1, 0xb0, 0x54, 0x58
};
enum { nonce64 = 0xe57c5095aa5723c9ULL };

static const u8 input65[] __initconst = {
	0x1c, 0xfb, 0xd3, 0x3f, 0x85, 0xd7, 0xba, 0x7b,
	0xae, 0xb1, 0xa5, 0xd2, 0xe5, 0x40, 0xce, 0x4d,
	0x3e, 0xab, 0x17, 0x9d, 0x7d, 0x9f, 0x03, 0x98,
	0x3f, 0x9f, 0xc8, 0xdd, 0x36, 0x17, 0x43, 0x5c,
	0x34, 0xd1, 0x23, 0xe0, 0x77, 0xbf, 0x35, 0x5d,
	0x8f, 0xb1, 0xcb, 0x82, 0xbb, 0x39, 0x69, 0xd8,
	0x90, 0x45, 0x37, 0xfd, 0x98, 0x25, 0xf7, 0x5b,
	0xce, 0x06, 0x43, 0xba, 0x61, 0xa8, 0x47, 0xb9
};
static const u8 output65[] __initconst = {
	0x73, 0xa5, 0x68, 0xab, 0x8b, 0xa5, 0xc3, 0x7e,
	0x74, 0xf8, 0x9d, 0xf5, 0x93, 0x6e, 0xf2, 0x71,
	0x6d, 0xde, 0x82, 0xc5, 0x40, 0xa0, 0x46, 0xb3,
	0x9a, 0x78, 0xa8, 0xf7, 0xdf, 0xb1, 0xc3, 0xdd,
	0x8d, 0x90, 0x00, 0x68, 0x21, 0x48, 0xe8, 0xba,
	0x56, 0x9f, 0x8f, 0xe7, 0xa4, 0x4d, 0x36, 0x55,
	0xd0, 0x34, 0x99, 0xa6, 0x1c, 0x4c, 0xc1, 0xe2,
	0x65, 0x98, 0x14, 0x8e, 0x6a, 0x05, 0xb1, 0x2b
};
static const u8 key65[] __initconst = {
	0xbd, 0x5c, 0x8a, 0xb0, 0x11, 0x29, 0xf3, 0x00,
	0x7a, 0x78, 0x32, 0x63, 0x34, 0x00, 0xe6, 0x7d,
	0x30, 0x54, 0xde, 0x37, 0xda, 0xc2, 0xc4, 0x3d,
	0x92, 0x6b, 0x4c, 0xc2, 0x92, 0xe9, 0x9e, 0x2a
};
enum { nonce65 = 0xf654a3031de746f2ULL };

static const u8 input66[] __initconst = {
	0x4b, 0x27, 0x30, 0x8f, 0x28, 0xd8, 0x60, 0x46,
	0x39, 0x06, 0x49, 0xea, 0x1b, 0x71, 0x26, 0xe0,
	0x99, 0x2b, 0xd4, 0x8f, 0x64, 0x64, 0xcd, 0xac,
	0x1d, 0x78, 0x88, 0x90, 0xe1, 0x5c, 0x24, 0x4b,
	0xdc, 0x2d, 0xb7, 0xee, 0x3a, 0xe6, 0x86, 0x2c,
	0x21, 0xe4, 0x2b, 0xfc, 0xe8, 0x19, 0xca, 0x65,
	0xe7, 0xdd, 0x6f, 0x52, 0xb3, 0x11, 0xe1, 0xe2,
	0xbf, 0xe8, 0x70, 0xe3, 0x0d, 0x45, 0xb8, 0xa5,
	0x20, 0xb7, 0xb5, 0xaf, 0xff, 0x08, 0xcf, 0x23,
	0x65, 0xdf, 0x8d, 0xc3, 0x31, 0xf3, 0x1e, 0x6a,
	0x58, 0x8d, 0xcc, 0x45, 0x16, 0x86, 0x1f, 0x31,
	0x5c, 0x27, 0xcd, 0xc8, 0x6b, 0x19, 0x1e, 0xec,
	0x44, 0x75, 0x63, 0x97, 0xfd, 0x79, 0xf6, 0x62,
	0xc5, 0xba, 0x17, 0xc7, 0xab, 0x8f, 0xbb, 0xed,
	0x85, 0x2a, 0x98, 0x79, 0x21, 0xec, 0x6e, 0x4d,
	0xdc, 0xfa, 0x72, 0x52, 0xba, 0xc8, 0x4c
};
static const u8 output66[] __initconst = {
	0x76, 0x5b, 0x2c, 0xa7, 0x62, 0xb9, 0x08, 0x4a,
	0xc6, 0x4a, 0x92, 0xc3, 0xbb, 0x10, 0xb3, 0xee,
	0xff, 0xb9, 0x07, 0xc7, 0x27, 0xcb, 0x1e, 0xcf,
	0x58, 0x6f, 0xa1, 0x64, 0xe8, 0xf1, 0x4e, 0xe1,
	0xef, 0x18, 0x96, 0xab, 0x97, 0x28, 0xd1, 0x7c,
	0x71, 0x6c, 0xd1, 0xe2, 0xfa, 0xd9, 0x75, 0xcb,
	0xeb, 0xea, 0x0c, 0x86, 0x82, 0xd8, 0xf4, 0xcc,
	0xea, 0xa3, 0x00, 0xfa, 0x82, 0xd2, 0xcd, 0xcb,
	0xdb, 0x63, 0x28, 0xe2, 0x82, 0xe9, 0x01, 0xed,
	0x31, 0xe6, 0x71, 0x45, 0x08, 0x89, 0x8a, 0x23,
	0xa8, 0xb5, 0xc2, 0xe2, 0x9f, 0xe9, 0xb8, 0x9a,
	0xc4, 0x79, 0x6d, 0x71, 0x52, 0x61, 0x74, 0x6c,
	0x1b, 0xd7, 0x65, 0x6d, 0x03, 0xc4, 0x1a, 0xc0,
	0x50, 0xba, 0xd6, 0xc9, 0x43, 0x50, 0xbe, 0x09,
	0x09, 0x8a, 0xdb, 0xaa, 0x76, 0x4e, 0x3b, 0x61,
	0x3c, 0x7c, 0x44, 0xe7, 0xdb, 0x10, 0xa7
};
static const u8 key66[] __initconst = {
	0x88, 0xdf, 0xca, 0x68, 0xaf, 0x4f, 0xb3, 0xfd,
	0x6e, 0xa7, 0x95, 0x35, 0x8a, 0xe8, 0x37, 0xe8,
	0xc8, 0x55, 0xa2, 0x2a, 0x6d, 0x77, 0xf8, 0x93,
	0x7a, 0x41, 0xf3, 0x7b, 0x95, 0xdf, 0x89, 0xf5
};
enum { nonce66 = 0x1024b4fdd415cf82ULL };

static const u8 input67[] __initconst = {
	0xd4, 0x2e, 0xfa, 0x92, 0xe9, 0x29, 0x68, 0xb7,
	0x54, 0x2c, 0xf7, 0xa4, 0x2d, 0xb7, 0x50, 0xb5,
	0xc5, 0xb2, 0x9d, 0x17, 0x5e, 0x0a, 0xca, 0x37,
	0xbf, 0x60, 0xae, 0xd2, 0x98, 0xe9, 0xfa, 0x59,
	0x67, 0x62, 0xe6, 0x43, 0x0c, 0x77, 0x80, 0x82,
	0x33, 0x61, 0xa3, 0xff, 0xc1, 0xa0, 0x8f, 0x56,
	0xbc, 0xec, 0x65, 0x43, 0x88, 0xa5, 0xff, 0x51,
	0x64, 0x30, 0xee, 0x34, 0xb7, 0x5c, 0x28, 0x68,
	0xc3, 0x52, 0xd2, 0xac, 0x78, 0x2a, 0xa6, 0x10,
	0xb8, 0xb2, 0x4c, 0x80, 0x4f, 0x99, 0xb2, 0x36,
	0x94, 0x8f, 0x66, 0xcb, 0xa1, 0x91, 0xed, 0x06,
	0x42, 0x6d, 0xc1, 0xae, 0x55, 0x93, 0xdd, 0x93,
	0x9e, 0x88, 0x34, 0x7f, 0x98, 0xeb, 0xbe, 0x61,
	0xf9, 0xa9, 0x0f, 0xd9, 0xc4, 0x87, 0xd5, 0xef,
	0xcc, 0x71, 0x8c, 0x0e, 0xce, 0xad, 0x02, 0xcf,
	0xa2, 0x61, 0xdf, 0xb1, 0xfe, 0x3b, 0xdc, 0xc0,
	0x58, 0xb5, 0x71, 0xa1, 0x83, 0xc9, 0xb4, 0xaf,
	0x9d, 0x54, 0x12, 0xcd, 0xea, 0x06, 0xd6, 0x4e,
	0xe5, 0x27, 0x0c, 0xc3, 0xbb, 0xa8, 0x0a, 0x81,
	0x75, 0xc3, 0xc9, 0xd4, 0x35, 0x3e, 0x53, 0x9f,
	0xaa, 0x20, 0xc0, 0x68, 0x39, 0x2c, 0x96, 0x39,
	0x53, 0x81, 0xda, 0x07, 0x0f, 0x44, 0xa5, 0x47,
	0x0e, 0xb3, 0x87, 0x0d, 0x1b, 0xc1, 0xe5, 0x41,
	0x35, 0x12, 0x58, 0x96, 0x69, 0x8a, 0x1a, 0xa3,
	0x9d, 0x3d, 0xd4, 0xb1, 0x8e, 0x1f, 0x96, 0x87,
	0xda, 0xd3, 0x19, 0xe2, 0xb1, 0x3a, 0x19, 0x74,
	0xa0, 0x00, 0x9f, 0x4d, 0xbc, 0xcb, 0x0c, 0xe9,
	0xec, 0x10, 0xdf, 0x2a, 0x88, 0xdc, 0x30, 0x51,
	0x46, 0x56, 0x53, 0x98, 0x6a, 0x26, 0x14, 0x05,
	0x54, 0x81, 0x55, 0x0b, 0x3c, 0x85, 0xdd, 0x33,
	0x81, 0x11, 0x29, 0x82, 0x46, 0x35, 0xe1, 0xdb,
	0x59, 0x7b
};
static const u8 output67[] __initconst = {
	0x64, 0x6c, 0xda, 0x7f, 0xd4, 0xa9, 0x2a, 0x5e,
	0x22, 0xae, 0x8d, 0x67, 0xdb, 0xee, 0xfd, 0xd0,
	0x44, 0x80, 0x17, 0xb2, 0xe3, 0x87, 0xad, 0x57,
	0x15, 0xcb, 0x88, 0x64, 0xc0, 0xf1, 0x49, 0x3d,
	0xfa, 0xbe, 0xa8, 0x9f, 0x12, 0xc3, 0x57, 0x56,
	0x70, 0xa5, 0xc5, 0x6b, 0xf1, 0xab, 0xd5, 0xde,
	0x77, 0x92, 0x6a, 0x56, 0x03, 0xf5, 0x21, 0x0d,
	0xb6, 0xc4, 0xcc, 0x62, 0x44, 0x3f, 0xb1, 0xc1,
	0x61, 0x41, 0x90, 0xb2, 0xd5, 0xb8, 0xf3, 0x57,
	0xfb, 0xc2, 0x6b, 0x25, 0x58, 0xc8, 0x45, 0x20,
	0x72, 0x29, 0x6f, 0x9d, 0xb5, 0x81, 0x4d, 0x2b,
	0xb2, 0x89, 0x9e, 0x91, 0x53, 0x97, 0x1c, 0xd9,
	0x3d, 0x79, 0xdc, 0x14, 0xae, 0x01, 0x73, 0x75,
	0xf0, 0xca, 0xd5, 0xab, 0x62, 0x5c, 0x7a, 0x7d,
	0x3f, 0xfe, 0x22, 0x7d, 0xee, 0xe2, 0xcb, 0x76,
	0x55, 0xec, 0x06, 0xdd, 0x41, 0x47, 0x18, 0x62,
	0x1d, 0x57, 0xd0, 0xd6, 0xb6, 0x0f, 0x4b, 0xfc,
	0x79, 0x19, 0xf4, 0xd6, 0x37, 0x86, 0x18, 0x1f,
	0x98, 0x0d, 0x9e, 0x15, 0x2d, 0xb6, 0x9a, 0x8a,
	0x8c, 0x80, 0x22, 0x2f, 0x82, 0xc4, 0xc7, 0x36,
	0xfa, 0xfa, 0x07, 0xbd, 0xc2, 0x2a, 0xe2, 0xea,
	0x93, 0xc8, 0xb2, 0x90, 0x33, 0xf2, 0xee, 0x4b,
	0x1b, 0xf4, 0x37, 0x92, 0x13, 0xbb, 0xe2, 0xce,
	0xe3, 0x03, 0xcf, 0x07, 0x94, 0xab, 0x9a, 0xc9,
	0xff, 0x83, 0x69, 0x3a, 0xda, 0x2c, 0xd0, 0x47,
	0x3d, 0x6c, 0x1a, 0x60, 0x68, 0x47, 0xb9, 0x36,
	0x52, 0xdd, 0x16, 0xef, 0x6c, 0xbf, 0x54, 0x11,
	0x72, 0x62, 0xce, 0x8c, 0x9d, 0x90, 0xa0, 0x25,
	0x06, 0x92, 0x3e, 0x12, 0x7e, 0x1a, 0x1d, 0xe5,
	0xa2, 0x71, 0xce, 0x1c, 0x4c, 0x6a, 0x7c, 0xdc,
	0x3d, 0xe3, 0x6e, 0x48, 0x9d, 0xb3, 0x64, 0x7d,
	0x78, 0x40
};
static const u8 key67[] __initconst = {
	0xa9, 0x20, 0x75, 0x89, 0x7e, 0x37, 0x85, 0x48,
	0xa3, 0xfb, 0x7b, 0xe8, 0x30, 0xa7, 0xe3, 0x6e,
	0xa6, 0xc1, 0x71, 0x17, 0xc1, 0x6c, 0x9b, 0xc2,
	0xde, 0xf0, 0xa7, 0x19, 0xec, 0xce, 0xc6, 0x53
};
enum { nonce67 = 0x4adc4d1f968c8a10ULL };

static const u8 input68[] __initconst = {
	0x99, 0xae, 0x72, 0xfb, 0x16, 0xe1, 0xf1, 0x59,
	0x43, 0x15, 0x4e, 0x33, 0xa0, 0x95, 0xe7, 0x6c,
	0x74, 0x24, 0x31, 0xca, 0x3b, 0x2e, 0xeb, 0xd7,
	0x11, 0xd8, 0xe0, 0x56, 0x92, 0x91, 0x61, 0x57,
	0xe2, 0x82, 0x9f, 0x8f, 0x37, 0xf5, 0x3d, 0x24,
	0x92, 0x9d, 0x87, 0x00, 0x8d, 0x89, 0xe0, 0x25,
	0x8b, 0xe4, 0x20, 0x5b, 0x8a, 0x26, 0x2c, 0x61,
	0x78, 0xb0, 0xa6, 0x3e, 0x82, 0x18, 0xcf, 0xdc,
	0x2d, 0x24, 0xdd, 0x81, 0x42, 0xc4, 0x95, 0xf0,
	0x48, 0x60, 0x71, 0xe3, 0xe3, 0xac, 0xec, 0xbe,
	0x98, 0x6b, 0x0c, 0xb5, 0x6a, 0xa9, 0xc8, 0x79,
	0x23, 0x2e, 0x38, 0x0b, 0x72, 0x88, 0x8c, 0xe7,
	0x71, 0x8b, 0x36, 0xe3, 0x58, 0x3d, 0x9c, 0xa0,
	0xa2, 0xea, 0xcf, 0x0c, 0x6a, 0x6c, 0x64, 0xdf,
	0x97, 0x21, 0x8f, 0x93, 0xfb, 0xba, 0xf3, 0x5a,
	0xd7, 0x8f, 0xa6, 0x37, 0x15, 0x50, 0x43, 0x02,
	0x46, 0x7f, 0x93, 0x46, 0x86, 0x31, 0xe2, 0xaa,
	0x24, 0xa8, 0x26, 0xae, 0xe6, 0xc0, 0x05, 0x73,
	0x0b, 0x4f, 0x7e, 0xed, 0x65, 0xeb, 0x56, 0x1e,
	0xb6, 0xb3, 0x0b, 0xc3, 0x0e, 0x31, 0x95, 0xa9,
	0x18, 0x4d, 0xaf, 0x38, 0xd7, 0xec, 0xc6, 0x44,
	0x72, 0x77, 0x4e, 0x25, 0x4b, 0x25, 0xdd, 0x1e,
	0x8c, 0xa2, 0xdf, 0xf6, 0x2a, 0x97, 0x1a, 0x88,
	0x2c, 0x8a, 0x5d, 0xfe, 0xe8, 0xfb, 0x35, 0xe8,
	0x0f, 0x2b, 0x7a, 0x18, 0x69, 0x43, 0x31, 0x1d,
	0x38, 0x6a, 0x62, 0x95, 0x0f, 0x20, 0x4b, 0xbb,
	0x97, 0x3c, 0xe0, 0x64, 0x2f, 0x52, 0xc9, 0x2d,
	0x4d, 0x9d, 0x54, 0x04, 0x3d, 0xc9, 0xea, 0xeb,
	0xd0, 0x86, 0x52, 0xff, 0x42, 0xe1, 0x0d, 0x7a,
	0xad, 0x88, 0xf9, 0x9b, 0x1e, 0x5e, 0x12, 0x27,
	0x95, 0x3e, 0x0c, 0x2c, 0x13, 0x00, 0x6f, 0x8e,
	0x93, 0x69, 0x0e, 0x01, 0x8c, 0xc1, 0xfd, 0xb3
};
static const u8 output68[] __initconst = {
	0x26, 0x3e, 0xf2, 0xb1, 0xf5, 0xef, 0x81, 0xa4,
	0xb7, 0x42, 0xd4, 0x26, 0x18, 0x4b, 0xdd, 0x6a,
	0x47, 0x15, 0xcb, 0x0e, 0x57, 0xdb, 0xa7, 0x29,
	0x7e, 0x7b, 0x3f, 0x47, 0x89, 0x57, 0xab, 0xea,
	0x14, 0x7b, 0xcf, 0x37, 0xdb, 0x1c, 0xe1, 0x11,
	0x77, 0xae, 0x2e, 0x4c, 0xd2, 0x08, 0x3f, 0xa6,
	0x62, 0x86, 0xa6, 0xb2, 0x07, 0xd5, 0x3f, 0x9b,
	0xdc, 0xc8, 0x50, 0x4b, 0x7b, 0xb9, 0x06, 0xe6,
	0xeb, 0xac, 0x98, 0x8c, 0x36, 0x0c, 0x1e, 0xb2,
	0xc8, 0xfb, 0x24, 0x60, 0x2c, 0x08, 0x17, 0x26,
	0x5b, 0xc8, 0xc2, 0xdf, 0x9c, 0x73, 0x67, 0x4a,
	0xdb, 0xcf, 0xd5, 0x2c, 0x2b, 0xca, 0x24, 0xcc,
	0xdb, 0xc9, 0xa8, 0xf2, 0x5d, 0x67, 0xdf, 0x5c,
	0x62, 0x0b, 0x58, 0xc0, 0x83, 0xde, 0x8b, 0xf6,
	0x15, 0x0a, 0xd6, 0x32, 0xd8, 0xf5, 0xf2, 0x5f,
	0x33, 0xce, 0x7e, 0xab, 0x76, 0xcd, 0x14, 0x91,
	0xd8, 0x41, 0x90, 0x93, 0xa1, 0xaf, 0xf3, 0x45,
	0x6c, 0x1b, 0x25, 0xbd, 0x48, 0x51, 0x6d, 0x15,
	0x47, 0xe6, 0x23, 0x50, 0x32, 0x69, 0x1e, 0xb5,
	0x94, 0xd3, 0x97, 0xba, 0xd7, 0x37, 0x4a, 0xba,
	0xb9, 0xcd, 0xfb, 0x96, 0x9a, 0x90, 0xe0, 0x37,
	0xf8, 0xdf, 0x91, 0x6c, 0x62, 0x13, 0x19, 0x21,
	0x4b, 0xa9, 0xf1, 0x12, 0x66, 0xe2, 0x74, 0xd7,
	0x81, 0xa0, 0x74, 0x8d, 0x7e, 0x7e, 0xc9, 0xb1,
	0x69, 0x8f, 0xed, 0xb3, 0xf6, 0x97, 0xcd, 0x72,
	0x78, 0x93, 0xd3, 0x54, 0x6b, 0x43, 0xac, 0x29,
	0xb4, 0xbc, 0x7d, 0xa4, 0x26, 0x4b, 0x7b, 0xab,
	0xd6, 0x67, 0x22, 0xff, 0x03, 0x92, 0xb6, 0xd4,
	0x96, 0x94, 0x5a, 0xe5, 0x02, 0x35, 0x77, 0xfa,
	0x3f, 0x54, 0x1d, 0xdd, 0x35, 0x39, 0xfe, 0x03,
	0xdd, 0x8e, 0x3c, 0x8c, 0xc2, 0x69, 0x2a, 0xb1,
	0xb7, 0xb3, 0xa1, 0x89, 0x84, 0xea, 0x16, 0xe2
};
static const u8 key68[] __initconst = {
	0xd2, 0x49, 0x7f, 0xd7, 0x49, 0x66, 0x0d, 0xb3,
	0x5a, 0x7e, 0x3c, 0xfc, 0x37, 0x83, 0x0e, 0xf7,
	0x96, 0xd8, 0xd6, 0x33, 0x79, 0x2b, 0x84, 0x53,
	0x06, 0xbc, 0x6c, 0x0a, 0x55, 0x84, 0xfe, 0xab
};
enum { nonce68 = 0x6a6df7ff0a20de06ULL };

static const u8 input69[] __initconst = {
	0xf9, 0x18, 0x4c, 0xd2, 0x3f, 0xf7, 0x22, 0xd9,
	0x58, 0xb6, 0x3b, 0x38, 0x69, 0x79, 0xf4, 0x71,
	0x5f, 0x38, 0x52, 0x1f, 0x17, 0x6f, 0x6f, 0xd9,
	0x09, 0x2b, 0xfb, 0x67, 0xdc, 0xc9, 0xe8, 0x4a,
	0x70, 0x9f, 0x2e, 0x3c, 0x06, 0xe5, 0x12, 0x20,
	0x25, 0x29, 0xd0, 0xdc, 0x81, 0xc5, 0xc6, 0x0f,
	0xd2, 0xa8, 0x81, 0x15, 0x98, 0xb2, 0x71, 0x5a,
	0x9a, 0xe9, 0xfb, 0xaf, 0x0e, 0x5f, 0x8a, 0xf3,
	0x16, 0x4a, 0x47, 0xf2, 0x5c, 0xbf, 0xda, 0x52,
	0x9a, 0xa6, 0x36, 0xfd, 0xc6, 0xf7, 0x66, 0x00,
	0xcc, 0x6c, 0xd4, 0xb3, 0x07, 0x6d, 0xeb, 0xfe,
	0x92, 0x71, 0x25, 0xd0, 0xcf, 0x9c, 0xe8, 0x65,
	0x45, 0x10, 0xcf, 0x62, 0x74, 0x7d, 0xf2, 0x1b,
	0x57, 0xa0, 0xf1, 0x6b, 0xa4, 0xd5, 0xfa, 0x12,
	0x27, 0x5a, 0xf7, 0x99, 0xfc, 0xca, 0xf3, 0xb8,
	0x2c, 0x8b, 0xba, 0x28, 0x74, 0xde, 0x8f, 0x78,
	0xa2, 0x8c, 0xaf, 0x89, 0x4b, 0x05, 0xe2, 0xf3,
	0xf8, 0xd2, 0xef, 0xac, 0xa4, 0xc4, 0xe2, 0xe2,
	0x36, 0xbb, 0x5e, 0xae, 0xe6, 0x87, 0x3d, 0x88,
	0x9f, 0xb8, 0x11, 0xbb, 0xcf, 0x57, 0xce, 0xd0,
	0xba, 0x62, 0xf4, 0xf8, 0x9b, 0x95, 0x04, 0xc9,
	0xcf, 0x01, 0xe9, 0xf1, 0xc8, 0xc6, 0x22, 0xa4,
	0xf2, 0x8b, 0x2f, 0x24, 0x0a, 0xf5, 0x6e, 0xb7,
	0xd4, 0x2c, 0xb6, 0xf7, 0x5c, 0x97, 0x61, 0x0b,
	0xd9, 0xb5, 0x06, 0xcd, 0xed, 0x3e, 0x1f, 0xc5,
	0xb2, 0x6c, 0xa3, 0xea, 0xb8, 0xad, 0xa6, 0x42,
	0x88, 0x7a, 0x52, 0xd5, 0x64, 0xba, 0xb5, 0x20,
	0x10, 0xa0, 0x0f, 0x0d, 0xea, 0xef, 0x5a, 0x9b,
	0x27, 0xb8, 0xca, 0x20, 0x19, 0x6d, 0xa8, 0xc4,
	0x46, 0x04, 0xb3, 0xe8, 0xf8, 0x66, 0x1b, 0x0a,
	0xce, 0x76, 0x5d, 0x59, 0x58, 0x05, 0xee, 0x3e,
	0x3c, 0x86, 0x5b, 0x49, 0x1c, 0x72, 0x18, 0x01,
	0x62, 0x92, 0x0f, 0x3e, 0xd1, 0x57, 0x5e, 0x20,
	0x7b, 0xfb, 0x4d, 0x3c, 0xc5, 0x35, 0x43, 0x2f,
	0xb0, 0xc5, 0x7c, 0xe4, 0xa2, 0x84, 0x13, 0x77
};
static const u8 output69[] __initconst = {
	0xbb, 0x4a, 0x7f, 0x7c, 0xd5, 0x2f, 0x89, 0x06,
	0xec, 0x20, 0xf1, 0x9a, 0x11, 0x09, 0x14, 0x2e,
	0x17, 0x50, 0xf9, 0xd5, 0xf5, 0x48, 0x7c, 0x7a,
	0x55, 0xc0, 0x57, 0x03, 0xe3, 0xc4, 0xb2, 0xb7,
	0x18, 0x47, 0x95, 0xde, 0xaf, 0x80, 0x06, 0x3c,
	0x5a, 0xf2, 0xc3, 0x53, 0xe3, 0x29, 0x92, 0xf8,
	0xff, 0x64, 0x85, 0xb9, 0xf7, 0xd3, 0x80, 0xd2,
	0x0c, 0x5d, 0x7b, 0x57, 0x0c, 0x51, 0x79, 0x86,
	0xf3, 0x20, 0xd2, 0xb8, 0x6e, 0x0c, 0x5a, 0xce,
	0xeb, 0x88, 0x02, 0x8b, 0x82, 0x1b, 0x7f, 0xf5,
	0xde, 0x7f, 0x48, 0x48, 0xdf, 0xa0, 0x55, 0xc6,
	0x0c, 0x22, 0xa1, 0x80, 0x8d, 0x3b, 0xcb, 0x40,
	0x2d, 0x3d, 0x0b, 0xf2, 0xe0, 0x22, 0x13, 0x99,
	0xe1, 0xa7, 0x27, 0x68, 0x31, 0xe1, 0x24, 0x5d,
	0xd2, 0xee, 0x16, 0xc1, 0xd7, 0xa8, 0x14, 0x19,
	0x23, 0x72, 0x67, 0x27, 0xdc, 0x5e, 0xb9, 0xc7,
	0xd8, 0xe3, 0x55, 0x50, 0x40, 0x98, 0x7b, 0xe7,
	0x34, 0x1c, 0x3b, 0x18, 0x14, 0xd8, 0x62, 0xc1,
	0x93, 0x84, 0xf3, 0x5b, 0xdd, 0x9e, 0x1f, 0x3b,
	0x0b, 0xbc, 0x4e, 0x5b, 0x79, 0xa3, 0xca, 0x74,
	0x2a, 0x98, 0xe8, 0x04, 0x39, 0xef, 0xc6, 0x76,
	0x6d, 0xee, 0x9f, 0x67, 0x5b, 0x59, 0x3a, 0xe5,
	0xf2, 0x3b, 0xca, 0x89, 0xe8, 0x9b, 0x03, 0x3d,
	0x11, 0xd2, 0x4a, 0x70, 0xaf, 0x88, 0xb0, 0x94,
	0x96, 0x26, 0xab, 0x3c, 0xc1, 0xb8, 0xe4, 0xe7,
	0x14, 0x61, 0x64, 0x3a, 0x61, 0x08, 0x0f, 0xa9,
	0xce, 0x64, 0xb2, 0x40, 0xf8, 0x20, 0x3a, 0xa9,
	0x31, 0xbd, 0x7e, 0x16, 0xca, 0xf5, 0x62, 0x0f,
	0x91, 0x9f, 0x8e, 0x1d, 0xa4, 0x77, 0xf3, 0x87,
	0x61, 0xe8, 0x14, 0xde, 0x18, 0x68, 0x4e, 0x9d,
	0x73, 0xcd, 0x8a, 0xe4, 0x80, 0x84, 0x23, 0xaa,
	0x9d, 0x64, 0x1c, 0x80, 0x41, 0xca, 0x82, 0x40,
	0x94, 0x55, 0xe3, 0x28, 0xa1, 0x97, 0x71, 0xba,
	0xf2, 0x2c, 0x39, 0x62, 0x29, 0x56, 0xd0, 0xff,
	0xb2, 0x82, 0x20, 0x59, 0x1f, 0xc3, 0x64, 0x57
};
static const u8 key69[] __initconst = {
	0x19, 0x09, 0xe9, 0x7c, 0xd9, 0x02, 0x4a, 0x0c,
	0x52, 0x25, 0xad, 0x5c, 0x2e, 0x8d, 0x86, 0x10,
	0x85, 0x2b, 0xba, 0xa4, 0x44, 0x5b, 0x39, 0x3e,
	0x18, 0xaa, 0xce, 0x0e, 0xe2, 0x69, 0x3c, 0xcf
};
enum { nonce69 = 0xdb925a1948f0f060ULL };

static const u8 input70[] __initconst = {
	0x10, 0xe7, 0x83, 0xcf, 0x42, 0x9f, 0xf2, 0x41,
	0xc7, 0xe4, 0xdb, 0xf9, 0xa3, 0x02, 0x1d, 0x8d,
	0x50, 0x81, 0x2c, 0x6b, 0x92, 0xe0, 0x4e, 0xea,
	0x26, 0x83, 0x2a, 0xd0, 0x31, 0xf1, 0x23, 0xf3,
	0x0e, 0x88, 0x14, 0x31, 0xf9, 0x01, 0x63, 0x59,
	0x21, 0xd1, 0x8b, 0xdd, 0x06, 0xd0, 0xc6, 0xab,
	0x91, 0x71, 0x82, 0x4d, 0xd4, 0x62, 0x37, 0x17,
	0xf9, 0x50, 0xf9, 0xb5, 0x74, 0xce, 0x39, 0x80,
	0x80, 0x78, 0xf8, 0xdc, 0x1c, 0xdb, 0x7c, 0x3d,
	0xd4, 0x86, 0x31, 0x00, 0x75, 0x7b, 0xd1, 0x42,
	0x9f, 0x1b, 0x97, 0x88, 0x0e, 0x14, 0x0e, 0x1e,
	0x7d, 0x7b, 0xc4, 0xd2, 0xf3, 0xc1, 0x6d, 0x17,
	0x5d, 0xc4, 0x75, 0x54, 0x0f, 0x38, 0x65, 0x89,
	0xd8, 0x7d, 0xab, 0xc9, 0xa7, 0x0a, 0x21, 0x0b,
	0x37, 0x12, 0x05, 0x07, 0xb5, 0x68, 0x32, 0x32,
	0xb9, 0xf8, 0x97, 0x17, 0x03, 0xed, 0x51, 0x8f,
	0x3d, 0x5a, 0xd0, 0x12, 0x01, 0x6e, 0x2e, 0x91,
	0x1c, 0xbe, 0x6b, 0xa3, 0xcc, 0x75, 0x62, 0x06,
	0x8e, 0x65, 0xbb, 0xe2, 0x29, 0x71, 0x4b, 0x89,
	0x6a, 0x9d, 0x85, 0x8c, 0x8c, 0xdf, 0x94, 0x95,
	0x23, 0x66, 0xf8, 0x92, 0xee, 0x56, 0xeb, 0xb3,
	0xeb, 0xd2, 0x4a, 0x3b, 0x77, 0x8a, 0x6e, 0xf6,
	0xca, 0xd2, 0x34, 0x00, 0xde, 0xbe, 0x1d, 0x7a,
	0x73, 0xef, 0x2b, 0x80, 0x56, 0x16, 0x29, 0xbf,
	0x6e, 0x33, 0xed, 0x0d, 0xe2, 0x02, 0x60, 0x74,
	0xe9, 0x0a, 0xbc, 0xd1, 0xc5, 0xe8, 0x53, 0x02,
	0x79, 0x0f, 0x25, 0x0c, 0xef, 0xab, 0xd3, 0xbc,
	0xb7, 0xfc, 0xf3, 0xb0, 0x34, 0xd1, 0x07, 0xd2,
	0x5a, 0x31, 0x1f, 0xec, 0x1f, 0x87, 0xed, 0xdd,
	0x6a, 0xc1, 0xe8, 0xb3, 0x25, 0x4c, 0xc6, 0x9b,
	0x91, 0x73, 0xec, 0x06, 0x73, 0x9e, 0x57, 0x65,
	0x32, 0x75, 0x11, 0x74, 0x6e, 0xa4, 0x7d, 0x0d,
	0x74, 0x9f, 0x51, 0x10, 0x10, 0x47, 0xc9, 0x71,
	0x6e, 0x97, 0xae, 0x44, 0x41, 0xef, 0x98, 0x78,
	0xf4, 0xc5, 0xbd, 0x5e, 0x00, 0xe5, 0xfd, 0xe2,
	0xbe, 0x8c, 0xc2, 0xae, 0xc2, 0xee, 0x59, 0xf6,
	0xcb, 0x20, 0x54, 0x84, 0xc3, 0x31, 0x7e, 0x67,
	0x71, 0xb6, 0x76, 0xbe, 0x81, 0x8f, 0x82, 0xad,
	0x01, 0x8f, 0xc4, 0x00, 0x04, 0x3d, 0x8d, 0x34,
	0xaa, 0xea, 0xc0, 0xea, 0x91, 0x42, 0xb6, 0xb8,
	0x43, 0xf3, 0x17, 0xb2, 0x73, 0x64, 0x82, 0x97,
	0xd5, 0xc9, 0x07, 0x77, 0xb1, 0x26, 0xe2, 0x00,
	0x6a, 0xae, 0x70, 0x0b, 0xbe, 0xe6, 0xb8, 0x42,
	0x81, 0x55, 0xf7, 0xb8, 0x96, 0x41, 0x9d, 0xd4,
	0x2c, 0x27, 0x00, 0xcc, 0x91, 0x28, 0x22, 0xa4,
	0x7b, 0x42, 0x51, 0x9e, 0xd6, 0xec, 0xf3, 0x6b,
	0x00, 0xff, 0x5c, 0xa2, 0xac, 0x47, 0x33, 0x2d,
	0xf8, 0x11, 0x65, 0x5f, 0x4d, 0x79, 0x8b, 0x4f,
	0xad, 0xf0, 0x9d, 0xcd, 0xb9, 0x7b, 0x08, 0xf7,
	0x32, 0x51, 0xfa, 0x39, 0xaa, 0x78, 0x05, 0xb1,
	0xf3, 0x5d, 0xe8, 0x7c, 0x8e, 0x4f, 0xa2, 0xe0,
	0x98, 0x0c, 0xb2, 0xa7, 0xf0, 0x35, 0x8e, 0x70,
	0x7c, 0x82, 0xf3, 0x1b, 0x26, 0x28, 0x12, 0xe5,
	0x23, 0x57, 0xe4, 0xb4, 0x9b, 0x00, 0x39, 0x97,
	0xef, 0x7c, 0x46, 0x9b, 0x34, 0x6b, 0xe7, 0x0e,
	0xa3, 0x2a, 0x18, 0x11, 0x64, 0xc6, 0x7c, 0x8b,
	0x06, 0x02, 0xf5, 0x69, 0x76, 0xf9, 0xaa, 0x09,
	0x5f, 0x68, 0xf8, 0x4a, 0x79, 0x58, 0xec, 0x37,
	0xcf, 0x3a, 0xcc, 0x97, 0x70, 0x1d, 0x3e, 0x52,
	0x18, 0x0a, 0xad, 0x28, 0x5b, 0x3b, 0xe9, 0x03,
	0x84, 0xe9, 0x68, 0x50, 0xce, 0xc4, 0xbc, 0x3e,
	0x21, 0xad, 0x63, 0xfe, 0xc6, 0xfd, 0x6e, 0x69,
	0x84, 0xa9, 0x30, 0xb1, 0x7a, 0xc4, 0x31, 0x10,
	0xc1, 0x1f, 0x6e, 0xeb, 0xa5, 0xa6, 0x01
};
static const u8 output70[] __initconst = {
	0x0f, 0x93, 0x2a, 0x20, 0xb3, 0x87, 0x2d, 0xce,
	0xd1, 0x3b, 0x30, 0xfd, 0x06, 0x6d, 0x0a, 0xaa,
	0x3e, 0xc4, 0x29, 0x02, 0x8a, 0xde, 0xa6, 0x4b,
	0x45, 0x1b, 0x4f, 0x25, 0x59, 0xd5, 0x56, 0x6a,
	0x3b, 0x37, 0xbd, 0x3e, 0x47, 0x12, 0x2c, 0x4e,
	0x60, 0x5f, 0x05, 0x75, 0x61, 0x23, 0x05, 0x74,
	0xcb, 0xfc, 0x5a, 0xb3, 0xac, 0x5c, 0x3d, 0xab,
	0x52, 0x5f, 0x05, 0xbc, 0x57, 0xc0, 0x7e, 0xcf,
	0x34, 0x5d, 0x7f, 0x41, 0xa3, 0x17, 0x78, 0xd5,
	0x9f, 0xec, 0x0f, 0x1e, 0xf9, 0xfe, 0xa3, 0xbd,
	0x28, 0xb0, 0xba, 0x4d, 0x84, 0xdb, 0xae, 0x8f,
	0x1d, 0x98, 0xb7, 0xdc, 0xf9, 0xad, 0x55, 0x9c,
	0x89, 0xfe, 0x9b, 0x9c, 0xa9, 0x89, 0xf6, 0x97,
	0x9c, 0x3f, 0x09, 0x3e, 0xc6, 0x02, 0xc2, 0x55,
	0x58, 0x09, 0x54, 0x66, 0xe4, 0x36, 0x81, 0x35,
	0xca, 0x88, 0x17, 0x89, 0x80, 0x24, 0x2b, 0x21,
	0x89, 0xee, 0x45, 0x5a, 0xe7, 0x1f, 0xd5, 0xa5,
	0x16, 0xa4, 0xda, 0x70, 0x7e, 0xe9, 0x4f, 0x24,
	0x61, 0x97, 0xab, 0xa0, 0xe0, 0xe7, 0xb8, 0x5c,
	0x0f, 0x25, 0x17, 0x37, 0x75, 0x12, 0xb5, 0x40,
	0xde, 0x1c, 0x0d, 0x8a, 0x77, 0x62, 0x3c, 0x86,
	0xd9, 0x70, 0x2e, 0x96, 0x30, 0xd2, 0x55, 0xb3,
	0x6b, 0xc3, 0xf2, 0x9c, 0x47, 0xf3, 0x3a, 0x24,
	0x52, 0xc6, 0x38, 0xd8, 0x22, 0xb3, 0x0c, 0xfd,
	0x2f, 0xa3, 0x3c, 0xb5, 0xe8, 0x26, 0xe1, 0xa3,
	0xad, 0xb0, 0x82, 0x17, 0xc1, 0x53, 0xb8, 0x34,
	0x48, 0xee, 0x39, 0xae, 0x51, 0x43, 0xec, 0x82,
	0xce, 0x87, 0xc6, 0x76, 0xb9, 0x76, 0xd3, 0x53,
	0xfe, 0x49, 0x24, 0x7d, 0x02, 0x42, 0x2b, 0x72,
	0xfb, 0xcb, 0xd8, 0x96, 0x02, 0xc6, 0x9a, 0x20,
	0xf3, 0x5a, 0x67, 0xe8, 0x13, 0xf8, 0xb2, 0xcb,
	0xa2, 0xec, 0x18, 0x20, 0x4a, 0xb0, 0x73, 0x53,
	0x21, 0xb0, 0x77, 0x53, 0xd8, 0x76, 0xa1, 0x30,
	0x17, 0x72, 0x2e, 0x33, 0x5f, 0x33, 0x6b, 0x28,
	0xfb, 0xb0, 0xf4, 0xec, 0x8e, 0xed, 0x20, 0x7d,
	0x57, 0x8c, 0x74, 0x28, 0x64, 0x8b, 0xeb, 0x59,
	0x38, 0x3f, 0xe7, 0x83, 0x2e, 0xe5, 0x64, 0x4d,
	0x5c, 0x1f, 0xe1, 0x3b, 0xd9, 0x84, 0xdb, 0xc9,
	0xec, 0xd8, 0xc1, 0x7c, 0x1f, 0x1b, 0x68, 0x35,
	0xc6, 0x34, 0x10, 0xef, 0x19, 0xc9, 0x0a, 0xd6,
	0x43, 0x7f, 0xa6, 0xcb, 0x9d, 0xf4, 0xf0, 0x16,
	0xb1, 0xb1, 0x96, 0x64, 0xec, 0x8d, 0x22, 0x4c,
	0x4b, 0xe8, 0x1a, 0xba, 0x6f, 0xb7, 0xfc, 0xa5,
	0x69, 0x3e, 0xad, 0x78, 0x79, 0x19, 0xb5, 0x04,
	0x69, 0xe5, 0x3f, 0xff, 0x60, 0x8c, 0xda, 0x0b,
	0x7b, 0xf7, 0xe7, 0xe6, 0x29, 0x3a, 0x85, 0xba,
	0xb5, 0xb0, 0x35, 0xbd, 0x38, 0xce, 0x34, 0x5e,
	0xf2, 0xdc, 0xd1, 0x8f, 0xc3, 0x03, 0x24, 0xa2,
	0x03, 0xf7, 0x4e, 0x49, 0x5b, 0xcf, 0x6d, 0xb0,
	0xeb, 0xe3, 0x30, 0x28, 0xd5, 0x5b, 0x82, 0x5f,
	0xe4, 0x7c, 0x1e, 0xec, 0xd2, 0x39, 0xf9, 0x6f,
	0x2e, 0xb3, 0xcd, 0x01, 0xb1, 0x67, 0xaa, 0xea,
	0xaa, 0xb3, 0x63, 0xaf, 0xd9, 0xb2, 0x1f, 0xba,
	0x05, 0x20, 0xeb, 0x19, 0x32, 0xf0, 0x6c, 0x3f,
	0x40, 0xcc, 0x93, 0xb3, 0xd8, 0x25, 0xa6, 0xe4,
	0xce, 0xd7, 0x7e, 0x48, 0x99, 0x65, 0x7f, 0x86,
	0xc5, 0xd4, 0x79, 0x6b, 0xab, 0x43, 0xb8, 0x6b,
	0xf1, 0x2f, 0xea, 0x4c, 0x5e, 0xf0, 0x3b, 0xb4,
	0xb8, 0xb0, 0x94, 0x0c, 0x6b, 0xe7, 0x22, 0x93,
	0xaa, 0x01, 0xcb, 0xf1, 0x11, 0x60, 0xf6, 0x69,
	0xcf, 0x14, 0xde, 0xfb, 0x90, 0x05, 0x27, 0x0c,
	0x1a, 0x9e, 0xf0, 0xb4, 0xc6, 0xa1, 0xe8, 0xdd,
	0xd0, 0x4c, 0x25, 0x4f, 0x9c, 0xb7, 0xb1, 0xb0,
	0x21, 0xdb, 0x87, 0x09, 0x03, 0xf2, 0xb3
};
static const u8 key70[] __initconst = {
	0x3b, 0x5b, 0x59, 0x36, 0x44, 0xd1, 0xba, 0x71,
	0x55, 0x87, 0x4d, 0x62, 0x3d, 0xc2, 0xfc, 0xaa,
	0x3f, 0x4e, 0x1a, 0xe4, 0xca, 0x09, 0xfc, 0x6a,
	0xb2, 0xd6, 0x5d, 0x79, 0xf9, 0x1a, 0x91, 0xa7
};
enum { nonce70 = 0x3fd6786dd147a85ULL };

static const u8 input71[] __initconst = {
	0x18, 0x78, 0xd6, 0x79, 0xe4, 0x9a, 0x6c, 0x73,
	0x17, 0xd4, 0x05, 0x0f, 0x1e, 0x9f, 0xd9, 0x2b,
	0x86, 0x48, 0x7d, 0xf4, 0xd9, 0x1c, 0x76, 0xfc,
	0x8e, 0x22, 0x34, 0xe1, 0x48, 0x4a, 0x8d, 0x79,
	0xb7, 0xbb, 0x88, 0xab, 0x90, 0xde, 0xc5, 0xb4,
	0xb4, 0xe7, 0x85, 0x49, 0xda, 0x57, 0xeb, 0xc9,
	0xcd, 0x21, 0xfc, 0x45, 0x6e, 0x32, 0x67, 0xf2,
	0x4f, 0xa6, 0x54, 0xe5, 0x20, 0xed, 0xcf, 0xc6,
	0x62, 0x25, 0x8e, 0x00, 0xf8, 0x6b, 0xa2, 0x80,
	0xac, 0x88, 0xa6, 0x59, 0x27, 0x83, 0x95, 0x11,
	0x3f, 0x70, 0x5e, 0x3f, 0x11, 0xfb, 0x26, 0xbf,
	0xe1, 0x48, 0x75, 0xf9, 0x86, 0xbf, 0xa6, 0x5d,
	0x15, 0x61, 0x66, 0xbf, 0x78, 0x8f, 0x6b, 0x9b,
	0xda, 0x98, 0xb7, 0x19, 0xe2, 0xf2, 0xa3, 0x9c,
	0x7c, 0x6a, 0x9a, 0xd8, 0x3d, 0x4c, 0x2c, 0xe1,
	0x09, 0xb4, 0x28, 0x82, 0x4e, 0xab, 0x0c, 0x75,
	0x63, 0xeb, 0xbc, 0xd0, 0x71, 0xa2, 0x73, 0x85,
	0xed, 0x53, 0x7a, 0x3f, 0x68, 0x9f, 0xd0, 0xa9,
	0x00, 0x5a, 0x9e, 0x80, 0x55, 0x00, 0xe6, 0xae,
	0x0c, 0x03, 0x40, 0xed, 0xfc, 0x68, 0x4a, 0xb7,
	0x1e, 0x09, 0x65, 0x30, 0x5a, 0x3d, 0x97, 0x4d,
	0x5e, 0x51, 0x8e, 0xda, 0xc3, 0x55, 0x8c, 0xfb,
	0xcf, 0x83, 0x05, 0x35, 0x0d, 0x08, 0x1b, 0xf3,
	0x3a, 0x57, 0x96, 0xac, 0x58, 0x8b, 0xfa, 0x00,
	0x49, 0x15, 0x78, 0xd2, 0x4b, 0xed, 0xb8, 0x59,
	0x78, 0x9b, 0x7f, 0xaa, 0xfc, 0xe7, 0x46, 0xdc,
	0x7b, 0x34, 0xd0, 0x34, 0xe5, 0x10, 0xff, 0x4d,
	0x5a, 0x4d, 0x60, 0xa7, 0x16, 0x54, 0xc4, 0xfd,
	0xca, 0x5d, 0x68, 0xc7, 0x4a, 0x01, 0x8d, 0x7f,
	0x74, 0x5d, 0xff, 0xb8, 0x37, 0x15, 0x62, 0xfa,
	0x44, 0x45, 0xcf, 0x77, 0x3b, 0x1d, 0xb2, 0xd2,
	0x0d, 0x42, 0x00, 0x39, 0x68, 0x1f, 0xcc, 0x89,
	0x73, 0x5d, 0xa9, 0x2e, 0xfd, 0x58, 0x62, 0xca,
	0x35, 0x8e, 0x70, 0x70, 0xaa, 0x6e, 0x14, 0xe9,
	0xa4, 0xe2, 0x10, 0x66, 0x71, 0xdc, 0x4c, 0xfc,
	0xa9, 0xdc, 0x8f, 0x57, 0x4d, 0xc5, 0xac, 0xd7,
	0xa9, 0xf3, 0xf3, 0xa1, 0xff, 0x62, 0xa0, 0x8f,
	0xe4, 0x96, 0x3e, 0xcb, 0x9f, 0x76, 0x42, 0x39,
	0x1f, 0x24, 0xfd, 0xfd, 0x79, 0xe8, 0x27, 0xdf,
	0xa8, 0xf6, 0x33, 0x8b, 0x31, 0x59, 0x69, 0xcf,
	0x6a, 0xef, 0x89, 0x4d, 0xa7, 0xf6, 0x7e, 0x97,
	0x14, 0xbd, 0xda, 0xdd, 0xb4, 0x84, 0x04, 0x24,
	0xe0, 0x17, 0xe1, 0x0f, 0x1f, 0x8a, 0x6a, 0x71,
	0x74, 0x41, 0xdc, 0x59, 0x5c, 0x8f, 0x01, 0x25,
	0x92, 0xf0, 0x2e, 0x15, 0x62, 0x71, 0x9a, 0x9f,
	0x87, 0xdf, 0x62, 0x49, 0x7f, 0x86, 0x62, 0xfc,
	0x20, 0x84, 0xd7, 0xe3, 0x3a, 0xd9, 0x37, 0x85,
	0xb7, 0x84, 0x5a, 0xf9, 0xed, 0x21, 0x32, 0x94,
	0x3e, 0x04, 0xe7, 0x8c, 0x46, 0x76, 0x21, 0x67,
	0xf6, 0x95, 0x64, 0x92, 0xb7, 0x15, 0xf6, 0xe3,
	0x41, 0x27, 0x9d, 0xd7, 0xe3, 0x79, 0x75, 0x92,
	0xd0, 0xc1, 0xf3, 0x40, 0x92, 0x08, 0xde, 0x90,
	0x22, 0x82, 0xb2, 0x69, 0xae, 0x1a, 0x35, 0x11,
	0x89, 0xc8, 0x06, 0x82, 0x95, 0x23, 0x44, 0x08,
	0x22, 0xf2, 0x71, 0x73, 0x1b, 0x88, 0x11, 0xcf,
	0x1c, 0x7e, 0x8a, 0x2e, 0xdc, 0x79, 0x57, 0xce,
	0x1f, 0xe7, 0x6c, 0x07, 0xd8, 0x06, 0xbe, 0xec,
	0xa3, 0xcf, 0xf9, 0x68, 0xa5, 0xb8, 0xf0, 0xe3,
	0x3f, 0x01, 0x92, 0xda, 0xf1, 0xa0, 0x2d, 0x7b,
	0xab, 0x57, 0x58, 0x2a, 0xaf, 0xab, 0xbd, 0xf2,
	0xe5, 0xaf, 0x7e, 0x1f, 0x46, 0x24, 0x9e, 0x20,
	0x22, 0x0f, 0x84, 0x4c, 0xb7, 0xd8, 0x03, 0xe8,
	0x09, 0x73, 0x6c, 0xc6, 0x9b, 0x90, 0xe0, 0xdb,
	0xf2, 0x71, 0xba, 0xad, 0xb3, 0xec, 0xda, 0x7a
};
static const u8 output71[] __initconst = {
	0x28, 0xc5, 0x9b, 0x92, 0xf9, 0x21, 0x4f, 0xbb,
	0xef, 0x3b, 0xf0, 0xf5, 0x3a, 0x6d, 0x7f, 0xd6,
	0x6a, 0x8d, 0xa1, 0x01, 0x5c, 0x62, 0x20, 0x8b,
	0x5b, 0x39, 0xd5, 0xd3, 0xc2, 0xf6, 0x9d, 0x5e,
	0xcc, 0xe1, 0xa2, 0x61, 0x16, 0xe2, 0xce, 0xe9,
	0x86, 0xd0, 0xfc, 0xce, 0x9a, 0x28, 0x27, 0xc4,
	0x0c, 0xb9, 0xaa, 0x8d, 0x48, 0xdb, 0xbf, 0x82,
	0x7d, 0xd0, 0x35, 0xc4, 0x06, 0x34, 0xb4, 0x19,
	0x51, 0x73, 0xf4, 0x7a, 0xf4, 0xfd, 0xe9, 0x1d,
	0xdc, 0x0f, 0x7e, 0xf7, 0x96, 0x03, 0xe3, 0xb1,
	0x2e, 0x22, 0x59, 0xb7, 0x6d, 0x1c, 0x97, 0x8c,
	0xd7, 0x31, 0x08, 0x26, 0x4c, 0x6d, 0xc6, 0x14,
	0xa5, 0xeb, 0x45, 0x6a, 0x88, 0xa3, 0xa2, 0x36,
	0xc4, 0x35, 0xb1, 0x5a, 0xa0, 0xad, 0xf7, 0x06,
	0x9b, 0x5d, 0xc1, 0x15, 0xc1, 0xce, 0x0a, 0xb0,
	0x57, 0x2e, 0x3f, 0x6f, 0x0d, 0x10, 0xd9, 0x11,
	0x2c, 0x9c, 0xad, 0x2d, 0xa5, 0x81, 0xfb, 0x4e,
	0x8f, 0xd5, 0x32, 0x4e, 0xaf, 0x5c, 0xc1, 0x86,
	0xde, 0x56, 0x5a, 0x33, 0x29, 0xf7, 0x67, 0xc6,
	0x37, 0x6f, 0xb2, 0x37, 0x4e, 0xd4, 0x69, 0x79,
	0xaf, 0xd5, 0x17, 0x79, 0xe0, 0xba, 0x62, 0xa3,
	0x68, 0xa4, 0x87, 0x93, 0x8d, 0x7e, 0x8f, 0xa3,
	0x9c, 0xef, 0xda, 0xe3, 0xa5, 0x1f, 0xcd, 0x30,
	0xa6, 0x55, 0xac, 0x4c, 0x69, 0x74, 0x02, 0xc7,
	0x5d, 0x95, 0x81, 0x4a, 0x68, 0x11, 0xd3, 0xa9,
	0x98, 0xb1, 0x0b, 0x0d, 0xae, 0x40, 0x86, 0x65,
	0xbf, 0xcc, 0x2d, 0xef, 0x57, 0xca, 0x1f, 0xe4,
	0x34, 0x4e, 0xa6, 0x5e, 0x82, 0x6e, 0x61, 0xad,
	0x0b, 0x3c, 0xf8, 0xeb, 0x01, 0x43, 0x7f, 0x87,
	0xa2, 0xa7, 0x6a, 0xe9, 0x62, 0x23, 0x24, 0x61,
	0xf1, 0xf7, 0x36, 0xdb, 0x10, 0xe5, 0x57, 0x72,
	0x3a, 0xc2, 0xae, 0xcc, 0x75, 0xc7, 0x80, 0x05,
	0x0a, 0x5c, 0x4c, 0x95, 0xda, 0x02, 0x01, 0x14,
	0x06, 0x6b, 0x5c, 0x65, 0xc2, 0xb8, 0x4a, 0xd6,
	0xd3, 0xb4, 0xd8, 0x12, 0x52, 0xb5, 0x60, 0xd3,
	0x8e, 0x5f, 0x5c, 0x76, 0x33, 0x7a, 0x05, 0xe5,
	0xcb, 0xef, 0x4f, 0x89, 0xf1, 0xba, 0x32, 0x6f,
	0x33, 0xcd, 0x15, 0x8d, 0xa3, 0x0c, 0x3f, 0x63,
	0x11, 0xe7, 0x0e, 0xe0, 0x00, 0x01, 0xe9, 0xe8,
	0x8e, 0x36, 0x34, 0x8d, 0x96, 0xb5, 0x03, 0xcf,
	0x55, 0x62, 0x49, 0x7a, 0x34, 0x44, 0xa5, 0xee,
	0x8c, 0x46, 0x06, 0x22, 0xab, 0x1d, 0x53, 0x9c,
	0xa1, 0xf9, 0x67, 0x18, 0x57, 0x89, 0xf9, 0xc2,
	0xd1, 0x7e, 0xbe, 0x36, 0x40, 0xcb, 0xe9, 0x04,
	0xde, 0xb1, 0x3b, 0x29, 0x52, 0xc5, 0x9a, 0xb5,
	0xa2, 0x7c, 0x7b, 0xfe, 0xe5, 0x92, 0x73, 0xea,
	0xea, 0x7b, 0xba, 0x0a, 0x8c, 0x88, 0x15, 0xe6,
	0x53, 0xbf, 0x1c, 0x33, 0xf4, 0x9b, 0x9a, 0x5e,
	0x8d, 0xae, 0x60, 0xdc, 0xcb, 0x5d, 0xfa, 0xbe,
	0x06, 0xc3, 0x3f, 0x06, 0xe7, 0x00, 0x40, 0x7b,
	0xaa, 0x94, 0xfa, 0x6d, 0x1f, 0xe4, 0xc5, 0xa9,
	0x1b, 0x5f, 0x36, 0xea, 0x5a, 0xdd, 0xa5, 0x48,
	0x6a, 0x55, 0xd2, 0x47, 0x28, 0xbf, 0x96, 0xf1,
	0x9f, 0xb6, 0x11, 0x4b, 0xd3, 0x44, 0x7d, 0x48,
	0x41, 0x61, 0xdb, 0x12, 0xd4, 0xc2, 0x59, 0x82,
	0x4c, 0x47, 0x5c, 0x04, 0xf6, 0x7b, 0xd3, 0x92,
	0x2e, 0xe8, 0x40, 0xef, 0x15, 0x32, 0x97, 0xdc,
	0x35, 0x4c, 0x6e, 0xa4, 0x97, 0xe9, 0x24, 0xde,
	0x63, 0x8b, 0xb1, 0x6b, 0x48, 0xbb, 0x46, 0x1f,
	0x84, 0xd6, 0x17, 0xb0, 0x5a, 0x4a, 0x4e, 0xd5,
	0x31, 0xd7, 0xcf, 0xa0, 0x39, 0xc6, 0x2e, 0xfc,
	0xa6, 0xa3, 0xd3, 0x0f, 0xa4, 0x28, 0xac, 0xb2,
	0xf4, 0x48, 0x8d, 0x50, 0xa5, 0x1c, 0x44, 0x5d,
	0x6e, 0x38, 0xb7, 0x2b, 0x8a, 0x45, 0xa7, 0x3d
};
static const u8 key71[] __initconst = {
	0x8b, 0x68, 0xc4, 0xb7, 0x0d, 0x81, 0xef, 0x52,
	0x1e, 0x05, 0x96, 0x72, 0x62, 0x89, 0x27, 0x83,
	0xd0, 0xc7, 0x33, 0x6d, 0xf2, 0xcc, 0x69, 0xf9,
	0x23, 0xae, 0x99, 0xb1, 0xd1, 0x05, 0x4e, 0x54
};
enum { nonce71 = 0x983f03656d64b5f6ULL };

static const u8 input72[] __initconst = {
	0x6b, 0x09, 0xc9, 0x57, 0x3d, 0x79, 0x04, 0x8c,
	0x65, 0xad, 0x4a, 0x0f, 0xa1, 0x31, 0x3a, 0xdd,
	0x14, 0x8e, 0xe8, 0xfe, 0xbf, 0x42, 0x87, 0x98,
	0x2e, 0x8d, 0x83, 0xa3, 0xf8, 0x55, 0x3d, 0x84,
	0x1e, 0x0e, 0x05, 0x4a, 0x38, 0x9e, 0xe7, 0xfe,
	0xd0, 0x4d, 0x79, 0x74, 0x3a, 0x0b, 0x9b, 0xe1,
	0xfd, 0x51, 0x84, 0x4e, 0xb2, 0x25, 0xe4, 0x64,
	0x4c, 0xda, 0xcf, 0x46, 0xec, 0xba, 0x12, 0xeb,
	0x5a, 0x33, 0x09, 0x6e, 0x78, 0x77, 0x8f, 0x30,
	0xb1, 0x7d, 0x3f, 0x60, 0x8c, 0xf2, 0x1d, 0x8e,
	0xb4, 0x70, 0xa2, 0x90, 0x7c, 0x79, 0x1a, 0x2c,
	0xf6, 0x28, 0x79, 0x7c, 0x53, 0xc5, 0xfa, 0xcc,
	0x65, 0x9b, 0xe1, 0x51, 0xd1, 0x7f, 0x1d, 0xc4,
	0xdb, 0xd4, 0xd9, 0x04, 0x61, 0x7d, 0xbe, 0x12,
	0xfc, 0xcd, 0xaf, 0xe4, 0x0f, 0x9c, 0x20, 0xb5,
	0x22, 0x40, 0x18, 0xda, 0xe4, 0xda, 0x8c, 0x2d,
	0x84, 0xe3, 0x5f, 0x53, 0x17, 0xed, 0x78, 0xdc,
	0x2f, 0xe8, 0x31, 0xc7, 0xe6, 0x39, 0x71, 0x40,
	0xb4, 0x0f, 0xc9, 0xa9, 0x7e, 0x78, 0x87, 0xc1,
	0x05, 0x78, 0xbb, 0x01, 0xf2, 0x8f, 0x33, 0xb0,
	0x6e, 0x84, 0xcd, 0x36, 0x33, 0x5c, 0x5b, 0x8e,
	0xf1, 0xac, 0x30, 0xfe, 0x33, 0xec, 0x08, 0xf3,
	0x7e, 0xf2, 0xf0, 0x4c, 0xf2, 0xad, 0xd8, 0xc1,
	0xd4, 0x4e, 0x87, 0x06, 0xd4, 0x75, 0xe7, 0xe3,
	0x09, 0xd3, 0x4d, 0xe3, 0x21, 0x32, 0xba, 0xb4,
	0x68, 0x68, 0xcb, 0x4c, 0xa3, 0x1e, 0xb3, 0x87,
	0x7b, 0xd3, 0x0c, 0x63, 0x37, 0x71, 0x79, 0xfb,
	0x58, 0x36, 0x57, 0x0f, 0x34, 0x1d, 0xc1, 0x42,
	0x02, 0x17, 0xe7, 0xed, 0xe8, 0xe7, 0x76, 0xcb,
	0x42, 0xc4, 0x4b, 0xe2, 0xb2, 0x5e, 0x42, 0xd5,
	0xec, 0x9d, 0xc1, 0x32, 0x71, 0xe4, 0xeb, 0x10,
	0x68, 0x1a, 0x6e, 0x99, 0x8e, 0x73, 0x12, 0x1f,
	0x97, 0x0c, 0x9e, 0xcd, 0x02, 0x3e, 0x4c, 0xa0,
	0xf2, 0x8d, 0xe5, 0x44, 0xca, 0x6d, 0xfe, 0x07,
	0xe3, 0xe8, 0x9b, 0x76, 0xc1, 0x6d, 0xb7, 0x6e,
	0x0d, 0x14, 0x00, 0x6f, 0x8a, 0xfd, 0x43, 0xc6,
	0x43, 0xa5, 0x9c, 0x02, 0x47, 0x10, 0xd4, 0xb4,
	0x9b, 0x55, 0x67, 0xc8, 0x7f, 0xc1, 0x8a, 0x1f,
	0x1e, 0xd1, 0xbc, 0x99, 0x5d, 0x50, 0x4f, 0x89,
	0xf1, 0xe6, 0x5d, 0x91, 0x40, 0xdc, 0x20, 0x67,
	0x56, 0xc2, 0xef, 0xbd, 0x2c, 0xa2, 0x99, 0x38,
	0xe0, 0x45, 0xec, 0x44, 0x05, 0x52, 0x65, 0x11,
	0xfc, 0x3b, 0x19, 0xcb, 0x71, 0xc2, 0x8e, 0x0e,
	0x03, 0x2a, 0x03, 0x3b, 0x63, 0x06, 0x31, 0x9a,
	0xac, 0x53, 0x04, 0x14, 0xd4, 0x80, 0x9d, 0x6b,
	0x42, 0x7e, 0x7e, 0x4e, 0xdc, 0xc7, 0x01, 0x49,
	0x9f, 0xf5, 0x19, 0x86, 0x13, 0x28, 0x2b, 0xa6,
	0xa6, 0xbe, 0xa1, 0x7e, 0x71, 0x05, 0x00, 0xff,
	0x59, 0x2d, 0xb6, 0x63, 0xf0, 0x1e, 0x2e, 0x69,
	0x9b, 0x85, 0xf1, 0x1e, 0x8a, 0x64, 0x39, 0xab,
	0x00, 0x12, 0xe4, 0x33, 0x4b, 0xb5, 0xd8, 0xb3,
	0x6b, 0x5b, 0x8b, 0x5c, 0xd7, 0x6f, 0x23, 0xcf,
	0x3f, 0x2e, 0x5e, 0x47, 0xb9, 0xb8, 0x1f, 0xf0,
	0x1d, 0xda, 0xe7, 0x4f, 0x6e, 0xab, 0xc3, 0x36,
	0xb4, 0x74, 0x6b, 0xeb, 0xc7, 0x5d, 0x91, 0xe5,
	0xda, 0xf2, 0xc2, 0x11, 0x17, 0x48, 0xf8, 0x9c,
	0xc9, 0x8b, 0xc1, 0xa2, 0xf4, 0xcd, 0x16, 0xf8,
	0x27, 0xd9, 0x6c, 0x6f, 0xb5, 0x8f, 0x77, 0xca,
	0x1b, 0xd8, 0xef, 0x84, 0x68, 0x71, 0x53, 0xc1,
	0x43, 0x0f, 0x9f, 0x98, 0xae, 0x7e, 0x31, 0xd2,
	0x98, 0xfb, 0x20, 0xa2, 0xad, 0x00, 0x10, 0x83,
	0x00, 0x8b, 0xeb, 0x56, 0xd2, 0xc4, 0xcc, 0x7f,
	0x2f, 0x4e, 0xfa, 0x88, 0x13, 0xa4, 0x2c, 0xde,
	0x6b, 0x77, 0x86, 0x10, 0x6a, 0xab, 0x43, 0x0a,
	0x02
};
static const u8 output72[] __initconst = {
	0x42, 0x89, 0xa4, 0x80, 0xd2, 0xcb, 0x5f, 0x7f,
	0x2a, 0x1a, 0x23, 0x00, 0xa5, 0x6a, 0x95, 0xa3,
	0x9a, 0x41, 0xa1, 0xd0, 0x2d, 0x1e, 0xd6, 0x13,
	0x34, 0x40, 0x4e, 0x7f, 0x1a, 0xbe, 0xa0, 0x3d,
	0x33, 0x9c, 0x56, 0x2e, 0x89, 0x25, 0x45, 0xf9,
	0xf0, 0xba, 0x9c, 0x6d, 0xd1, 0xd1, 0xde, 0x51,
	0x47, 0x63, 0xc9, 0xbd, 0xfa, 0xa2, 0x9e, 0xad,
	0x6a, 0x7b, 0x21, 0x1a, 0x6c, 0x3e, 0xff, 0x46,
	0xbe, 0xf3, 0x35, 0x7a, 0x6e, 0xb3, 0xb9, 0xf7,
	0xda, 0x5e, 0xf0, 0x14, 0xb5, 0x70, 0xa4, 0x2b,
	0xdb, 0xbb, 0xc7, 0x31, 0x4b, 0x69, 0x5a, 0x83,
	0x70, 0xd9, 0x58, 0xd4, 0x33, 0x84, 0x23, 0xf0,
	0xae, 0xbb, 0x6d, 0x26, 0x7c, 0xc8, 0x30, 0xf7,
	0x24, 0xad, 0xbd, 0xe4, 0x2c, 0x38, 0x38, 0xac,
	0xe1, 0x4a, 0x9b, 0xac, 0x33, 0x0e, 0x4a, 0xf4,
	0x93, 0xed, 0x07, 0x82, 0x81, 0x4f, 0x8f, 0xb1,
	0xdd, 0x73, 0xd5, 0x50, 0x6d, 0x44, 0x1e, 0xbe,
	0xa7, 0xcd, 0x17, 0x57, 0xd5, 0x3b, 0x62, 0x36,
	0xcf, 0x7d, 0xc8, 0xd8, 0xd1, 0x78, 0xd7, 0x85,
	0x46, 0x76, 0x5d, 0xcc, 0xfe, 0xe8, 0x94, 0xc5,
	0xad, 0xbc, 0x5e, 0xbc, 0x8d, 0x1d, 0xdf, 0x03,
	0xc9, 0x6b, 0x1b, 0x81, 0xd1, 0xb6, 0x5a, 0x24,
	0xe3, 0xdc, 0x3f, 0x20, 0xc9, 0x07, 0x73, 0x4c,
	0x43, 0x13, 0x87, 0x58, 0x34, 0x0d, 0x14, 0x63,
	0x0f, 0x6f, 0xad, 0x8d, 0xac, 0x7c, 0x67, 0x68,
	0xa3, 0x9d, 0x7f, 0x00, 0xdf, 0x28, 0xee, 0x67,
	0xf4, 0x5c, 0x26, 0xcb, 0xef, 0x56, 0x71, 0xc8,
	0xc6, 0x67, 0x5f, 0x38, 0xbb, 0xa0, 0xb1, 0x5c,
	0x1f, 0xb3, 0x08, 0xd9, 0x38, 0xcf, 0x74, 0x54,
	0xc6, 0xa4, 0xc4, 0xc0, 0x9f, 0xb3, 0xd0, 0xda,
	0x62, 0x67, 0x8b, 0x81, 0x33, 0xf0, 0xa9, 0x73,
	0xa4, 0xd1, 0x46, 0x88, 0x8d, 0x85, 0x12, 0x40,
	0xba, 0x1a, 0xcd, 0x82, 0xd8, 0x8d, 0xc4, 0x52,
	0xe7, 0x01, 0x94, 0x2e, 0x0e, 0xd0, 0xaf, 0xe7,
	0x2d, 0x3f, 0x3c, 0xaa, 0xf4, 0xf5, 0xa7, 0x01,
	0x4c, 0x14, 0xe2, 0xc2, 0x96, 0x76, 0xbe, 0x05,
	0xaa, 0x19, 0xb1, 0xbd, 0x95, 0xbb, 0x5a, 0xf9,
	0xa5, 0xa7, 0xe6, 0x16, 0x38, 0x34, 0xf7, 0x9d,
	0x19, 0x66, 0x16, 0x8e, 0x7f, 0x2b, 0x5a, 0xfb,
	0xb5, 0x29, 0x79, 0xbf, 0x52, 0xae, 0x30, 0x95,
	0x3f, 0x31, 0x33, 0x28, 0xde, 0xc5, 0x0d, 0x55,
	0x89, 0xec, 0x21, 0x11, 0x0f, 0x8b, 0xfe, 0x63,
	0x3a, 0xf1, 0x95, 0x5c, 0xcd, 0x50, 0xe4, 0x5d,
	0x8f, 0xa7, 0xc8, 0xca, 0x93, 0xa0, 0x67, 0x82,
	0x63, 0x5c, 0xd0, 0xed, 0xe7, 0x08, 0xc5, 0x60,
	0xf8, 0xb4, 0x47, 0xf0, 0x1a, 0x65, 0x4e, 0xa3,
	0x51, 0x68, 0xc7, 0x14, 0xa1, 0xd9, 0x39, 0x72,
	0xa8, 0x6f, 0x7c, 0x7e, 0xf6, 0x03, 0x0b, 0x25,
	0x9b, 0xf2, 0xca, 0x49, 0xae, 0x5b, 0xf8, 0x0f,
	0x71, 0x51, 0x01, 0xa6, 0x23, 0xa9, 0xdf, 0xd0,
	0x7a, 0x39, 0x19, 0xf5, 0xc5, 0x26, 0x44, 0x7b,
	0x0a, 0x4a, 0x41, 0xbf, 0xf2, 0x8e, 0x83, 0x50,
	0x91, 0x96, 0x72, 0x02, 0xf6, 0x80, 0xbf, 0x95,
	0x41, 0xac, 0xda, 0xb0, 0xba, 0xe3, 0x76, 0xb1,
	0x9d, 0xff, 0x1f, 0x33, 0x02, 0x85, 0xfc, 0x2a,
	0x29, 0xe6, 0xe3, 0x9d, 0xd0, 0xef, 0xc2, 0xd6,
	0x9c, 0x4a, 0x62, 0xac, 0xcb, 0xea, 0x8b, 0xc3,
	0x08, 0x6e, 0x49, 0x09, 0x26, 0x19, 0xc1, 0x30,
	0xcc, 0x27, 0xaa, 0xc6, 0x45, 0x88, 0xbd, 0xae,
	0xd6, 0x79, 0xff, 0x4e, 0xfc, 0x66, 0x4d, 0x02,
	0xa5, 0xee, 0x8e, 0xa5, 0xb6, 0x15, 0x72, 0x24,
	0xb1, 0xbf, 0xbf, 0x64, 0xcf, 0xcc, 0x93, 0xe9,
	0xb6, 0xfd, 0xb4, 0xb6, 0x21, 0xb5, 0x48, 0x08,
	0x0f, 0x11, 0x65, 0xe1, 0x47, 0xee, 0x93, 0x29,
	0xad
};
static const u8 key72[] __initconst = {
	0xb9, 0xa2, 0xfc, 0x59, 0x06, 0x3f, 0x77, 0xa5,
	0x66, 0xd0, 0x2b, 0x22, 0x74, 0x22, 0x4c, 0x1e,
	0x6a, 0x39, 0xdf, 0xe1, 0x0d, 0x4c, 0x64, 0x99,
	0x54, 0x8a, 0xba, 0x1d, 0x2c, 0x21, 0x5f, 0xc3
};
enum { nonce72 = 0x3d069308fa3db04bULL };

static const u8 input73[] __initconst = {
	0xe4, 0xdd, 0x36, 0xd4, 0xf5, 0x70, 0x51, 0x73,
	0x97, 0x1d, 0x45, 0x05, 0x92, 0xe7, 0xeb, 0xb7,
	0x09, 0x82, 0x6e, 0x25, 0x6c, 0x50, 0xf5, 0x40,
	0x19, 0xba, 0xbc, 0xf4, 0x39, 0x14, 0xc5, 0x15,
	0x83, 0x40, 0xbd, 0x26, 0xe0, 0xff, 0x3b, 0x22,
	0x7c, 0x7c, 0xd7, 0x0b, 0xe9, 0x25, 0x0c, 0x3d,
	0x92, 0x38, 0xbe, 0xe4, 0x22, 0x75, 0x65, 0xf1,
	0x03, 0x85, 0x34, 0x09, 0xb8, 0x77, 0xfb, 0x48,
	0xb1, 0x2e, 0x21, 0x67, 0x9b, 0x9d, 0xad, 0x18,
	0x82, 0x0d, 0x6b, 0xc3, 0xcf, 0x00, 0x61, 0x6e,
	0xda, 0xdc, 0xa7, 0x0b, 0x5c, 0x02, 0x1d, 0xa6,
	0x4e, 0x0d, 0x7f, 0x37, 0x01, 0x5a, 0x37, 0xf3,
	0x2b, 0xbf, 0xba, 0xe2, 0x1c, 0xb3, 0xa3, 0xbc,
	0x1c, 0x93, 0x1a, 0xb1, 0x71, 0xaf, 0xe2, 0xdd,
	0x17, 0xee, 0x53, 0xfa, 0xfb, 0x02, 0x40, 0x3e,
	0x03, 0xca, 0xe7, 0xc3, 0x51, 0x81, 0xcc, 0x8c,
	0xca, 0xcf, 0x4e, 0xc5, 0x78, 0x99, 0xfd, 0xbf,
	0xea, 0xab, 0x38, 0x81, 0xfc, 0xd1, 0x9e, 0x41,
	0x0b, 0x84, 0x25, 0xf1, 0x6b, 0x3c, 0xf5, 0x40,
	0x0d, 0xc4, 0x3e, 0xb3, 0x6a, 0xec, 0x6e, 0x75,
	0xdc, 0x9b, 0xdf, 0x08, 0x21, 0x16, 0xfb, 0x7a,
	0x8e, 0x19, 0x13, 0x02, 0xa7, 0xfc, 0x58, 0x21,
	0xc3, 0xb3, 0x59, 0x5a, 0x9c, 0xef, 0x38, 0xbd,
	0x87, 0x55, 0xd7, 0x0d, 0x1f, 0x84, 0xdc, 0x98,
	0x22, 0xca, 0x87, 0x96, 0x71, 0x6d, 0x68, 0x00,
	0xcb, 0x4f, 0x2f, 0xc4, 0x64, 0x0c, 0xc1, 0x53,
	0x0c, 0x90, 0xe7, 0x3c, 0x88, 0xca, 0xc5, 0x85,
	0xa3, 0x2a, 0x96, 0x7c, 0x82, 0x6d, 0x45, 0xf5,
	0xb7, 0x8d, 0x17, 0x69, 0xd6, 0xcd, 0x3c, 0xd3,
	0xe7, 0x1c, 0xce, 0x93, 0x50, 0xd4, 0x59, 0xa2,
	0xd8, 0x8b, 0x72, 0x60, 0x5b, 0x25, 0x14, 0xcd,
	0x5a, 0xe8, 0x8c, 0xdb, 0x23, 0x8d, 0x2b, 0x59,
	0x12, 0x13, 0x10, 0x47, 0xa4, 0xc8, 0x3c, 0xc1,
	0x81, 0x89, 0x6c, 0x98, 0xec, 0x8f, 0x7b, 0x32,
	0xf2, 0x87, 0xd9, 0xa2, 0x0d, 0xc2, 0x08, 0xf9,
	0xd5, 0xf3, 0x91, 0xe7, 0xb3, 0x87, 0xa7, 0x0b,
	0x64, 0x8f, 0xb9, 0x55, 0x1c, 0x81, 0x96, 0x6c,
	0xa1, 0xc9, 0x6e, 0x3b, 0xcd, 0x17, 0x1b, 0xfc,
	0xa6, 0x05, 0xba, 0x4a, 0x7d, 0x03, 0x3c, 0x59,
	0xc8, 0xee, 0x50, 0xb2, 0x5b, 0xe1, 0x4d, 0x6a,
	0x1f, 0x09, 0xdc, 0xa2, 0x51, 0xd1, 0x93, 0x3a,
	0x5f, 0x72, 0x1d, 0x26, 0x14, 0x62, 0xa2, 0x41,
	0x3d, 0x08, 0x70, 0x7b, 0x27, 0x3d, 0xbc, 0xdf,
	0x15, 0xfa, 0xb9, 0x5f, 0xb5, 0x38, 0x84, 0x0b,
	0x58, 0x3d, 0xee, 0x3f, 0x32, 0x65, 0x6d, 0xd7,
	0xce, 0x97, 0x3c, 0x8d, 0xfb, 0x63, 0xb9, 0xb0,
	0xa8, 0x4a, 0x72, 0x99, 0x97, 0x58, 0xc8, 0xa7,
	0xf9, 0x4c, 0xae, 0xc1, 0x63, 0xb9, 0x57, 0x18,
	0x8a, 0xfa, 0xab, 0xe9, 0xf3, 0x67, 0xe6, 0xfd,
	0xd2, 0x9d, 0x5c, 0xa9, 0x8e, 0x11, 0x0a, 0xf4,
	0x4b, 0xf1, 0xec, 0x1a, 0xaf, 0x50, 0x5d, 0x16,
	0x13, 0x69, 0x2e, 0xbd, 0x0d, 0xe6, 0xf0, 0xb2,
	0xed, 0xb4, 0x4c, 0x59, 0x77, 0x37, 0x00, 0x0b,
	0xc7, 0xa7, 0x9e, 0x37, 0xf3, 0x60, 0x70, 0xef,
	0xf3, 0xc1, 0x74, 0x52, 0x87, 0xc6, 0xa1, 0x81,
	0xbd, 0x0a, 0x2c, 0x5d, 0x2c, 0x0c, 0x6a, 0x81,
	0xa1, 0xfe, 0x26, 0x78, 0x6c, 0x03, 0x06, 0x07,
	0x34, 0xaa, 0xd1, 0x1b, 0x40, 0x03, 0x39, 0x56,
	0xcf, 0x2a, 0x92, 0xc1, 0x4e, 0xdf, 0x29, 0x24,
	0x83, 0x22, 0x7a, 0xea, 0x67, 0x1e, 0xe7, 0x54,
	0x64, 0xd3, 0xbd, 0x3a, 0x5d, 0xae, 0xca, 0xf0,
	0x9c, 0xd6, 0x5a, 0x9a, 0x62, 0xc8, 0xc7, 0x83,
	0xf9, 0x89, 0xde, 0x2d, 0x53, 0x64, 0x61, 0xf7,
	0xa3, 0xa7, 0x31, 0x38, 0xc6, 0x22, 0x9c, 0xb4,
	0x87, 0xe0
};
static const u8 output73[] __initconst = {
	0x34, 0xed, 0x05, 0xb0, 0x14, 0xbc, 0x8c, 0xcc,
	0x95, 0xbd, 0x99, 0x0f, 0xb1, 0x98, 0x17, 0x10,
	0xae, 0xe0, 0x08, 0x53, 0xa3, 0x69, 0xd2, 0xed,
	0x66, 0xdb, 0x2a, 0x34, 0x8d, 0x0c, 0x6e, 0xce,
	0x63, 0x69, 0xc9, 0xe4, 0x57, 0xc3, 0x0c, 0x8b,
	0xa6, 0x2c, 0xa7, 0xd2, 0x08, 0xff, 0x4f, 0xec,
	0x61, 0x8c, 0xee, 0x0d, 0xfa, 0x6b, 0xe0, 0xe8,
	0x71, 0xbc, 0x41, 0x46, 0xd7, 0x33, 0x1d, 0xc0,
	0xfd, 0xad, 0xca, 0x8b, 0x34, 0x56, 0xa4, 0x86,
	0x71, 0x62, 0xae, 0x5e, 0x3d, 0x2b, 0x66, 0x3e,
	0xae, 0xd8, 0xc0, 0xe1, 0x21, 0x3b, 0xca, 0xd2,
	0x6b, 0xa2, 0xb8, 0xc7, 0x98, 0x4a, 0xf3, 0xcf,
	0xb8, 0x62, 0xd8, 0x33, 0xe6, 0x80, 0xdb, 0x2f,
	0x0a, 0xaf, 0x90, 0x3c, 0xe1, 0xec, 0xe9, 0x21,
	0x29, 0x42, 0x9e, 0xa5, 0x50, 0xe9, 0x93, 0xd3,
	0x53, 0x1f, 0xac, 0x2a, 0x24, 0x07, 0xb8, 0xed,
	0xed, 0x38, 0x2c, 0xc4, 0xa1, 0x2b, 0x31, 0x5d,
	0x9c, 0x24, 0x7b, 0xbf, 0xd9, 0xbb, 0x4e, 0x87,
	0x8f, 0x32, 0x30, 0xf1, 0x11, 0x29, 0x54, 0x94,
	0x00, 0x95, 0x1d, 0x1d, 0x24, 0xc0, 0xd4, 0x34,
	0x49, 0x1d, 0xd5, 0xe3, 0xa6, 0xde, 0x8b, 0xbf,
	0x5a, 0x9f, 0x58, 0x5a, 0x9b, 0x70, 0xe5, 0x9b,
	0xb3, 0xdb, 0xe8, 0xb8, 0xca, 0x1b, 0x43, 0xe3,
	0xc6, 0x6f, 0x0a, 0xd6, 0x32, 0x11, 0xd4, 0x04,
	0xef, 0xa3, 0xe4, 0x3f, 0x12, 0xd8, 0xc1, 0x73,
	0x51, 0x87, 0x03, 0xbd, 0xba, 0x60, 0x79, 0xee,
	0x08, 0xcc, 0xf7, 0xc0, 0xaa, 0x4c, 0x33, 0xc4,
	0xc7, 0x09, 0xf5, 0x91, 0xcb, 0x74, 0x57, 0x08,
	0x1b, 0x90, 0xa9, 0x1b, 0x60, 0x02, 0xd2, 0x3f,
	0x7a, 0xbb, 0xfd, 0x78, 0xf0, 0x15, 0xf9, 0x29,
	0x82, 0x8f, 0xc4, 0xb2, 0x88, 0x1f, 0xbc, 0xcc,
	0x53, 0x27, 0x8b, 0x07, 0x5f, 0xfc, 0x91, 0x29,
	0x82, 0x80, 0x59, 0x0a, 0x3c, 0xea, 0xc4, 0x7e,
	0xad, 0xd2, 0x70, 0x46, 0xbd, 0x9e, 0x3b, 0x1c,
	0x8a, 0x62, 0xea, 0x69, 0xbd, 0xf6, 0x96, 0x15,
	0xb5, 0x57, 0xe8, 0x63, 0x5f, 0x65, 0x46, 0x84,
	0x58, 0x50, 0x87, 0x4b, 0x0e, 0x5b, 0x52, 0x90,
	0xb0, 0xae, 0x37, 0x0f, 0xdd, 0x7e, 0xa2, 0xa0,
	0x8b, 0x78, 0xc8, 0x5a, 0x1f, 0x53, 0xdb, 0xc5,
	0xbf, 0x73, 0x20, 0xa9, 0x44, 0xfb, 0x1e, 0xc7,
	0x97, 0xb2, 0x3a, 0x5a, 0x17, 0xe6, 0x8b, 0x9b,
	0xe8, 0xf8, 0x2a, 0x01, 0x27, 0xa3, 0x71, 0x28,
	0xe3, 0x19, 0xc6, 0xaf, 0xf5, 0x3a, 0x26, 0xc0,
	0x5c, 0x69, 0x30, 0x78, 0x75, 0x27, 0xf2, 0x0c,
	0x22, 0x71, 0x65, 0xc6, 0x8e, 0x7b, 0x47, 0xe3,
	0x31, 0xaf, 0x7b, 0xc6, 0xc2, 0x55, 0x68, 0x81,
	0xaa, 0x1b, 0x21, 0x65, 0xfb, 0x18, 0x35, 0x45,
	0x36, 0x9a, 0x44, 0xba, 0x5c, 0xff, 0x06, 0xde,
	0x3a, 0xc8, 0x44, 0x0b, 0xaa, 0x8e, 0x34, 0xe2,
	0x84, 0xac, 0x18, 0xfe, 0x9b, 0xe1, 0x4f, 0xaa,
	0xb6, 0x90, 0x0b, 0x1c, 0x2c, 0xd9, 0x9a, 0x10,
	0x18, 0xf9, 0x49, 0x41, 0x42, 0x1b, 0xb5, 0xe1,
	0x26, 0xac, 0x2d, 0x38, 0x00, 0x00, 0xe4, 0xb4,
	0x50, 0x6f, 0x14, 0x18, 0xd6, 0x3d, 0x00, 0x59,
	0x3c, 0x45, 0xf3, 0x42, 0x13, 0x44, 0xb8, 0x57,
	0xd4, 0x43, 0x5c, 0x8a, 0x2a, 0xb4, 0xfc, 0x0a,
	0x25, 0x5a, 0xdc, 0x8f, 0x11, 0x0b, 0x11, 0x44,
	0xc7, 0x0e, 0x54, 0x8b, 0x22, 0x01, 0x7e, 0x67,
	0x2e, 0x15, 0x3a, 0xb9, 0xee, 0x84, 0x10, 0xd4,
	0x80, 0x57, 0xd7, 0x75, 0xcf, 0x8b, 0xcb, 0x03,
	0xc9, 0x92, 0x2b, 0x69, 0xd8, 0x5a, 0x9b, 0x06,
	0x85, 0x47, 0xaa, 0x4c, 0x28, 0xde, 0x49, 0x58,
	0xe6, 0x11, 0x1e, 0x5e, 0x64, 0x8e, 0x3b, 0xe0,
	0x40, 0x2e, 0xac, 0x96, 0x97, 0x15, 0x37, 0x1e,
	0x30, 0xdd
};
static const u8 key73[] __initconst = {
	0x96, 0x06, 0x1e, 0xc1, 0x6d, 0xba, 0x49, 0x5b,
	0x65, 0x80, 0x79, 0xdd, 0xf3, 0x67, 0xa8, 0x6e,
	0x2d, 0x9c, 0x54, 0x46, 0xd8, 0x4a, 0xeb, 0x7e,
	0x23, 0x86, 0x51, 0xd8, 0x49, 0x49, 0x56, 0xe0
};
enum { nonce73 = 0xbefb83cb67e11ffdULL };

static const u8 input74[] __initconst = {
	0x47, 0x22, 0x70, 0xe5, 0x2f, 0x41, 0x18, 0x45,
	0x07, 0xd3, 0x6d, 0x32, 0x0d, 0x43, 0x92, 0x2b,
	0x9b, 0x65, 0x73, 0x13, 0x1a, 0x4f, 0x49, 0x8f,
	0xff, 0xf8, 0xcc, 0xae, 0x15, 0xab, 0x9d, 0x7d,
	0xee, 0x22, 0x5d, 0x8b, 0xde, 0x81, 0x5b, 0x81,
	0x83, 0x49, 0x35, 0x9b, 0xb4, 0xbc, 0x4e, 0x01,
	0xc2, 0x29, 0xa7, 0xf1, 0xca, 0x3a, 0xce, 0x3f,
	0xf5, 0x31, 0x93, 0xa8, 0xe2, 0xc9, 0x7d, 0x03,
	0x26, 0xa4, 0xbc, 0xa8, 0x9c, 0xb9, 0x68, 0xf3,
	0xb3, 0x91, 0xe8, 0xe6, 0xc7, 0x2b, 0x1a, 0xce,
	0xd2, 0x41, 0x53, 0xbd, 0xa3, 0x2c, 0x54, 0x94,
	0x21, 0xa1, 0x40, 0xae, 0xc9, 0x0c, 0x11, 0x92,
	0xfd, 0x91, 0xa9, 0x40, 0xca, 0xde, 0x21, 0x4e,
	0x1e, 0x3d, 0xcc, 0x2c, 0x87, 0x11, 0xef, 0x46,
	0xed, 0x52, 0x03, 0x11, 0x19, 0x43, 0x25, 0xc7,
	0x0d, 0xc3, 0x37, 0x5f, 0xd3, 0x6f, 0x0c, 0x6a,
	0x45, 0x30, 0x88, 0xec, 0xf0, 0x21, 0xef, 0x1d,
	0x7b, 0x38, 0x63, 0x4b, 0x49, 0x0c, 0x72, 0xf6,
	0x4c, 0x40, 0xc3, 0xcc, 0x03, 0xa7, 0xae, 0xa8,
	0x8c, 0x37, 0x03, 0x1c, 0x11, 0xae, 0x0d, 0x1b,
	0x62, 0x97, 0x27, 0xfc, 0x56, 0x4b, 0xb7, 0xfd,
	0xbc, 0xfb, 0x0e, 0xfc, 0x61, 0xad, 0xc6, 0xb5,
	0x9c, 0x8c, 0xc6, 0x38, 0x27, 0x91, 0x29, 0x3d,
	0x29, 0xc8, 0x37, 0xc9, 0x96, 0x69, 0xe3, 0xdc,
	0x3e, 0x61, 0x35, 0x9b, 0x99, 0x4f, 0xb9, 0x4e,
	0x5a, 0x29, 0x1c, 0x2e, 0xcf, 0x16, 0xcb, 0x69,
	0x87, 0xe4, 0x1a, 0xc4, 0x6e, 0x78, 0x43, 0x00,
	0x03, 0xb2, 0x8b, 0x03, 0xd0, 0xb4, 0xf1, 0xd2,
	0x7d, 0x2d, 0x7e, 0xfc, 0x19, 0x66, 0x5b, 0xa3,
	0x60, 0x3f, 0x9d, 0xbd, 0xfa, 0x3e, 0xca, 0x7b,
	0x26, 0x08, 0x19, 0x16, 0x93, 0x5d, 0x83, 0xfd,
	0xf9, 0x21, 0xc6, 0x31, 0x34, 0x6f, 0x0c, 0xaa,
	0x28, 0xf9, 0x18, 0xa2, 0xc4, 0x78, 0x3b, 0x56,
	0xc0, 0x88, 0x16, 0xba, 0x22, 0x2c, 0x07, 0x2f,
	0x70, 0xd0, 0xb0, 0x46, 0x35, 0xc7, 0x14, 0xdc,
	0xbb, 0x56, 0x23, 0x1e, 0x36, 0x36, 0x2d, 0x73,
	0x78, 0xc7, 0xce, 0xf3, 0x58, 0xf7, 0x58, 0xb5,
	0x51, 0xff, 0x33, 0x86, 0x0e, 0x3b, 0x39, 0xfb,
	0x1a, 0xfd, 0xf8, 0x8b, 0x09, 0x33, 0x1b, 0x83,
	0xf2, 0xe6, 0x38, 0x37, 0xef, 0x47, 0x84, 0xd9,
	0x82, 0x77, 0x2b, 0x82, 0xcc, 0xf9, 0xee, 0x94,
	0x71, 0x78, 0x81, 0xc8, 0x4d, 0x91, 0xd7, 0x35,
	0x29, 0x31, 0x30, 0x5c, 0x4a, 0x23, 0x23, 0xb1,
	0x38, 0x6b, 0xac, 0x22, 0x3f, 0x80, 0xc7, 0xe0,
	0x7d, 0xfa, 0x76, 0x47, 0xd4, 0x6f, 0x93, 0xa0,
	0xa0, 0x93, 0x5d, 0x68, 0xf7, 0x43, 0x25, 0x8f,
	0x1b, 0xc7, 0x87, 0xea, 0x59, 0x0c, 0xa2, 0xfa,
	0xdb, 0x2f, 0x72, 0x43, 0xcf, 0x90, 0xf1, 0xd6,
	0x58, 0xf3, 0x17, 0x6a, 0xdf, 0xb3, 0x4e, 0x0e,
	0x38, 0x24, 0x48, 0x1f, 0xb7, 0x01, 0xec, 0x81,
	0xb1, 0x87, 0x5b, 0xec, 0x9c, 0x11, 0x1a, 0xff,
	0xa5, 0xca, 0x5a, 0x63, 0x31, 0xb2, 0xe4, 0xc6,
	0x3c, 0x1d, 0xaf, 0x27, 0xb2, 0xd4, 0x19, 0xa2,
	0xcc, 0x04, 0x92, 0x42, 0xd2, 0xc1, 0x8c, 0x3b,
	0xce, 0xf5, 0x74, 0xc1, 0x81, 0xf8, 0x20, 0x23,
	0x6f, 0x20, 0x6d, 0x78, 0x36, 0x72, 0x2c, 0x52,
	0xdf, 0x5e, 0xe8, 0x75, 0xce, 0x1c, 0x49, 0x9d,
	0x93, 0x6f, 0x65, 0xeb, 0xb1, 0xbd, 0x8e, 0x5e,
	0xe5, 0x89, 0xc4, 0x8a, 0x81, 0x3d, 0x9a, 0xa7,
	0x11, 0x82, 0x8e, 0x38, 0x5b, 0x5b, 0xca, 0x7d,
	0x4b, 0x72, 0xc2, 0x9c, 0x30, 0x5e, 0x7f, 0xc0,
	0x6f, 0x91, 0xd5, 0x67, 0x8c, 0x3e, 0xae, 0xda,
	0x2b, 0x3c, 0x53, 0xcc, 0x50, 0x97, 0x36, 0x0b,
	0x79, 0xd6, 0x73, 0x6e, 0x7d, 0x42, 0x56, 0xe1,
	0xaa, 0xfc, 0xb3, 0xa7, 0xc8, 0x01, 0xaa, 0xc1,
	0xfc, 0x5c, 0x72, 0x8e, 0x63, 0xa8, 0x46, 0x18,
	0xee, 0x11, 0xe7, 0x30, 0x09, 0x83, 0x6c, 0xd9,
	0xf4, 0x7a, 0x7b, 0xb5, 0x1f, 0x6d, 0xc7, 0xbc,
	0xcb, 0x55, 0xea, 0x40, 0x58, 0x7a, 0x00, 0x00,
	0x90, 0x60, 0xc5, 0x64, 0x69, 0x05, 0x99, 0xd2,
	0x49, 0x62, 0x4f, 0xcb, 0x97, 0xdf, 0xdd, 0x6b,
	0x60, 0x75, 0xe2, 0xe0, 0x6f, 0x76, 0xd0, 0x37,
	0x67, 0x0a, 0xcf, 0xff, 0xc8, 0x61, 0x84, 0x14,
	0x80, 0x7c, 0x1d, 0x31, 0x8d, 0x90, 0xde, 0x0b,
	0x1c, 0x74, 0x9f, 0x82, 0x96, 0x80, 0xda, 0xaf,
	0x8d, 0x99, 0x86, 0x9f, 0x24, 0x99, 0x28, 0x3e,
	0xe0, 0xa3, 0xc3, 0x90, 0x2d, 0x14, 0x65, 0x1e,
	0x3b, 0xb9, 0xba, 0x13, 0xa5, 0x77, 0x73, 0x63,
	0x9a, 0x06, 0x3d, 0xa9, 0x28, 0x9b, 0xba, 0x25,
	0x61, 0xc9, 0xcd, 0xcf, 0x7a, 0x4d, 0x96, 0x09,
	0xcb, 0xca, 0x03, 0x9c, 0x54, 0x34, 0x31, 0x85,
	0xa0, 0x3d, 0xe5, 0xbc, 0xa5, 0x5f, 0x1b, 0xd3,
	0x10, 0x63, 0x74, 0x9d, 0x01, 0x92, 0x88, 0xf0,
	0x27, 0x9c, 0x28, 0xd9, 0xfd, 0xe2, 0x4e, 0x01,
	0x8d, 0x61, 0x79, 0x60, 0x61, 0x5b, 0x76, 0xab,
	0x06, 0xd3, 0x44, 0x87, 0x43, 0x52, 0xcd, 0x06,
	0x68, 0x1e, 0x2d, 0xc5, 0xb0, 0x07, 0x25, 0xdf,
	0x0a, 0x50, 0xd7, 0xd9, 0x08, 0x53, 0x65, 0xf1,
	0x0c, 0x2c, 0xde, 0x3f, 0x9d, 0x03, 0x1f, 0xe1,
	0x49, 0x43, 0x3c, 0x83, 0x81, 0x37, 0xf8, 0xa2,
	0x0b, 0xf9, 0x61, 0x1c, 0xc1, 0xdb, 0x79, 0xbc,
	0x64, 0xce, 0x06, 0x4e, 0x87, 0x89, 0x62, 0x73,
	0x51, 0xbc, 0xa4, 0x32, 0xd4, 0x18, 0x62, 0xab,
	0x65, 0x7e, 0xad, 0x1e, 0x91, 0xa3, 0xfa, 0x2d,
	0x58, 0x9e, 0x2a, 0xe9, 0x74, 0x44, 0x64, 0x11,
	0xe6, 0xb6, 0xb3, 0x00, 0x7e, 0xa3, 0x16, 0xef,
	0x72
};
static const u8 output74[] __initconst = {
	0xf5, 0xca, 0x45, 0x65, 0x50, 0x35, 0x47, 0x67,
	0x6f, 0x4f, 0x67, 0xff, 0x34, 0xd9, 0xc3, 0x37,
	0x2a, 0x26, 0xb0, 0x4f, 0x08, 0x1e, 0x45, 0x13,
	0xc7, 0x2c, 0x14, 0x75, 0x33, 0xd8, 0x8e, 0x1e,
	0x1b, 0x11, 0x0d, 0x97, 0x04, 0x33, 0x8a, 0xe4,
	0xd8, 0x8d, 0x0e, 0x12, 0x8d, 0xdb, 0x6e, 0x02,
	0xfa, 0xe5, 0xbd, 0x3a, 0xb5, 0x28, 0x07, 0x7d,
	0x20, 0xf0, 0x12, 0x64, 0x83, 0x2f, 0x59, 0x79,
	0x17, 0x88, 0x3c, 0x2d, 0x08, 0x2f, 0x55, 0xda,
	0xcc, 0x02, 0x3a, 0x82, 0xcd, 0x03, 0x94, 0xdf,
	0xdf, 0xab, 0x8a, 0x13, 0xf5, 0xe6, 0x74, 0xdf,
	0x7b, 0xe2, 0xab, 0x34, 0xbc, 0x00, 0x85, 0xbf,
	0x5a, 0x48, 0xc8, 0xff, 0x8d, 0x6c, 0x27, 0x48,
	0x19, 0x2d, 0x08, 0xfa, 0x82, 0x62, 0x39, 0x55,
	0x32, 0x11, 0xa8, 0xd7, 0xb9, 0x08, 0x2c, 0xd6,
	0x7a, 0xd9, 0x83, 0x9f, 0x9b, 0xfb, 0xec, 0x3a,
	0xd1, 0x08, 0xc7, 0xad, 0xdc, 0x98, 0x4c, 0xbc,
	0x98, 0xeb, 0x36, 0xb0, 0x39, 0xf4, 0x3a, 0xd6,
	0x53, 0x02, 0xa0, 0xa9, 0x73, 0xa1, 0xca, 0xef,
	0xd8, 0xd2, 0xec, 0x0e, 0xf8, 0xf5, 0xac, 0x8d,
	0x34, 0x41, 0x06, 0xa8, 0xc6, 0xc3, 0x31, 0xbc,
	0xe5, 0xcc, 0x7e, 0x72, 0x63, 0x59, 0x3e, 0x63,
	0xc2, 0x8d, 0x2b, 0xd5, 0xb9, 0xfd, 0x1e, 0x31,
	0x69, 0x32, 0x05, 0xd6, 0xde, 0xc9, 0xe6, 0x4c,
	0xac, 0x68, 0xf7, 0x1f, 0x9d, 0xcd, 0x0e, 0xa2,
	0x15, 0x3d, 0xd6, 0x47, 0x99, 0xab, 0x08, 0x5f,
	0x28, 0xc3, 0x4c, 0xc2, 0xd5, 0xdd, 0x10, 0xb7,
	0xbd, 0xdb, 0x9b, 0xcf, 0x85, 0x27, 0x29, 0x76,
	0x98, 0xeb, 0xad, 0x31, 0x64, 0xe7, 0xfb, 0x61,
	0xe0, 0xd8, 0x1a, 0xa6, 0xe2, 0xe7, 0x43, 0x42,
	0x77, 0xc9, 0x82, 0x00, 0xac, 0x85, 0xe0, 0xa2,
	0xd4, 0x62, 0xe3, 0xb7, 0x17, 0x6e, 0xb2, 0x9e,
	0x21, 0x58, 0x73, 0xa9, 0x53, 0x2d, 0x3c, 0xe1,
	0xdd, 0xd6, 0x6e, 0x92, 0xf2, 0x1d, 0xc2, 0x22,
	0x5f, 0x9a, 0x7e, 0xd0, 0x52, 0xbf, 0x54, 0x19,
	0xd7, 0x80, 0x63, 0x3e, 0xd0, 0x08, 0x2d, 0x37,
	0x0c, 0x15, 0xf7, 0xde, 0xab, 0x2b, 0xe3, 0x16,
	0x21, 0x3a, 0xee, 0xa5, 0xdc, 0xdf, 0xde, 0xa3,
	0x69, 0xcb, 0xfd, 0x92, 0x89, 0x75, 0xcf, 0xc9,
	0x8a, 0xa4, 0xc8, 0xdd, 0xcc, 0x21, 0xe6, 0xfe,
	0x9e, 0x43, 0x76, 0xb2, 0x45, 0x22, 0xb9, 0xb5,
	0xac, 0x7e, 0x3d, 0x26, 0xb0, 0x53, 0xc8, 0xab,
	0xfd, 0xea, 0x2c, 0xd1, 0x44, 0xc5, 0x60, 0x1b,
	0x8a, 0x99, 0x0d, 0xa5, 0x0e, 0x67, 0x6e, 0x3a,
	0x96, 0x55, 0xec, 0xe8, 0xcc, 0xbe, 0x49, 0xd9,
	0xf2, 0x72, 0x9f, 0x30, 0x21, 0x97, 0x57, 0x19,
	0xbe, 0x5e, 0x33, 0x0c, 0xee, 0xc0, 0x72, 0x0d,
	0x2e, 0xd1, 0xe1, 0x52, 0xc2, 0xea, 0x41, 0xbb,
	0xe1, 0x6d, 0xd4, 0x17, 0xa9, 0x8d, 0x89, 0xa9,
	0xd6, 0x4b, 0xc6, 0x4c, 0xf2, 0x88, 0x97, 0x54,
	0x3f, 0x4f, 0x57, 0xb7, 0x37, 0xf0, 0x2c, 0x11,
	0x15, 0x56, 0xdb, 0x28, 0xb5, 0x16, 0x84, 0x66,
	0xce, 0x45, 0x3f, 0x61, 0x75, 0xb6, 0xbe, 0x00,
	0xd1, 0xe4, 0xf5, 0x27, 0x54, 0x7f, 0xc2, 0xf1,
	0xb3, 0x32, 0x9a, 0xe8, 0x07, 0x02, 0xf3, 0xdb,
	0xa9, 0xd1, 0xc2, 0xdf, 0xee, 0xad, 0xe5, 0x8a,
	0x3c, 0xfa, 0x67, 0xec, 0x6b, 0xa4, 0x08, 0xfe,
	0xba, 0x5a, 0x58, 0x0b, 0x78, 0x11, 0x91, 0x76,
	0xe3, 0x1a, 0x28, 0x54, 0x5e, 0xbd, 0x71, 0x1b,
	0x8b, 0xdc, 0x6c, 0xf4, 0x6f, 0xd7, 0xf4, 0xf3,
	0xe1, 0x03, 0xa4, 0x3c, 0x8d, 0x91, 0x2e, 0xba,
	0x5f, 0x7f, 0x8c, 0xaf, 0x69, 0x89, 0x29, 0x0a,
	0x5b, 0x25, 0x13, 0xc4, 0x2e, 0x16, 0xc2, 0x15,
	0x07, 0x5d, 0x58, 0x33, 0x7c, 0xe0, 0xf0, 0x55,
	0x5f, 0xbf, 0x5e, 0xf0, 0x71, 0x48, 0x8f, 0xf7,
	0x48, 0xb3, 0xf7, 0x0d, 0xa1, 0xd0, 0x63, 0xb1,
	0xad, 0xae, 0xb5, 0xb0, 0x5f, 0x71, 0xaf, 0x24,
	0x8b, 0xb9, 0x1c, 0x44, 0xd2, 0x1a, 0x53, 0xd1,
	0xd5, 0xb4, 0xa9, 0xff, 0x88, 0x73, 0xb5, 0xaa,
	0x15, 0x32, 0x5f, 0x59, 0x9d, 0x2e, 0xb5, 0xcb,
	0xde, 0x21, 0x2e, 0xe9, 0x35, 0xed, 0xfd, 0x0f,
	0xb6, 0xbb, 0xe6, 0x4b, 0x16, 0xf1, 0x45, 0x1e,
	0xb4, 0x84, 0xe9, 0x58, 0x1c, 0x0c, 0x95, 0xc0,
	0xcf, 0x49, 0x8b, 0x59, 0xa1, 0x78, 0xe6, 0x80,
	0x12, 0x49, 0x7a, 0xd4, 0x66, 0x62, 0xdf, 0x9c,
	0x18, 0xc8, 0x8c, 0xda, 0xc1, 0xa6, 0xbc, 0x65,
	0x28, 0xd2, 0xa4, 0xe8, 0xf1, 0x35, 0xdb, 0x5a,
	0x75, 0x1f, 0x73, 0x60, 0xec, 0xa8, 0xda, 0x5a,
	0x43, 0x15, 0x83, 0x9b, 0xe7, 0xb1, 0xa6, 0x81,
	0xbb, 0xef, 0xf3, 0x8f, 0x0f, 0xd3, 0x79, 0xa2,
	0xe5, 0xaa, 0x42, 0xef, 0xa0, 0x13, 0x4e, 0x91,
	0x2d, 0xcb, 0x61, 0x7a, 0x9a, 0x33, 0x14, 0x50,
	0x77, 0x4a, 0xd0, 0x91, 0x48, 0xe0, 0x0c, 0xe0,
	0x11, 0xcb, 0xdf, 0xb0, 0xce, 0x06, 0xd2, 0x79,
	0x4d, 0x69, 0xb9, 0xc9, 0x36, 0x74, 0x8f, 0x81,
	0x72, 0x73, 0xf3, 0x17, 0xb7, 0x13, 0xcb, 0x5b,
	0xd2, 0x5c, 0x33, 0x61, 0xb7, 0x61, 0x79, 0xb0,
	0xc0, 0x4d, 0xa1, 0xc7, 0x5d, 0x98, 0xc9, 0xe1,
	0x98, 0xbd, 0x78, 0x5a, 0x2c, 0x64, 0x53, 0xaf,
	0xaf, 0x66, 0x51, 0x47, 0xe4, 0x48, 0x66, 0x8b,
	0x07, 0x52, 0xa3, 0x03, 0x93, 0x28, 0xad, 0xcc,
	0xa3, 0x86, 0xad, 0x63, 0x04, 0x35, 0x6c, 0x49,
	0xd5, 0x28, 0x0e, 0x00, 0x47, 0xf4, 0xd4, 0x32,
	0x27, 0x19, 0xb3, 0x29, 0xe7, 0xbc, 0xbb, 0xce,
	0x3e, 0x3e, 0xd5, 0x67, 0x20, 0xe4, 0x0b, 0x75,
	0x95, 0x24, 0xe0, 0x6c, 0xb6, 0x29, 0x0c, 0x14,
	0xfd
};
static const u8 key74[] __initconst = {
	0xf0, 0x41, 0x5b, 0x00, 0x56, 0xc4, 0xac, 0xf6,
	0xa2, 0x4c, 0x33, 0x41, 0x16, 0x09, 0x1b, 0x8e,
	0x4d, 0xe8, 0x8c, 0xd9, 0x48, 0xab, 0x3e, 0x60,
	0xcb, 0x49, 0x3e, 0xaf, 0x2b, 0x8b, 0xc8, 0xf0
};
enum { nonce74 = 0xcbdb0ffd0e923384ULL };

static const struct chacha20_testvec chacha20_testvecs[] __initconst = {
	{ input01, output01, key01, nonce01, sizeof(input01) },
	{ input02, output02, key02, nonce02, sizeof(input02) },
	{ input03, output03, key03, nonce03, sizeof(input03) },
	{ input04, output04, key04, nonce04, sizeof(input04) },
	{ input05, output05, key05, nonce05, sizeof(input05) },
	{ input06, output06, key06, nonce06, sizeof(input06) },
	{ input07, output07, key07, nonce07, sizeof(input07) },
	{ input08, output08, key08, nonce08, sizeof(input08) },
	{ input09, output09, key09, nonce09, sizeof(input09) },
	{ input10, output10, key10, nonce10, sizeof(input10) },
	{ input11, output11, key11, nonce11, sizeof(input11) },
	{ input12, output12, key12, nonce12, sizeof(input12) },
	{ input13, output13, key13, nonce13, sizeof(input13) },
	{ input14, output14, key14, nonce14, sizeof(input14) },
	{ input15, output15, key15, nonce15, sizeof(input15) },
	{ input16, output16, key16, nonce16, sizeof(input16) },
	{ input17, output17, key17, nonce17, sizeof(input17) },
	{ input18, output18, key18, nonce18, sizeof(input18) },
	{ input19, output19, key19, nonce19, sizeof(input19) },
	{ input20, output20, key20, nonce20, sizeof(input20) },
	{ input21, output21, key21, nonce21, sizeof(input21) },
	{ input22, output22, key22, nonce22, sizeof(input22) },
	{ input23, output23, key23, nonce23, sizeof(input23) },
	{ input24, output24, key24, nonce24, sizeof(input24) },
	{ input25, output25, key25, nonce25, sizeof(input25) },
	{ input26, output26, key26, nonce26, sizeof(input26) },
	{ input27, output27, key27, nonce27, sizeof(input27) },
	{ input28, output28, key28, nonce28, sizeof(input28) },
	{ input29, output29, key29, nonce29, sizeof(input29) },
	{ input30, output30, key30, nonce30, sizeof(input30) },
	{ input31, output31, key31, nonce31, sizeof(input31) },
	{ input32, output32, key32, nonce32, sizeof(input32) },
	{ input33, output33, key33, nonce33, sizeof(input33) },
	{ input34, output34, key34, nonce34, sizeof(input34) },
	{ input35, output35, key35, nonce35, sizeof(input35) },
	{ input36, output36, key36, nonce36, sizeof(input36) },
	{ input37, output37, key37, nonce37, sizeof(input37) },
	{ input38, output38, key38, nonce38, sizeof(input38) },
	{ input39, output39, key39, nonce39, sizeof(input39) },
	{ input40, output40, key40, nonce40, sizeof(input40) },
	{ input41, output41, key41, nonce41, sizeof(input41) },
	{ input42, output42, key42, nonce42, sizeof(input42) },
	{ input43, output43, key43, nonce43, sizeof(input43) },
	{ input44, output44, key44, nonce44, sizeof(input44) },
	{ input45, output45, key45, nonce45, sizeof(input45) },
	{ input46, output46, key46, nonce46, sizeof(input46) },
	{ input47, output47, key47, nonce47, sizeof(input47) },
	{ input48, output48, key48, nonce48, sizeof(input48) },
	{ input49, output49, key49, nonce49, sizeof(input49) },
	{ input50, output50, key50, nonce50, sizeof(input50) },
	{ input51, output51, key51, nonce51, sizeof(input51) },
	{ input52, output52, key52, nonce52, sizeof(input52) },
	{ input53, output53, key53, nonce53, sizeof(input53) },
	{ input54, output54, key54, nonce54, sizeof(input54) },
	{ input55, output55, key55, nonce55, sizeof(input55) },
	{ input56, output56, key56, nonce56, sizeof(input56) },
	{ input57, output57, key57, nonce57, sizeof(input57) },
	{ input58, output58, key58, nonce58, sizeof(input58) },
	{ input59, output59, key59, nonce59, sizeof(input59) },
	{ input60, output60, key60, nonce60, sizeof(input60) },
	{ input61, output61, key61, nonce61, sizeof(input61) },
	{ input62, output62, key62, nonce62, sizeof(input62) },
	{ input63, output63, key63, nonce63, sizeof(input63) },
	{ input64, output64, key64, nonce64, sizeof(input64) },
	{ input65, output65, key65, nonce65, sizeof(input65) },
	{ input66, output66, key66, nonce66, sizeof(input66) },
	{ input67, output67, key67, nonce67, sizeof(input67) },
	{ input68, output68, key68, nonce68, sizeof(input68) },
	{ input69, output69, key69, nonce69, sizeof(input69) },
	{ input70, output70, key70, nonce70, sizeof(input70) },
	{ input71, output71, key71, nonce71, sizeof(input71) },
	{ input72, output72, key72, nonce72, sizeof(input72) },
	{ input73, output73, key73, nonce73, sizeof(input73) },
	{ input74, output74, key74, nonce74, sizeof(input74) }
};

static const struct hchacha20_testvec hchacha20_testvecs[] __initconst = {{
	.key	= { 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
		    0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
		    0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
		    0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f },
	.nonce	= { 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x4a,
		    0x00, 0x00, 0x00, 0x00, 0x31, 0x41, 0x59, 0x27 },
	.output	= { 0x82, 0x41, 0x3b, 0x42, 0x27, 0xb2, 0x7b, 0xfe,
		    0xd3, 0x0e, 0x42, 0x50, 0x8a, 0x87, 0x7d, 0x73,
		    0xa0, 0xf9, 0xe4, 0xd5, 0x8a, 0x74, 0xa8, 0x53,
		    0xc1, 0x2e, 0xc4, 0x13, 0x26, 0xd3, 0xec, 0xdc }
}};

static bool __init chacha20_selftest(void)
{
	enum {
		MAXIMUM_TEST_BUFFER_LEN = 1UL << 10,
		OUTRAGEOUSLY_HUGE_BUFFER_LEN = PAGE_SIZE * 35 + 17 /* 143k */
	};
	size_t i, j, k;
	u32 derived_key[CHACHA20_KEY_WORDS];
	u8 *offset_input = NULL, *computed_output = NULL, *massive_input = NULL;
	u8 offset_key[CHACHA20_KEY_SIZE + 1]
			__aligned(__alignof__(unsigned long));
	struct chacha20_ctx state;
	bool success = true;
	simd_context_t simd_context;

	offset_input = kmalloc(MAXIMUM_TEST_BUFFER_LEN + 1, GFP_KERNEL);
	computed_output = kmalloc(MAXIMUM_TEST_BUFFER_LEN + 1, GFP_KERNEL);
	massive_input = vzalloc(OUTRAGEOUSLY_HUGE_BUFFER_LEN);
	if (!computed_output || !offset_input || !massive_input) {
		pr_err("chacha20 self-test malloc: FAIL\n");
		success = false;
		goto out;
	}

	simd_get(&simd_context);
	for (i = 0; i < ARRAY_SIZE(chacha20_testvecs); ++i) {
		/* Boring case */
		memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN + 1);
		memset(&state, 0, sizeof(state));
		chacha20_init(&state, chacha20_testvecs[i].key,
			      chacha20_testvecs[i].nonce);
		chacha20(&state, computed_output, chacha20_testvecs[i].input,
			 chacha20_testvecs[i].ilen, &simd_context);
		if (memcmp(computed_output, chacha20_testvecs[i].output,
			   chacha20_testvecs[i].ilen)) {
			pr_err("chacha20 self-test %zu: FAIL\n", i + 1);
			success = false;
		}
		for (k = chacha20_testvecs[i].ilen;
		     k < MAXIMUM_TEST_BUFFER_LEN + 1; ++k) {
			if (computed_output[k]) {
				pr_err("chacha20 self-test %zu (zero check): FAIL\n",
				       i + 1);
				success = false;
				break;
			}
		}

		/* Unaligned case */
		memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN + 1);
		memset(&state, 0, sizeof(state));
		memcpy(offset_input + 1, chacha20_testvecs[i].input,
		       chacha20_testvecs[i].ilen);
		memcpy(offset_key + 1, chacha20_testvecs[i].key,
		       CHACHA20_KEY_SIZE);
		chacha20_init(&state, offset_key + 1, chacha20_testvecs[i].nonce);
		chacha20(&state, computed_output + 1, offset_input + 1,
			 chacha20_testvecs[i].ilen, &simd_context);
		if (memcmp(computed_output + 1, chacha20_testvecs[i].output,
			   chacha20_testvecs[i].ilen)) {
			pr_err("chacha20 self-test %zu (unaligned): FAIL\n",
			       i + 1);
			success = false;
		}
		if (computed_output[0]) {
			pr_err("chacha20 self-test %zu (unaligned, zero check): FAIL\n",
			       i + 1);
			success = false;
		}
		for (k = chacha20_testvecs[i].ilen + 1;
		     k < MAXIMUM_TEST_BUFFER_LEN + 1; ++k) {
			if (computed_output[k]) {
				pr_err("chacha20 self-test %zu (unaligned, zero check): FAIL\n",
				       i + 1);
				success = false;
				break;
			}
		}

		/* Chunked case */
		if (chacha20_testvecs[i].ilen <= CHACHA20_BLOCK_SIZE)
			goto next_test;
		memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN + 1);
		memset(&state, 0, sizeof(state));
		chacha20_init(&state, chacha20_testvecs[i].key,
			      chacha20_testvecs[i].nonce);
		chacha20(&state, computed_output, chacha20_testvecs[i].input,
			 CHACHA20_BLOCK_SIZE, &simd_context);
		chacha20(&state, computed_output + CHACHA20_BLOCK_SIZE,
			 chacha20_testvecs[i].input + CHACHA20_BLOCK_SIZE,
			 chacha20_testvecs[i].ilen - CHACHA20_BLOCK_SIZE,
			 &simd_context);
		if (memcmp(computed_output, chacha20_testvecs[i].output,
			   chacha20_testvecs[i].ilen)) {
			pr_err("chacha20 self-test %zu (chunked): FAIL\n",
			       i + 1);
			success = false;
		}
		for (k = chacha20_testvecs[i].ilen;
		     k < MAXIMUM_TEST_BUFFER_LEN + 1; ++k) {
			if (computed_output[k]) {
				pr_err("chacha20 self-test %zu (chunked, zero check): FAIL\n",
				       i + 1);
				success = false;
				break;
			}
		}

next_test:
		/* Sliding unaligned case */
		if (chacha20_testvecs[i].ilen > CHACHA20_BLOCK_SIZE + 1 ||
		    !chacha20_testvecs[i].ilen)
			continue;
		for (j = 1; j < CHACHA20_BLOCK_SIZE; ++j) {
			memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN + 1);
			memset(&state, 0, sizeof(state));
			memcpy(offset_input + j, chacha20_testvecs[i].input,
			       chacha20_testvecs[i].ilen);
			chacha20_init(&state, chacha20_testvecs[i].key,
				      chacha20_testvecs[i].nonce);
			chacha20(&state, computed_output + j, offset_input + j,
				 chacha20_testvecs[i].ilen, &simd_context);
			if (memcmp(computed_output + j,
				   chacha20_testvecs[i].output,
				   chacha20_testvecs[i].ilen)) {
				pr_err("chacha20 self-test %zu (unaligned, slide %zu): FAIL\n",
				       i + 1, j);
				success = false;
			}
			for (k = j; k < j; ++k) {
				if (computed_output[k]) {
					pr_err("chacha20 self-test %zu (unaligned, slide %zu, zero check): FAIL\n",
					       i + 1, j);
					success = false;
					break;
				}
			}
			for (k = chacha20_testvecs[i].ilen + j;
			     k < MAXIMUM_TEST_BUFFER_LEN + 1; ++k) {
				if (computed_output[k]) {
					pr_err("chacha20 self-test %zu (unaligned, slide %zu, zero check): FAIL\n",
					       i + 1, j);
					success = false;
					break;
				}
			}
		}
	}
	for (i = 0; i < ARRAY_SIZE(hchacha20_testvecs); ++i) {
		memset(&derived_key, 0, sizeof(derived_key));
		hchacha20(derived_key, hchacha20_testvecs[i].nonce,
			  hchacha20_testvecs[i].key, &simd_context);
		cpu_to_le32_array(derived_key, ARRAY_SIZE(derived_key));
		if (memcmp(derived_key, hchacha20_testvecs[i].output,
			   CHACHA20_KEY_SIZE)) {
			pr_err("hchacha20 self-test %zu: FAIL\n", i + 1);
			success = false;
		}
	}
	memset(&state, 0, sizeof(state));
	chacha20_init(&state, chacha20_testvecs[0].key,
		      chacha20_testvecs[0].nonce);
	chacha20(&state, massive_input, massive_input,
		 OUTRAGEOUSLY_HUGE_BUFFER_LEN, &simd_context);
	chacha20_init(&state, chacha20_testvecs[0].key,
		      chacha20_testvecs[0].nonce);
	chacha20(&state, massive_input, massive_input,
		 OUTRAGEOUSLY_HUGE_BUFFER_LEN, DONT_USE_SIMD);
	for (k = 0; k < OUTRAGEOUSLY_HUGE_BUFFER_LEN; ++k) {
		if (massive_input[k]) {
			pr_err("chacha20 self-test massive: FAIL\n");
			success = false;
			break;
		}
	}

	simd_put(&simd_context);

out:
	kfree(offset_input);
	kfree(computed_output);
	vfree(massive_input);
	return success;
}
