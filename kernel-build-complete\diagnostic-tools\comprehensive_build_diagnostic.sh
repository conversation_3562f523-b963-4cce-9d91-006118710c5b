#!/bin/bash
# Comprehensive Kernel Build Diagnostic Script
# This script checks all requirements before attempting to build

echo "========================================"
echo "  🔍 KERNEL BUILD DIAGNOSTIC TOOL"
echo "  Version 1.0 - Complete Analysis"
echo "========================================"
echo ""

# Initialize counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
CRITICAL_ISSUES=0
WARNINGS=0

# Create log file
LOG_FILE="diagnostic_$(date +%Y%m%d_%H%M%S).log"
exec > >(tee -a "$LOG_FILE")
exec 2>&1

echo "📝 Diagnostic log: $LOG_FILE"
echo "🕐 Started at: $(date)"
echo ""

echo "=== SECTION 1: SYSTEM ENVIRONMENT ==="
echo ""

# Check operating system
echo "🖥️  Operating System:"
if [[ -f /etc/os-release ]]; then
    source /etc/os-release
    echo "   OS: $PRETTY_NAME"
    echo "   Version: $VERSION"
    ((TOTAL_CHECKS++))
    ((PASSED_CHECKS++))
else
    echo "   ❌ Cannot determine OS version"
    ((TOTAL_CHECKS++))
    ((CRITICAL_ISSUES++))
fi

# Check WSL environment
echo ""
echo "🐧 WSL Environment:"
if [[ -n "$WSL_DISTRO_NAME" ]]; then
    echo "   ✅ Running in WSL: $WSL_DISTRO_NAME"
    echo "   WSL Version: $(cat /proc/version | grep -o 'WSL[0-9]*' || echo 'WSL1')"
    WSL_ENV=true
else
    echo "   ℹ️  Not running in WSL (native Linux)"
    WSL_ENV=false
fi

# Check available memory and disk space
echo ""
echo "💾 System Resources:"
MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
DISK_FREE_GB=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')

echo "   RAM: ${MEMORY_GB}GB total"
echo "   Free disk space: ${DISK_FREE_GB}GB"

if [[ $MEMORY_GB -lt 4 ]]; then
    echo "   ⚠️  Warning: Less than 4GB RAM (kernel build may be slow)"
    ((WARNINGS++))
fi

if [[ $DISK_FREE_GB -lt 10 ]]; then
    echo "   ❌ Critical: Less than 10GB free space needed"
    ((CRITICAL_ISSUES++))
fi

echo ""
echo "=== SECTION 2: BUILD TOOLS ANALYSIS ==="
echo ""

# Essential build tools
ESSENTIAL_TOOLS=(
    "make:build-essential"
    "gcc:build-essential" 
    "g++:build-essential"
    "tar:tar"
    "xz:xz-utils"
    "gzip:gzip"
    "bison:bison"
    "flex:flex"
    "bc:bc"
    "python3:python3"
    "perl:perl"
    "rsync:rsync"
    "cpio:cpio"
    "git:git"
    "curl:curl"
    "wget:wget"
)

echo "🔧 Essential Build Tools:"
MISSING_TOOLS=()
MISSING_PACKAGES=()

for tool_info in "${ESSENTIAL_TOOLS[@]}"; do
    tool=$(echo "$tool_info" | cut -d: -f1)
    package=$(echo "$tool_info" | cut -d: -f2)
    
    ((TOTAL_CHECKS++))
    if command -v "$tool" > /dev/null 2>&1; then
        version_info=$(command -v "$tool")
        echo "   ✅ $tool: $version_info"
        ((PASSED_CHECKS++))
    else
        echo "   ❌ $tool: NOT FOUND"
        MISSING_TOOLS+=("$tool")
        MISSING_PACKAGES+=("$package")
        ((CRITICAL_ISSUES++))
    fi
done

echo ""
echo "🎯 Cross-Compilation Tools:"

# ARM64 cross compiler
((TOTAL_CHECKS++))
if command -v aarch64-linux-gnu-gcc > /dev/null 2>&1; then
    ARM64_VERSION=$(aarch64-linux-gnu-gcc --version | head -1)
    echo "   ✅ ARM64: $ARM64_VERSION"
    ((PASSED_CHECKS++))
    ARM64_AVAILABLE=true
else
    echo "   ❌ ARM64 cross compiler missing (aarch64-linux-gnu-gcc)"
    MISSING_PACKAGES+=("gcc-aarch64-linux-gnu")
    ((CRITICAL_ISSUES++))
    ARM64_AVAILABLE=false
fi

# ARM32 cross compiler
((TOTAL_CHECKS++))
if command -v arm-linux-gnueabihf-gcc > /dev/null 2>&1; then
    ARM32_VERSION=$(arm-linux-gnueabihf-gcc --version | head -1)
    echo "   ✅ ARM32: $ARM32_VERSION"
    ((PASSED_CHECKS++))
    ARM32_AVAILABLE=true
else
    echo "   ❌ ARM32 cross compiler missing (arm-linux-gnueabihf-gcc)"
    MISSING_PACKAGES+=("gcc-arm-linux-gnueabihf")
    ((CRITICAL_ISSUES++))
    ARM32_AVAILABLE=false
fi

echo ""
echo "========================================"
echo "  📊 DIAGNOSTIC SUMMARY"
echo "========================================"
echo ""

# Calculate scores
SUCCESS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

echo "🎯 **Overall Assessment:**"
echo "   Total Checks: $TOTAL_CHECKS"
echo "   Passed: $PASSED_CHECKS"
echo "   Success Rate: $SUCCESS_RATE%"
echo "   Critical Issues: $CRITICAL_ISSUES"
echo "   Warnings: $WARNINGS"
echo ""

# Determine build readiness
if [[ $CRITICAL_ISSUES -eq 0 ]] && [[ $SUCCESS_RATE -ge 90 ]]; then
    BUILD_STATUS="READY"
    STATUS_ICON="🎉"
elif [[ $CRITICAL_ISSUES -le 2 ]] && [[ $SUCCESS_RATE -ge 70 ]]; then
    BUILD_STATUS="MOSTLY_READY"
    STATUS_ICON="⚠️"
else
    BUILD_STATUS="NOT_READY"
    STATUS_ICON="❌"
fi

echo "$STATUS_ICON **BUILD STATUS: $BUILD_STATUS**"
echo ""

# Generate installation commands
if [[ ${#MISSING_PACKAGES[@]} -gt 0 ]]; then
    echo "📦 **Required Package Installation:**"
    echo ""
    
    # Remove duplicates
    UNIQUE_PACKAGES=($(printf "%s\n" "${MISSING_PACKAGES[@]}" | sort -u))
    
    echo "   sudo apt update"
    echo "   sudo apt install -y \\"
    for package in "${UNIQUE_PACKAGES[@]}"; do
        echo "     $package \\"
    done
    echo ""
fi

echo "🚀 **Next Steps:**"
echo ""

case $BUILD_STATUS in
    "READY")
        echo "   ✅ All requirements satisfied!"
        echo "   1. Proceed with kernel build"
        echo "   2. Run: ./apollo.sh (recommended)"
        ;;
    "MOSTLY_READY")
        echo "   ⚠️  Minor issues to resolve:"
        echo "   1. Install missing packages (see above)"
        echo "   2. Run diagnostic again to verify"
        echo "   3. Proceed with kernel build"
        ;;
    "NOT_READY")
        echo "   ❌ Critical issues must be resolved:"
        echo "   1. Install missing packages (see above)"
        echo "   2. Fix critical issues identified"
        echo "   3. Run diagnostic again"
        ;;
esac

echo ""
echo "📝 **Detailed Report Saved:** $LOG_FILE"
echo "🕐 **Completed at:** $(date)"
