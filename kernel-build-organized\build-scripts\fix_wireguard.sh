#!/bin/bash

# WireGuard Module Fix Script for Exynos 9810 Kernel
# This script fixes the WireGuard module setup issues

set -e

echo "=== WireGuard Module Fix Script ==="
echo "Fixing WireGuard module setup for Exynos 9810 kernel build..."

# Change to kernel directory
KERNEL_DIR="$(pwd)"
if [[ ! -f "Makefile" ]] || [[ ! -d "net" ]]; then
    echo "Error: This script must be run from the kernel root directory"
    exit 1
fi

echo "Working in kernel directory: $KERNEL_DIR"

# Step 1: Clean up existing WireGuard directory
echo "Step 1: Cleaning up existing WireGuard directory..."
if [[ -d "net/wireguard" ]]; then
    echo "Removing existing net/wireguard directory..."
    rm -rf net/wireguard
fi

# Step 2: Clone WireGuard module with SSL fixes
echo "Step 2: Cloning WireGuard module..."

# Set up SSL bypass for git operations
export GIT_SSL_NO_VERIFY=1

# Clone the WireGuard Linux compatibility module
echo "Cloning wireguard-linux-compat..."
if ! git clone https://git.zx2c4.com/wireguard-linux-compat net/wireguard; then
    echo "Git clone failed, trying with curl fallback..."
    
    # Fallback: Download as tarball if git clone fails
    mkdir -p net/wireguard
    cd net/wireguard
    
    # Try to download the latest snapshot
    if curl -k -L -o wireguard.tar.gz "https://git.zx2c4.com/wireguard-linux-compat/snapshot/wireguard-linux-compat-master.tar.gz"; then
        echo "Downloaded WireGuard tarball, extracting..."
        tar -xzf wireguard.tar.gz --strip-components=1
        rm wireguard.tar.gz
    else
        echo "Error: Failed to download WireGuard module"
        cd "$KERNEL_DIR"
        exit 1
    fi
    
    cd "$KERNEL_DIR"
fi

# Step 3: Verify the WireGuard directory structure
echo "Step 3: Verifying WireGuard directory structure..."
if [[ ! -d "net/wireguard" ]]; then
    echo "Error: WireGuard directory was not created successfully"
    exit 1
fi

echo "WireGuard directory contents:"
ls -la net/wireguard/

# Step 4: Set up Kconfig file
echo "Step 4: Setting up Kconfig file..."

# Check if src/Kconfig exists
if [[ -f "net/wireguard/src/Kconfig" ]]; then
    echo "Found src/Kconfig, creating symbolic link..."
    ln -sf src/Kconfig net/wireguard/Kconfig
elif [[ -f "net/wireguard/Kconfig" ]]; then
    echo "Kconfig already exists in the root directory"
else
    echo "Creating minimal Kconfig file..."
    cat > net/wireguard/Kconfig << 'EOF'
# SPDX-License-Identifier: GPL-2.0
#
# WireGuard VPN configuration
#

config WIREGUARD
	tristate "WireGuard secure network tunnel"
	depends on NET && INET
	depends on IPV6 || !IPV6
	select DST_CACHE
	select ZINC_CHACHA20POLY1305
	select ZINC_BLAKE2S
	select ZINC_CURVE25519
	default m
	help
	  WireGuard is a secure, fast, and easy to use replacement for IPSec
	  that uses modern cryptography and clever networking tricks. It's
	  designed to be fairly general purpose and abstract enough to fit most
	  use cases, while at the same time remaining extremely simple to
	  configure. See www.wireguard.com for more info.

	  It's safe to say Y or M here, as the driver is very lightweight and
	  is only in use when an administrator chooses to add an interface.

config WIREGUARD_DEBUG
	bool "Debugging checks and verbose messages"
	depends on WIREGUARD
	help
	  This will write log messages for handshake and other events
	  that occur for a WireGuard interface. It will also perform some
	  extra validation checks and unit tests at various points. This is
	  only useful for debugging.

	  Say N here unless you know what you're doing.
EOF
fi

# Step 5: Verify Kconfig file
echo "Step 5: Verifying Kconfig file..."
if [[ -f "net/wireguard/Kconfig" ]]; then
    echo "Kconfig file created successfully:"
    head -10 net/wireguard/Kconfig
else
    echo "Error: Failed to create Kconfig file"
    exit 1
fi

# Step 6: Set up Makefile if needed
echo "Step 6: Setting up Makefile..."
if [[ -f "net/wireguard/src/Makefile" ]] && [[ ! -f "net/wireguard/Makefile" ]]; then
    echo "Creating Makefile symbolic link..."
    ln -sf src/Makefile net/wireguard/Makefile
elif [[ ! -f "net/wireguard/Makefile" ]]; then
    echo "Creating minimal Makefile..."
    cat > net/wireguard/Makefile << 'EOF'
# SPDX-License-Identifier: GPL-2.0
#
# Makefile for the WireGuard secure network tunnel.
#

ccflags-y := -O3 -fvisibility=hidden
ccflags-$(CONFIG_WIREGUARD_DEBUG) += -DDEBUG -g
ccflags-y += -Wframe-larger-than=2048

wireguard-y := device.o peer.o timers.o queueing.o send.o receive.o socket.o peerlookup.o allowedips.o ratelimiter.o cookie.o netlink.o
wireguard-y += crypto/zinc/chacha20/chacha20.o
wireguard-y += crypto/zinc/poly1305/poly1305.o
wireguard-y += crypto/zinc/chacha20poly1305.o
wireguard-y += crypto/zinc/blake2s/blake2s.o
wireguard-y += crypto/zinc/curve25519/curve25519.o

obj-$(CONFIG_WIREGUARD) := wireguard.o
EOF
fi

echo "=== WireGuard Module Fix Complete ==="
echo "WireGuard module has been set up successfully!"
echo ""
echo "Next steps:"
echo "1. Run 'make exynos9810_defconfig' or your preferred defconfig"
echo "2. Run 'make menuconfig' to configure WireGuard if needed"
echo "3. Run 'make -j\$(nproc)' to build the kernel"
echo ""
echo "WireGuard can be found in the kernel config under:"
echo "  Device Drivers -> Network device support -> WireGuard secure network tunnel"
