#!/bin/bash

# Test Script for Kernel Build Fixes
# This script tests if the applied fixes resolve the build issues

set -e

echo "========================================"
echo "  Testing Kernel Build Fixes"
echo "========================================"
echo ""

# Check if we're in the right directory
if [[ ! -f "Makefile" ]] || [[ ! -d "net" ]]; then
    echo "❌ Error: This script must be run from the kernel root directory"
    exit 1
fi

KERNEL_DIR="$(pwd)"
echo "Testing in kernel directory: $KERNEL_DIR"
echo ""

# Test 1: Check WireGuard files exist
echo "=== Test 1: WireGuard Module Files ==="
if [[ -f "net/wireguard/Kconfig" ]]; then
    echo "✅ net/wireguard/Kconfig exists"
    echo "   First few lines:"
    head -3 net/wireguard/Kconfig | sed 's/^/   /'
else
    echo "❌ net/wireguard/Kconfig missing"
fi

if [[ -f "net/wireguard/Makefile" ]]; then
    echo "✅ net/wireguard/Makefile exists"
else
    echo "❌ net/wireguard/Makefile missing"
fi

if [[ -d "net/wireguard" ]] && [[ "$(ls -A net/wireguard)" ]]; then
    echo "✅ net/wireguard directory is not empty"
    echo "   Contents: $(ls net/wireguard | tr '\n' ' ')"
else
    echo "❌ net/wireguard directory is empty or missing"
fi
echo ""

# Test 2: Check net/Kconfig references
echo "=== Test 2: net/Kconfig WireGuard Reference ==="
if grep -q 'source "net/wireguard/Kconfig"' net/Kconfig; then
    echo "✅ net/Kconfig contains WireGuard reference"
else
    echo "❌ net/Kconfig missing WireGuard reference"
fi
echo ""

# Test 3: Test defconfig generation
echo "=== Test 3: Defconfig Generation ==="
DEFCONFIG_TESTS=("exynos9810_defconfig" "starlte_defconfig" "star2lte_defconfig" "crownlte_defconfig")

for defconfig in "${DEFCONFIG_TESTS[@]}"; do
    echo "Testing $defconfig..."
    if make $defconfig > /dev/null 2>&1; then
        echo "✅ $defconfig works"
        
        # Check if WireGuard config option is available
        if grep -q "CONFIG_WIREGUARD" .config 2>/dev/null; then
            echo "   ✅ WireGuard config option found"
        else
            echo "   ⚠️  WireGuard config option not found (may be disabled by default)"
        fi
        break
    else
        echo "❌ $defconfig failed"
    fi
done
echo ""

# Test 4: Check for common build dependencies
echo "=== Test 4: Build Dependencies ==="
REQUIRED_TOOLS=("make" "gcc" "git")

for tool in "${REQUIRED_TOOLS[@]}"; do
    if command -v $tool > /dev/null 2>&1; then
        echo "✅ $tool is available"
    else
        echo "❌ $tool is missing"
    fi
done
echo ""

# Test 5: SSL Configuration Test
echo "=== Test 5: SSL Configuration ==="
if [[ "$GIT_SSL_NO_VERIFY" == "1" ]]; then
    echo "✅ GIT_SSL_NO_VERIFY is set"
else
    echo "⚠️  GIT_SSL_NO_VERIFY not set (may cause SSL issues)"
fi

if git config --global --get http.sslVerify | grep -q "false"; then
    echo "✅ Git SSL verification disabled"
else
    echo "⚠️  Git SSL verification not disabled"
fi
echo ""

# Test 6: Quick build test (just preprocessing)
echo "=== Test 6: Quick Build Test ==="
echo "Testing kernel preprocessing..."
if make prepare > /dev/null 2>&1; then
    echo "✅ Kernel preprocessing successful"
else
    echo "❌ Kernel preprocessing failed"
fi
echo ""

# Summary
echo "========================================"
echo "  Test Summary"
echo "========================================"
echo ""

# Count successful tests
TOTAL_TESTS=6
PASSED_TESTS=0

# Re-run key checks for summary
[[ -f "net/wireguard/Kconfig" ]] && ((PASSED_TESTS++))
[[ -f "net/wireguard/Makefile" ]] && ((PASSED_TESTS++))
grep -q 'source "net/wireguard/Kconfig"' net/Kconfig && ((PASSED_TESTS++))

# Test at least one defconfig
for defconfig in "${DEFCONFIG_TESTS[@]}"; do
    if make $defconfig > /dev/null 2>&1; then
        ((PASSED_TESTS++))
        break
    fi
done

command -v make > /dev/null 2>&1 && ((PASSED_TESTS++))
make prepare > /dev/null 2>&1 && ((PASSED_TESTS++))

echo "Tests passed: $PASSED_TESTS/$TOTAL_TESTS"
echo ""

if [[ $PASSED_TESTS -ge 4 ]]; then
    echo "🎉 Most tests passed! The kernel build fixes appear to be working."
    echo ""
    echo "Next steps:"
    echo "1. Run: make exynos9810_defconfig (or your preferred defconfig)"
    echo "2. Run: make menuconfig (optional, to configure options)"
    echo "3. Run: make -j\$(nproc) (to build the kernel)"
    echo "4. Or use: ./apollo.sh (automated build script)"
else
    echo "⚠️  Some tests failed. You may need to run the fix scripts:"
    echo "1. ./fix_kernel_build.sh (comprehensive fix)"
    echo "2. Or run individual scripts: ./fix_ssl_issues.sh && ./fix_wireguard.sh"
fi
echo ""
