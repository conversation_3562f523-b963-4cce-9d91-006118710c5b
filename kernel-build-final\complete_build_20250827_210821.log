scripts/kconfig/conf  --silentoldconfig Kconfig
warning: (MALI_MIDGARD && MALI_MIDGARD) selects DEVFREQ_THERMAL which has unmet direct dependencies (THERMAL && PM_DEVFREQ && PM_OPP)
warning: (MALI_MIDGARD && MALI_MIDGARD) selects <PERSON>VFREQ_THERMAL which has unmet direct dependencies (THERMAL && PM_DEVFREQ && PM_OPP)
arch/arm64/Makefile:31: ld does not support --fix-cortex-a53-843419; kernel may be susceptible to erratum
  CHK     include/config/kernel.release
  UPD     include/config/kernel.release
  WRAP    arch/arm64/include/generated/asm/bugs.h
  WRAP    arch/arm64/include/generated/asm/clkdev.h
  WRAP    arch/arm64/include/generated/asm/cputime.h
  WRAP    arch/arm64/include/generated/asm/delay.h
  WRAP    arch/arm64/include/generated/asm/div64.h
  WRAP    arch/arm64/include/generated/asm/dma.h
  WRAP    arch/arm64/include/generated/asm/dma-contiguous.h
  WRAP    arch/arm64/include/generated/asm/early_ioremap.h
  WRAP    arch/arm64/include/generated/asm/emergency-restart.h
  WRAP    arch/arm64/include/generated/asm/errno.h
  CHK     include/generated/uapi/linux/version.h
  WRAP    arch/arm64/include/generated/asm/hw_irq.h
  WRAP    arch/arm64/include/generated/asm/ioctl.h
  WRAP    arch/arm64/include/generated/asm/ioctls.h
  UPD     include/generated/uapi/linux/version.h
  WRAP    arch/arm64/include/generated/asm/ipcbuf.h
  WRAP    arch/arm64/include/generated/asm/irq_regs.h
  WRAP    arch/arm64/include/generated/asm/kdebug.h
  WRAP    arch/arm64/include/generated/asm/kmap_types.h
  WRAP    arch/arm64/include/generated/asm/kvm_para.h
  WRAP    arch/arm64/include/generated/asm/local.h
  WRAP    arch/arm64/include/generated/asm/local64.h
  WRAP    arch/arm64/include/generated/asm/mcs_spinlock.h
  WRAP    arch/arm64/include/generated/asm/mm-arch-hooks.h
  WRAP    arch/arm64/include/generated/asm/mman.h
  WRAP    arch/arm64/include/generated/asm/msgbuf.h
  WRAP    arch/arm64/include/generated/asm/msi.h
  WRAP    arch/arm64/include/generated/asm/mutex.h
  WRAP    arch/arm64/include/generated/asm/poll.h
  WRAP    arch/arm64/include/generated/asm/preempt.h
  WRAP    arch/arm64/include/generated/asm/resource.h
  WRAP    arch/arm64/include/generated/asm/rwsem.h
  WRAP    arch/arm64/include/generated/asm/segment.h
  WRAP    arch/arm64/include/generated/asm/sembuf.h
  WRAP    arch/arm64/include/generated/asm/serial.h
  WRAP    arch/arm64/include/generated/asm/shmbuf.h
  WRAP    arch/arm64/include/generated/asm/simd.h
  CHK     include/generated/utsrelease.h
  WRAP    arch/arm64/include/generated/asm/sizes.h
  WRAP    arch/arm64/include/generated/asm/socket.h
  WRAP    arch/arm64/include/generated/asm/sockios.h
  WRAP    arch/arm64/include/generated/asm/swab.h
  WRAP    arch/arm64/include/generated/asm/switch_to.h
  UPD     include/generated/utsrelease.h
  WRAP    arch/arm64/include/generated/asm/termbits.h
  WRAP    arch/arm64/include/generated/asm/termios.h
  WRAP    arch/arm64/include/generated/asm/trace_clock.h
  WRAP    arch/arm64/include/generated/asm/types.h
  WRAP    arch/arm64/include/generated/asm/unaligned.h
  WRAP    arch/arm64/include/generated/asm/user.h
  WRAP    arch/arm64/include/generated/asm/vga.h
  WRAP    arch/arm64/include/generated/uapi/asm/kvm_para.h
  HOSTCC  scripts/basic/fixdep
  HOSTCC  scripts/dtc/dtc.o
  CC      scripts/mod/empty.o
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mfloat-abi=hard’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-aggressive-ext-opt’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-slp-vectorize-hor-store’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-adce-remove-loops’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-scalar-evolution-use-expensive-range-sharpening’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-allow-unroll-and-jam’; did you mean ‘-floop-unroll-and-jam’?
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘-mllvm’
aarch64-linux-gnu-gcc: error: unrecognized command-line option ‘--cuda-path=/dev/null’
make[2]: *** [scripts/Makefile.build:338: scripts/mod/empty.o] Error 1
make[1]: *** [scripts/Makefile.build:650: scripts/mod] Error 2
make[1]: *** Waiting for unfinished jobs....
  HOSTCC  scripts/dtc/flattree.o
  HOSTCC  scripts/dtc/fstree.o
  HOSTCC  scripts/dtc/data.o
  HOSTCC  scripts/dtc/livetree.o
  HOSTCC  scripts/dtc/treesource.o
  HOSTCC  scripts/dtc/srcpos.o
  HOSTCC  scripts/dtc/checks.o
  HOSTCC  scripts/dtc/util.o
  SHIPPED scripts/dtc/dtc-lexer.lex.c
  SHIPPED scripts/dtc/dtc-parser.tab.h
  SHIPPED scripts/dtc/dtc-parser.tab.c
  HOSTCC  scripts/dtc/dtc-lexer.lex.o
  HOSTCC  scripts/dtc/dtc-parser.tab.o
  HOSTLD  scripts/dtc/dtc
make: *** [Makefile:622: scripts] Error 2
