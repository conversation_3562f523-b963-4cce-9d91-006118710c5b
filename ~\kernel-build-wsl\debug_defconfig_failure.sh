#!/bin/bash
# Debug script to investigate defconfig failures in WSL native filesystem

echo "========================================"
echo "  Defconfig Failure Debug Script"
echo "========================================"
echo ""

# Check we're in the right directory
if [[ ! -f "Makefile" ]] || [[ ! -d "arch" ]]; then
    echo "❌ Error: Run this from the kernel root directory"
    exit 1
fi

echo "Working in: $(pwd)"
echo ""

# Set environment variables
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export ANDROID_MAJOR_VERSION=q

echo "Environment:"
echo "  ARCH=$ARCH"
echo "  CROSS_COMPILE=$CROSS_COMPILE"
echo "  ANDROID_MAJOR_VERSION=$ANDROID_MAJOR_VERSION"
echo ""

# Test 1: Check if basic make works
echo "=== Test 1: Basic Make Test ==="
if make help > /dev/null 2>&1; then
    echo "✓ Basic make works"
else
    echo "❌ Basic make fails"
    echo "Trying make help with verbose output:"
    make help
fi
echo ""

# Test 2: Check specific defconfig files exist
echo "=== Test 2: Defconfig Files Check ==="
DEFCONFIGS=("exynos9810_defconfig" "starlte_defconfig" "star2lte_defconfig" "crownlte_defconfig")

for config in "${DEFCONFIGS[@]}"; do
    if [[ -f "arch/arm64/configs/$config" ]]; then
        echo "✓ arch/arm64/configs/$config exists"
    else
        echo "❌ arch/arm64/configs/$config missing"
    fi
done
echo ""

# Test 3: Try defconfig with verbose output
echo "=== Test 3: Verbose Defconfig Test ==="
echo "Trying exynos9810_defconfig with full error output:"
make exynos9810_defconfig 2>&1 | head -20
echo ""

# Test 4: Check for missing dependencies
echo "=== Test 4: Dependency Check ==="
echo "Checking for common missing dependencies..."

# Check for python
if command -v python3 > /dev/null; then
    echo "✓ python3 found"
else
    echo "❌ python3 missing"
fi

# Check for perl
if command -v perl > /dev/null; then
    echo "✓ perl found"
else
    echo "❌ perl missing"
fi

# Check for essential build tools
TOOLS=("awk" "sed" "grep" "find" "xargs")
for tool in "${TOOLS[@]}"; do
    if command -v $tool > /dev/null; then
        echo "✓ $tool found"
    else
        echo "❌ $tool missing"
    fi
done
echo ""

# Test 5: Check scripts directory
echo "=== Test 5: Scripts Directory Check ==="
if [[ -d "scripts" ]]; then
    echo "✓ scripts directory exists"
    echo "Key scripts:"
    ls -la scripts/ | grep -E "(kconfig|conf|mconf)" || echo "No kconfig scripts found"
else
    echo "❌ scripts directory missing"
fi
echo ""

# Test 6: Check for kconfig tools
echo "=== Test 6: Kconfig Tools Check ==="
if [[ -f "scripts/kconfig/conf" ]]; then
    echo "✓ scripts/kconfig/conf exists"
else
    echo "❌ scripts/kconfig/conf missing - trying to build it"
    make scripts/kconfig/conf 2>&1 | head -10
fi
echo ""

# Test 7: Check Kbuild.include issue
echo "=== Test 7: Kbuild.include Analysis ==="
if [[ -f "scripts/Kbuild.include" ]]; then
    echo "Checking scripts/Kbuild.include around the problematic lines..."
    
    # Look for the fetch scripts that were causing issues
    if grep -n "fetch-latest" scripts/Kbuild.include; then
        echo "Found fetch-latest references in Kbuild.include"
    else
        echo "No fetch-latest references found"
    fi
    
    # Check first few lines for any obvious issues
    echo ""
    echo "First 10 lines of Kbuild.include:"
    head -10 scripts/Kbuild.include | cat -n
    
else
    echo "❌ scripts/Kbuild.include not found"
fi
echo ""

# Test 8: Try alternative approach
echo "=== Test 8: Alternative Build Approach ==="
echo "Trying to build kconfig tools manually..."
if make -C scripts/kconfig conf > /dev/null 2>&1; then
    echo "✓ kconfig tools built successfully"
    
    echo "Now trying defconfig again..."
    if make exynos9810_defconfig > /dev/null 2>&1; then
        echo "✅ exynos9810_defconfig now works!"
    else
        echo "❌ defconfig still fails after building kconfig tools"
    fi
else
    echo "❌ Failed to build kconfig tools"
    echo "Error output:"
    make -C scripts/kconfig conf 2>&1 | head -10
fi
echo ""

# Test 9: Check for missing files that might be needed
echo "=== Test 9: Missing Files Check ==="
CRITICAL_FILES=(
    "Kconfig"
    "arch/arm64/Kconfig"
    "scripts/kconfig/Makefile"
    "scripts/Makefile.build"
)

for file in "${CRITICAL_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        echo "✓ $file exists"
    else
        echo "❌ $file missing"
    fi
done
echo ""

echo "========================================"
echo "  Debug Summary"
echo "========================================"
echo ""
echo "If defconfig is still failing, the most likely causes are:"
echo "1. Missing kconfig tools (need to build scripts/kconfig/conf)"
echo "2. Kernel configuration issues specific to this kernel version"
echo "3. Missing dependencies not caught by package installation"
echo ""
echo "Try these manual steps:"
echo "1. export ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- ANDROID_MAJOR_VERSION=q"
echo "2. make -C scripts/kconfig conf"
echo "3. make exynos9810_defconfig"
echo ""
echo "If that works, the issue was missing kconfig tools."
echo "If it still fails, there may be kernel-specific configuration problems."
