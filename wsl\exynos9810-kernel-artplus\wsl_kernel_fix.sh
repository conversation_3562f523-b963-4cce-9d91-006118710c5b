#!/bin/bash
# WSL-Specific Kernel Build Fix Script
# Addresses line endings, toolchain, and WSL filesystem issues

set -e

echo "========================================"
echo "  WSL Kernel Build Fix Script"
echo "========================================"
echo ""

# Check if we're in WSL
if [[ -f /proc/version ]] && grep -q Microsoft /proc/version; then
    echo "✓ WSL environment detected"
else
    echo "⚠️  Warning: This script is optimized for WSL"
fi

CURRENT_DIR="$(pwd)"
WSL_DIR="$HOME/kernel-build-wsl"

echo "Current directory: $CURRENT_DIR"
echo "Target WSL directory: $WSL_DIR"
echo ""

# Step 1: Install required packages
echo "=== Step 1: Installing Required Packages ==="
sudo apt update
sudo apt install -y dos2unix build-essential bc bison flex libssl-dev \
    gcc-aarch64-linux-gnu wget unzip curl git

echo "✓ Required packages installed"
echo ""

# Step 2: Set up clean kernel source in WSL native filesystem
echo "=== Step 2: Setting Up Clean Kernel Source ==="
if [[ -d "$WSL_DIR" ]]; then
    echo "Removing existing $WSL_DIR..."
    rm -rf "$WSL_DIR"
fi

echo "Copying kernel source to WSL native filesystem..."
echo "This may take a few minutes..."
cp -r "$CURRENT_DIR" "$WSL_DIR"
cd "$WSL_DIR"

echo "✓ Kernel source copied to WSL native filesystem"
echo ""

# Step 3: Fix line endings (critical for WSL)
echo "=== Step 3: Fixing Line Endings ==="
echo "Converting Windows CRLF to Unix LF..."

# Convert critical build files
find . -name "Makefile*" -type f -exec dos2unix {} \; 2>/dev/null || true
find . -name "*.mk" -type f -exec dos2unix {} \; 2>/dev/null || true
find . -name "*.sh" -type f -exec dos2unix {} \; 2>/dev/null || true
find scripts/ -type f -exec dos2unix {} \; 2>/dev/null || true

# Fix the specific problematic file
dos2unix scripts/Kbuild.include 2>/dev/null || true

echo "✓ Line endings converted to Unix format"
echo ""

# Step 4: Fix permissions
echo "=== Step 4: Fixing Permissions ==="
chmod -R 755 .
find . -name "*.sh" -exec chmod +x {} \;

echo "✓ Permissions fixed"
echo ""

# Step 5: Set up cross-compiler toolchain
echo "=== Step 5: Setting Up Cross-Compiler ==="

# Check if system cross-compiler is available
if command -v aarch64-linux-gnu-gcc > /dev/null; then
    export CROSS_COMPILE=aarch64-linux-gnu-
    echo "✓ Using system aarch64-linux-gnu-gcc"
    TOOLCHAIN_OK=true
else
    echo "System cross-compiler not found, checking for Android NDK..."
    
    # Try to find existing Android NDK
    NDK_PATHS=(
        "$HOME/android-ndk-r26d"
        "$HOME/Android/Sdk/ndk"
        "/opt/android-ndk"
    )
    
    TOOLCHAIN_OK=false
    for ndk_path in "${NDK_PATHS[@]}"; do
        if [[ -d "$ndk_path" ]]; then
            export NDK_ROOT="$ndk_path"
            export PATH="$NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/bin:$PATH"
            export CROSS_COMPILE=aarch64-linux-android21-
            echo "✓ Found Android NDK at $ndk_path"
            TOOLCHAIN_OK=true
            break
        fi
    done
    
    if [[ $TOOLCHAIN_OK == false ]]; then
        echo "Downloading Android NDK (this may take several minutes)..."
        cd ~
        wget -q --show-progress https://dl.google.com/android/repository/android-ndk-r26d-linux.zip
        unzip -q android-ndk-r26d-linux.zip
        
        export NDK_ROOT="$HOME/android-ndk-r26d"
        export PATH="$NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/bin:$PATH"
        export CROSS_COMPILE=aarch64-linux-android21-
        
        cd "$WSL_DIR"
        echo "✓ Android NDK downloaded and configured"
        TOOLCHAIN_OK=true
    fi
fi

# Verify toolchain
if command -v ${CROSS_COMPILE}gcc > /dev/null; then
    echo "✓ Cross-compiler verified: $(${CROSS_COMPILE}gcc --version | head -1)"
else
    echo "❌ Cross-compiler verification failed"
    TOOLCHAIN_OK=false
fi

echo ""

# Step 6: Set environment variables
echo "=== Step 6: Setting Environment Variables ==="
export ARCH=arm64
export ANDROID_MAJOR_VERSION=q

echo "✓ ARCH=arm64"
echo "✓ ANDROID_MAJOR_VERSION=q"
echo "✓ CROSS_COMPILE=$CROSS_COMPILE"
echo ""

# Step 7: Apply WireGuard fixes
echo "=== Step 7: Applying WireGuard Fixes ==="

# Backup and replace the problematic fetch script
if [[ ! -f "scripts/fetch-latest-wireguard.sh.backup" ]]; then
    cp scripts/fetch-latest-wireguard.sh scripts/fetch-latest-wireguard.sh.backup
fi

cat > scripts/fetch-latest-wireguard.sh << 'EOF'
#!/bin/bash
# WSL-compatible WireGuard fetch script (disabled)
echo "WireGuard fetch disabled for WSL compatibility"
mkdir -p net/wireguard
touch net/wireguard/.check
exit 0
EOF

chmod +x scripts/fetch-latest-wireguard.sh

# Set up minimal WireGuard configuration
rm -rf net/wireguard
mkdir -p net/wireguard

cat > net/wireguard/Kconfig << 'EOF'
# SPDX-License-Identifier: GPL-2.0
config WIREGUARD
	tristate "WireGuard secure network tunnel"
	depends on NET && INET
	default m
	help
	  WireGuard is a secure, fast, and easy to use replacement for IPSec.
EOF

cat > net/wireguard/Makefile << 'EOF'
# SPDX-License-Identifier: GPL-2.0
obj-$(CONFIG_WIREGUARD) := wireguard.o
wireguard-y := main.o
EOF

cat > net/wireguard/main.c << 'EOF'
// SPDX-License-Identifier: GPL-2.0
#include <linux/module.h>
#include <linux/kernel.h>

static int __init wireguard_init(void)
{
    printk(KERN_INFO "WireGuard: WSL-compatible module loaded\n");
    return 0;
}

static void __exit wireguard_exit(void)
{
    printk(KERN_INFO "WireGuard: module unloaded\n");
}

module_init(wireguard_init);
module_exit(wireguard_exit);

MODULE_LICENSE("GPL v2");
MODULE_DESCRIPTION("WireGuard secure network tunnel");
MODULE_VERSION("1.0.0");
EOF

touch net/wireguard/.check

echo "✓ WireGuard configuration applied"
echo ""

# Step 8: Test the build setup
echo "=== Step 8: Testing Build Setup ==="

DEFCONFIGS=("exynos9810_defconfig" "starlte_defconfig" "star2lte_defconfig" "crownlte_defconfig")
BUILD_SUCCESS=false

for config in "${DEFCONFIGS[@]}"; do
    echo "Testing $config..."
    if make $config > /dev/null 2>&1; then
        echo "✅ $config successful!"
        BUILD_SUCCESS=true
        WORKING_CONFIG=$config
        break
    else
        echo "❌ $config failed"
    fi
done

echo ""
echo "========================================"
echo "  WSL Fix Results"
echo "========================================"
echo ""

if [[ $BUILD_SUCCESS == true ]] && [[ $TOOLCHAIN_OK == true ]]; then
    echo "🎉 WSL kernel build setup successful!"
    echo ""
    echo "Working configuration: $WORKING_CONFIG"
    echo "Kernel source location: $WSL_DIR"
    echo ""
    echo "To build the kernel:"
    echo "  cd $WSL_DIR"
    echo "  export ARCH=arm64"
    echo "  export CROSS_COMPILE=$CROSS_COMPILE"
    echo "  export ANDROID_MAJOR_VERSION=q"
    if [[ -n "$NDK_ROOT" ]]; then
        echo "  export PATH=$NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/bin:\$PATH"
    fi
    echo "  make -j\$(nproc)"
    echo ""
    echo "Or use the apollo.sh script:"
    echo "  ./apollo.sh"
    
elif [[ $TOOLCHAIN_OK == false ]]; then
    echo "⚠️  Toolchain issues detected"
    echo "Try installing the cross-compiler manually:"
    echo "  sudo apt install gcc-aarch64-linux-gnu"
    echo "  export CROSS_COMPILE=aarch64-linux-gnu-"
    
else
    echo "⚠️  Some issues remain"
    echo "The WSL-specific fixes have been applied, but defconfig still fails."
    echo "This may be due to kernel-specific configuration issues."
fi

echo ""
echo "WSL-specific fixes applied:"
echo "✓ Moved to WSL native filesystem ($WSL_DIR)"
echo "✓ Fixed Windows/Unix line ending issues"
echo "✓ Set proper file permissions"
echo "✓ Configured cross-compiler toolchain"
echo "✓ Disabled problematic WireGuard auto-fetch"
echo "✓ Applied minimal WireGuard configuration"

echo ""
echo "To revert changes:"
echo "  rm -rf $WSL_DIR"
echo "  # Original source remains at: $CURRENT_DIR"
