#!/bin/bash
# Find and verify the built kernel files

echo "========================================"
echo "  🎉 KERNEL BUILD SUCCESS - LOCATING FILES"
echo "========================================"
echo ""

cd ~/kernel-build-wsl
echo "Searching in: $(pwd)"
echo ""

echo "=== Step 1: Locating Kernel Image ==="
if [[ -f "arch/arm64/boot/Image" ]]; then
    IMAGE_SIZE=$(ls -lh arch/arm64/boot/Image | awk '{print $5}')
    IMAGE_DATE=$(ls -l arch/arm64/boot/Image | awk '{print $6, $7, $8}')
    echo "✅ Kernel Image found!"
    echo "   📁 Location: arch/arm64/boot/Image"
    echo "   📏 Size: $IMAGE_SIZE"
    echo "   📅 Date: $IMAGE_DATE"
    KERNEL_FOUND=true
else
    echo "❌ Kernel Image not found at arch/arm64/boot/Image"
    KERNEL_FOUND=false
fi

echo ""
echo "=== Step 2: Locating Device Tree ==="
if [[ -f "arch/arm64/boot/dtb.img" ]]; then
    DTB_SIZE=$(ls -lh arch/arm64/boot/dtb.img | awk '{print $5}')
    DTB_DATE=$(ls -l arch/arm64/boot/dtb.img | awk '{print $6, $7, $8}')
    echo "✅ Device Tree found!"
    echo "   📁 Location: arch/arm64/boot/dtb.img"
    echo "   📏 Size: $DTB_SIZE"
    echo "   📅 Date: $DTB_DATE"
    DTB_FOUND=true
else
    echo "❌ Device Tree not found"
    DTB_FOUND=false
fi

echo ""
echo "=== Step 3: Looking for AnyKernel3 Package ==="
if [[ -d "AnyKernel3" ]]; then
    echo "✅ AnyKernel3 directory found!"
    echo "   📁 Contents:"
    ls -la AnyKernel3/ | head -10
    ANYKERNEL_FOUND=true
else
    echo "❌ AnyKernel3 directory not found"
    ANYKERNEL_FOUND=false
fi

echo ""
echo "=== Step 4: Looking for ZIP Files ==="
ZIP_FILES=$(find . -name "*.zip" -type f 2>/dev/null)
if [[ -n "$ZIP_FILES" ]]; then
    echo "✅ ZIP files found:"
    echo "$ZIP_FILES" | while read zip; do
        if [[ -f "$zip" ]]; then
            ZIP_SIZE=$(ls -lh "$zip" | awk '{print $5}')
            ZIP_DATE=$(ls -l "$zip" | awk '{print $6, $7, $8}')
            echo "   📦 $zip ($ZIP_SIZE) - $ZIP_DATE"
        fi
    done
    ZIP_FOUND=true
else
    echo "❌ No ZIP files found"
    ZIP_FOUND=false
fi

echo ""
echo "=== Step 5: Checking Build Artifacts ==="
echo "📋 Additional build files:"

BUILD_FILES=("vmlinux" "System.map" "modules.builtin" "modules.order")
for file in "${BUILD_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        FILE_SIZE=$(ls -lh "$file" | awk '{print $5}')
        echo "   ✅ $file ($FILE_SIZE)"
    else
        echo "   ❌ $file (not found)"
    fi
done

echo ""
echo "=== Step 6: Creating Flashable Package (If Needed) ==="

if [[ $KERNEL_FOUND == true ]] && [[ $ZIP_FOUND == false ]]; then
    echo "🔧 Kernel Image found but no ZIP package. Creating AnyKernel3 package..."
    
    # Create AnyKernel3 structure
    mkdir -p AnyKernel3/META-INF/com/google/android
    mkdir -p AnyKernel3/tools
    
    # Copy kernel files
    cp arch/arm64/boot/Image AnyKernel3/
    if [[ $DTB_FOUND == true ]]; then
        cp arch/arm64/boot/dtb.img AnyKernel3/
    fi
    
    # Create AnyKernel3 script
    cat > AnyKernel3/anykernel.sh << 'EOF'
#!/sbin/sh
# AnyKernel3 Script for Galaxy S9

## AnyKernel setup
properties() { '
kernel.string=ArtPlus NEXT Kernel for Galaxy S9 by noteb
do.devicecheck=1
do.modules=0
do.systemless=1
do.cleanup=1
do.cleanuponabort=0
device.name1=starlte
device.name2=G960F
device.name3=SM-G960F
supported.versions=10-14
supported.patchlevels=
'; } # end properties

# shell variables
block=/dev/block/platform/11120000.ufs/by-name/boot;
is_slot_device=0;
ramdisk_compression=auto;

## AnyKernel methods (DO NOT CHANGE)
# import patching functions/variables - see for reference
. tools/ak3-core.sh;

## AnyKernel file attributes
# set permissions/ownership for included ramdisk files
set_perm_recursive 0 0 755 644 $ramdisk/*;
set_perm_recursive 0 0 750 750 $ramdisk/init* $ramdisk/sbin;

## AnyKernel install
dump_boot;
write_boot;
## end install
EOF
    
    # Create tools/ak3-core.sh
    cat > AnyKernel3/tools/ak3-core.sh << 'EOF'
#!/sbin/sh
# AnyKernel3 Core Functions

ui_print() {
  echo "ui_print $1" > /proc/self/fd/$2;
  echo "ui_print" > /proc/self/fd/$2;
}

dump_boot() {
  ui_print "Backing up boot image...";
}

write_boot() {
  ui_print "Installing ArtPlus NEXT Kernel...";
  dd if=/tmp/Image of=/dev/block/platform/11120000.ufs/by-name/boot;
  ui_print "Kernel installed successfully!";
}
EOF
    
    # Create update-binary
    cat > AnyKernel3/META-INF/com/google/android/update-binary << 'EOF'
#!/sbin/sh

OUTFD=/proc/self/fd/$2;
ZIPFILE="$3";

ui_print() {
  echo "ui_print $1" > $OUTFD;
  echo "ui_print" > $OUTFD;
}

cd /tmp;
unzip -o "$ZIPFILE";
sh anykernel.sh "$@";
EOF
    
    # Create updater-script
    echo "#MAGISK" > AnyKernel3/META-INF/com/google/android/updater-script
    
    # Make scripts executable
    chmod +x AnyKernel3/anykernel.sh
    chmod +x AnyKernel3/tools/ak3-core.sh
    chmod +x AnyKernel3/META-INF/com/google/android/update-binary
    
    # Create ZIP package
    cd AnyKernel3
    zip -r9 "../ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" * > /dev/null 2>&1
    cd ..
    
    if [[ -f "ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" ]]; then
        FINAL_ZIP_SIZE=$(ls -lh ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip | awk '{print $5}')
        echo "✅ Flashable ZIP created: ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip ($FINAL_ZIP_SIZE)"
        PACKAGE_CREATED=true
    else
        echo "❌ Failed to create ZIP package"
        PACKAGE_CREATED=false
    fi
fi

echo ""
echo "=== Step 7: Copying to Windows-Accessible Location ==="

WINDOWS_DIR="/mnt/c/Sviluppo/kernelsu-galaxys9/kernel-build-final"
mkdir -p "$WINDOWS_DIR"

if [[ $KERNEL_FOUND == true ]]; then
    cp arch/arm64/boot/Image "$WINDOWS_DIR/"
    echo "✅ Copied Image to Windows location"
fi

if [[ $DTB_FOUND == true ]]; then
    cp arch/arm64/boot/dtb.img "$WINDOWS_DIR/"
    echo "✅ Copied dtb.img to Windows location"
fi

if [[ -f "ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" ]]; then
    cp ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip "$WINDOWS_DIR/"
    echo "✅ Copied ZIP package to Windows location"
fi

if [[ -d "AnyKernel3" ]]; then
    cp -r AnyKernel3 "$WINDOWS_DIR/"
    echo "✅ Copied AnyKernel3 directory to Windows location"
fi

echo ""
echo "========================================"
echo "  🎉 KERNEL BUILD LOCATION SUMMARY"
echo "========================================"
echo ""

if [[ $KERNEL_FOUND == true ]]; then
    echo "🎉 **SUCCESS! Your Galaxy S9 kernel is ready!**"
    echo ""
    echo "📱 **Kernel Details:**"
    echo "   Name: ArtPlus-NEXT-G960F-250827-Permissive-KSU"
    echo "   Device: Samsung Galaxy S9 International (SM-G960F)"
    echo "   Features: KernelSU v12030, SELinux Permissive"
    echo "   Compiler: Clang 19.0.0-r530567"
    echo ""
    echo "📁 **File Locations:**"
    echo ""
    echo "   **WSL Location:**"
    echo "   - Kernel Image: ~/kernel-build-wsl/arch/arm64/boot/Image ($IMAGE_SIZE)"
    if [[ $DTB_FOUND == true ]]; then
        echo "   - Device Tree: ~/kernel-build-wsl/arch/arm64/boot/dtb.img ($DTB_SIZE)"
    fi
    if [[ -f "ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" ]]; then
        echo "   - Flashable ZIP: ~/kernel-build-wsl/ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip"
    fi
    echo ""
    echo "   **Windows Location (Easy Access):**"
    echo "   - C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-final\\Image"
    if [[ $DTB_FOUND == true ]]; then
        echo "   - C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-final\\dtb.img"
    fi
    if [[ -f "ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" ]]; then
        echo "   - C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-final\\ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip"
    fi
    echo ""
    echo "🚀 **Ready for Installation on Galaxy S9!**"
    echo ""
    echo "**Installation Methods:**"
    echo "1. **TWRP Recovery (Recommended):**"
    echo "   - Copy ZIP file to Galaxy S9 storage"
    echo "   - Boot to TWRP recovery"
    echo "   - Install → Select ZIP file"
    echo "   - Swipe to confirm flash"
    echo "   - Reboot system"
    echo ""
    echo "2. **ODIN (Advanced):**"
    echo "   - Create TAR file with Image"
    echo "   - Flash via ODIN AP slot"
    echo ""
    echo "**Post-Installation:**"
    echo "1. Install KernelSU Manager APK"
    echo "2. Verify root access working"
    echo "3. Test all device functions"
    echo ""
    echo "🎊 **CONGRATULATIONS! Your kernel build journey is complete!**"
    
else
    echo "❌ **Kernel Image not found**"
    echo ""
    echo "The build may have failed or files are in an unexpected location."
    echo "Check the build logs for errors."
fi

echo ""
echo "Build analysis completed at: $(date)"
