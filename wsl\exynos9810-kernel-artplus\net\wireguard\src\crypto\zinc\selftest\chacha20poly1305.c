// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright (C) 2015-2019 <PERSON> <<PERSON>@zx2c4.com>. All Rights Reserved.
 */

struct chacha20poly1305_testvec {
	const u8 *input, *output, *assoc, *nonce, *key;
	size_t ilen, alen, nlen;
	bool failure;
};

/* The first of these are the ChaCha20-Poly1305 AEAD test vectors from RFC7539
 * 2.8.2. After they are generated by reference implementations. And the final
 * marked ones are taken from wycheproof, but we only do these for the encrypt
 * side, because mostly we're stressing the primitives rather than the actual
 * chapoly construction. This also requires adding a 96-bit nonce construction,
 * just for the purpose of the tests.
 */

static const u8 enc_input001[] __initconst = {
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74,
	0x2d, 0x44, 0x72, 0x61, 0x66, 0x74, 0x73, 0x20,
	0x61, 0x72, 0x65, 0x20, 0x64, 0x72, 0x61, 0x66,
	0x74, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x61, 0x20,
	0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x20,
	0x6f, 0x66, 0x20, 0x73, 0x69, 0x78, 0x20, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x20, 0x61, 0x6e,
	0x64, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65,
	0x20, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x2c, 0x20, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x64, 0x2c, 0x20, 0x6f, 0x72, 0x20, 0x6f,
	0x62, 0x73, 0x6f, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x20, 0x62, 0x79, 0x20, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x61, 0x74, 0x20, 0x61,
	0x6e, 0x79, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x20, 0x49, 0x74, 0x20, 0x69, 0x73, 0x20, 0x69,
	0x6e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x70, 0x72,
	0x69, 0x61, 0x74, 0x65, 0x20, 0x74, 0x6f, 0x20,
	0x75, 0x73, 0x65, 0x20, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x65, 0x74, 0x2d, 0x44, 0x72, 0x61,
	0x66, 0x74, 0x73, 0x20, 0x61, 0x73, 0x20, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x20, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x20, 0x6f, 0x72, 0x20, 0x74, 0x6f, 0x20,
	0x63, 0x69, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65,
	0x6d, 0x20, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x61, 0x73, 0x20,
	0x2f, 0xe2, 0x80, 0x9c, 0x77, 0x6f, 0x72, 0x6b,
	0x20, 0x69, 0x6e, 0x20, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x2f, 0xe2, 0x80,
	0x9d
};
static const u8 enc_output001[] __initconst = {
	0x64, 0xa0, 0x86, 0x15, 0x75, 0x86, 0x1a, 0xf4,
	0x60, 0xf0, 0x62, 0xc7, 0x9b, 0xe6, 0x43, 0xbd,
	0x5e, 0x80, 0x5c, 0xfd, 0x34, 0x5c, 0xf3, 0x89,
	0xf1, 0x08, 0x67, 0x0a, 0xc7, 0x6c, 0x8c, 0xb2,
	0x4c, 0x6c, 0xfc, 0x18, 0x75, 0x5d, 0x43, 0xee,
	0xa0, 0x9e, 0xe9, 0x4e, 0x38, 0x2d, 0x26, 0xb0,
	0xbd, 0xb7, 0xb7, 0x3c, 0x32, 0x1b, 0x01, 0x00,
	0xd4, 0xf0, 0x3b, 0x7f, 0x35, 0x58, 0x94, 0xcf,
	0x33, 0x2f, 0x83, 0x0e, 0x71, 0x0b, 0x97, 0xce,
	0x98, 0xc8, 0xa8, 0x4a, 0xbd, 0x0b, 0x94, 0x81,
	0x14, 0xad, 0x17, 0x6e, 0x00, 0x8d, 0x33, 0xbd,
	0x60, 0xf9, 0x82, 0xb1, 0xff, 0x37, 0xc8, 0x55,
	0x97, 0x97, 0xa0, 0x6e, 0xf4, 0xf0, 0xef, 0x61,
	0xc1, 0x86, 0x32, 0x4e, 0x2b, 0x35, 0x06, 0x38,
	0x36, 0x06, 0x90, 0x7b, 0x6a, 0x7c, 0x02, 0xb0,
	0xf9, 0xf6, 0x15, 0x7b, 0x53, 0xc8, 0x67, 0xe4,
	0xb9, 0x16, 0x6c, 0x76, 0x7b, 0x80, 0x4d, 0x46,
	0xa5, 0x9b, 0x52, 0x16, 0xcd, 0xe7, 0xa4, 0xe9,
	0x90, 0x40, 0xc5, 0xa4, 0x04, 0x33, 0x22, 0x5e,
	0xe2, 0x82, 0xa1, 0xb0, 0xa0, 0x6c, 0x52, 0x3e,
	0xaf, 0x45, 0x34, 0xd7, 0xf8, 0x3f, 0xa1, 0x15,
	0x5b, 0x00, 0x47, 0x71, 0x8c, 0xbc, 0x54, 0x6a,
	0x0d, 0x07, 0x2b, 0x04, 0xb3, 0x56, 0x4e, 0xea,
	0x1b, 0x42, 0x22, 0x73, 0xf5, 0x48, 0x27, 0x1a,
	0x0b, 0xb2, 0x31, 0x60, 0x53, 0xfa, 0x76, 0x99,
	0x19, 0x55, 0xeb, 0xd6, 0x31, 0x59, 0x43, 0x4e,
	0xce, 0xbb, 0x4e, 0x46, 0x6d, 0xae, 0x5a, 0x10,
	0x73, 0xa6, 0x72, 0x76, 0x27, 0x09, 0x7a, 0x10,
	0x49, 0xe6, 0x17, 0xd9, 0x1d, 0x36, 0x10, 0x94,
	0xfa, 0x68, 0xf0, 0xff, 0x77, 0x98, 0x71, 0x30,
	0x30, 0x5b, 0xea, 0xba, 0x2e, 0xda, 0x04, 0xdf,
	0x99, 0x7b, 0x71, 0x4d, 0x6c, 0x6f, 0x2c, 0x29,
	0xa6, 0xad, 0x5c, 0xb4, 0x02, 0x2b, 0x02, 0x70,
	0x9b, 0xee, 0xad, 0x9d, 0x67, 0x89, 0x0c, 0xbb,
	0x22, 0x39, 0x23, 0x36, 0xfe, 0xa1, 0x85, 0x1f,
	0x38
};
static const u8 enc_assoc001[] __initconst = {
	0xf3, 0x33, 0x88, 0x86, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x4e, 0x91
};
static const u8 enc_nonce001[] __initconst = {
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08
};
static const u8 enc_key001[] __initconst = {
	0x1c, 0x92, 0x40, 0xa5, 0xeb, 0x55, 0xd3, 0x8a,
	0xf3, 0x33, 0x88, 0x86, 0x04, 0xf6, 0xb5, 0xf0,
	0x47, 0x39, 0x17, 0xc1, 0x40, 0x2b, 0x80, 0x09,
	0x9d, 0xca, 0x5c, 0xbc, 0x20, 0x70, 0x75, 0xc0
};

static const u8 enc_input002[] __initconst = { };
static const u8 enc_output002[] __initconst = {
	0xea, 0xe0, 0x1e, 0x9e, 0x2c, 0x91, 0xaa, 0xe1,
	0xdb, 0x5d, 0x99, 0x3f, 0x8a, 0xf7, 0x69, 0x92
};
static const u8 enc_assoc002[] __initconst = { };
static const u8 enc_nonce002[] __initconst = {
	0xca, 0xbf, 0x33, 0x71, 0x32, 0x45, 0x77, 0x8e
};
static const u8 enc_key002[] __initconst = {
	0x4c, 0xf5, 0x96, 0x83, 0x38, 0xe6, 0xae, 0x7f,
	0x2d, 0x29, 0x25, 0x76, 0xd5, 0x75, 0x27, 0x86,
	0x91, 0x9a, 0x27, 0x7a, 0xfb, 0x46, 0xc5, 0xef,
	0x94, 0x81, 0x79, 0x57, 0x14, 0x59, 0x40, 0x68
};

static const u8 enc_input003[] __initconst = { };
static const u8 enc_output003[] __initconst = {
	0xdd, 0x6b, 0x3b, 0x82, 0xce, 0x5a, 0xbd, 0xd6,
	0xa9, 0x35, 0x83, 0xd8, 0x8c, 0x3d, 0x85, 0x77
};
static const u8 enc_assoc003[] __initconst = {
	0x33, 0x10, 0x41, 0x12, 0x1f, 0xf3, 0xd2, 0x6b
};
static const u8 enc_nonce003[] __initconst = {
	0x3d, 0x86, 0xb5, 0x6b, 0xc8, 0xa3, 0x1f, 0x1d
};
static const u8 enc_key003[] __initconst = {
	0x2d, 0xb0, 0x5d, 0x40, 0xc8, 0xed, 0x44, 0x88,
	0x34, 0xd1, 0x13, 0xaf, 0x57, 0xa1, 0xeb, 0x3a,
	0x2a, 0x80, 0x51, 0x36, 0xec, 0x5b, 0xbc, 0x08,
	0x93, 0x84, 0x21, 0xb5, 0x13, 0x88, 0x3c, 0x0d
};

static const u8 enc_input004[] __initconst = {
	0xa4
};
static const u8 enc_output004[] __initconst = {
	0xb7, 0x1b, 0xb0, 0x73, 0x59, 0xb0, 0x84, 0xb2,
	0x6d, 0x8e, 0xab, 0x94, 0x31, 0xa1, 0xae, 0xac,
	0x89
};
static const u8 enc_assoc004[] __initconst = {
	0x6a, 0xe2, 0xad, 0x3f, 0x88, 0x39, 0x5a, 0x40
};
static const u8 enc_nonce004[] __initconst = {
	0xd2, 0x32, 0x1f, 0x29, 0x28, 0xc6, 0xc4, 0xc4
};
static const u8 enc_key004[] __initconst = {
	0x4b, 0x28, 0x4b, 0xa3, 0x7b, 0xbe, 0xe9, 0xf8,
	0x31, 0x80, 0x82, 0xd7, 0xd8, 0xe8, 0xb5, 0xa1,
	0xe2, 0x18, 0x18, 0x8a, 0x9c, 0xfa, 0xa3, 0x3d,
	0x25, 0x71, 0x3e, 0x40, 0xbc, 0x54, 0x7a, 0x3e
};

static const u8 enc_input005[] __initconst = {
	0x2d
};
static const u8 enc_output005[] __initconst = {
	0xbf, 0xe1, 0x5b, 0x0b, 0xdb, 0x6b, 0xf5, 0x5e,
	0x6c, 0x5d, 0x84, 0x44, 0x39, 0x81, 0xc1, 0x9c,
	0xac
};
static const u8 enc_assoc005[] __initconst = { };
static const u8 enc_nonce005[] __initconst = {
	0x20, 0x1c, 0xaa, 0x5f, 0x9c, 0xbf, 0x92, 0x30
};
static const u8 enc_key005[] __initconst = {
	0x66, 0xca, 0x9c, 0x23, 0x2a, 0x4b, 0x4b, 0x31,
	0x0e, 0x92, 0x89, 0x8b, 0xf4, 0x93, 0xc7, 0x87,
	0x98, 0xa3, 0xd8, 0x39, 0xf8, 0xf4, 0xa7, 0x01,
	0xc0, 0x2e, 0x0a, 0xa6, 0x7e, 0x5a, 0x78, 0x87
};

static const u8 enc_input006[] __initconst = {
	0x33, 0x2f, 0x94, 0xc1, 0xa4, 0xef, 0xcc, 0x2a,
	0x5b, 0xa6, 0xe5, 0x8f, 0x1d, 0x40, 0xf0, 0x92,
	0x3c, 0xd9, 0x24, 0x11, 0xa9, 0x71, 0xf9, 0x37,
	0x14, 0x99, 0xfa, 0xbe, 0xe6, 0x80, 0xde, 0x50,
	0xc9, 0x96, 0xd4, 0xb0, 0xec, 0x9e, 0x17, 0xec,
	0xd2, 0x5e, 0x72, 0x99, 0xfc, 0x0a, 0xe1, 0xcb,
	0x48, 0xd2, 0x85, 0xdd, 0x2f, 0x90, 0xe0, 0x66,
	0x3b, 0xe6, 0x20, 0x74, 0xbe, 0x23, 0x8f, 0xcb,
	0xb4, 0xe4, 0xda, 0x48, 0x40, 0xa6, 0xd1, 0x1b,
	0xc7, 0x42, 0xce, 0x2f, 0x0c, 0xa6, 0x85, 0x6e,
	0x87, 0x37, 0x03, 0xb1, 0x7c, 0x25, 0x96, 0xa3,
	0x05, 0xd8, 0xb0, 0xf4, 0xed, 0xea, 0xc2, 0xf0,
	0x31, 0x98, 0x6c, 0xd1, 0x14, 0x25, 0xc0, 0xcb,
	0x01, 0x74, 0xd0, 0x82, 0xf4, 0x36, 0xf5, 0x41,
	0xd5, 0xdc, 0xca, 0xc5, 0xbb, 0x98, 0xfe, 0xfc,
	0x69, 0x21, 0x70, 0xd8, 0xa4, 0x4b, 0xc8, 0xde,
	0x8f
};
static const u8 enc_output006[] __initconst = {
	0x8b, 0x06, 0xd3, 0x31, 0xb0, 0x93, 0x45, 0xb1,
	0x75, 0x6e, 0x26, 0xf9, 0x67, 0xbc, 0x90, 0x15,
	0x81, 0x2c, 0xb5, 0xf0, 0xc6, 0x2b, 0xc7, 0x8c,
	0x56, 0xd1, 0xbf, 0x69, 0x6c, 0x07, 0xa0, 0xda,
	0x65, 0x27, 0xc9, 0x90, 0x3d, 0xef, 0x4b, 0x11,
	0x0f, 0x19, 0x07, 0xfd, 0x29, 0x92, 0xd9, 0xc8,
	0xf7, 0x99, 0x2e, 0x4a, 0xd0, 0xb8, 0x2c, 0xdc,
	0x93, 0xf5, 0x9e, 0x33, 0x78, 0xd1, 0x37, 0xc3,
	0x66, 0xd7, 0x5e, 0xbc, 0x44, 0xbf, 0x53, 0xa5,
	0xbc, 0xc4, 0xcb, 0x7b, 0x3a, 0x8e, 0x7f, 0x02,
	0xbd, 0xbb, 0xe7, 0xca, 0xa6, 0x6c, 0x6b, 0x93,
	0x21, 0x93, 0x10, 0x61, 0xe7, 0x69, 0xd0, 0x78,
	0xf3, 0x07, 0x5a, 0x1a, 0x8f, 0x73, 0xaa, 0xb1,
	0x4e, 0xd3, 0xda, 0x4f, 0xf3, 0x32, 0xe1, 0x66,
	0x3e, 0x6c, 0xc6, 0x13, 0xba, 0x06, 0x5b, 0xfc,
	0x6a, 0xe5, 0x6f, 0x60, 0xfb, 0x07, 0x40, 0xb0,
	0x8c, 0x9d, 0x84, 0x43, 0x6b, 0xc1, 0xf7, 0x8d,
	0x8d, 0x31, 0xf7, 0x7a, 0x39, 0x4d, 0x8f, 0x9a,
	0xeb
};
static const u8 enc_assoc006[] __initconst = {
	0x70, 0xd3, 0x33, 0xf3, 0x8b, 0x18, 0x0b
};
static const u8 enc_nonce006[] __initconst = {
	0xdf, 0x51, 0x84, 0x82, 0x42, 0x0c, 0x75, 0x9c
};
static const u8 enc_key006[] __initconst = {
	0x68, 0x7b, 0x8d, 0x8e, 0xe3, 0xc4, 0xdd, 0xae,
	0xdf, 0x72, 0x7f, 0x53, 0x72, 0x25, 0x1e, 0x78,
	0x91, 0xcb, 0x69, 0x76, 0x1f, 0x49, 0x93, 0xf9,
	0x6f, 0x21, 0xcc, 0x39, 0x9c, 0xad, 0xb1, 0x01
};

static const u8 enc_input007[] __initconst = {
	0x9b, 0x18, 0xdb, 0xdd, 0x9a, 0x0f, 0x3e, 0xa5,
	0x15, 0x17, 0xde, 0xdf, 0x08, 0x9d, 0x65, 0x0a,
	0x67, 0x30, 0x12, 0xe2, 0x34, 0x77, 0x4b, 0xc1,
	0xd9, 0xc6, 0x1f, 0xab, 0xc6, 0x18, 0x50, 0x17,
	0xa7, 0x9d, 0x3c, 0xa6, 0xc5, 0x35, 0x8c, 0x1c,
	0xc0, 0xa1, 0x7c, 0x9f, 0x03, 0x89, 0xca, 0xe1,
	0xe6, 0xe9, 0xd4, 0xd3, 0x88, 0xdb, 0xb4, 0x51,
	0x9d, 0xec, 0xb4, 0xfc, 0x52, 0xee, 0x6d, 0xf1,
	0x75, 0x42, 0xc6, 0xfd, 0xbd, 0x7a, 0x8e, 0x86,
	0xfc, 0x44, 0xb3, 0x4f, 0xf3, 0xea, 0x67, 0x5a,
	0x41, 0x13, 0xba, 0xb0, 0xdc, 0xe1, 0xd3, 0x2a,
	0x7c, 0x22, 0xb3, 0xca, 0xac, 0x6a, 0x37, 0x98,
	0x3e, 0x1d, 0x40, 0x97, 0xf7, 0x9b, 0x1d, 0x36,
	0x6b, 0xb3, 0x28, 0xbd, 0x60, 0x82, 0x47, 0x34,
	0xaa, 0x2f, 0x7d, 0xe9, 0xa8, 0x70, 0x81, 0x57,
	0xd4, 0xb9, 0x77, 0x0a, 0x9d, 0x29, 0xa7, 0x84,
	0x52, 0x4f, 0xc2, 0x4a, 0x40, 0x3b, 0x3c, 0xd4,
	0xc9, 0x2a, 0xdb, 0x4a, 0x53, 0xc4, 0xbe, 0x80,
	0xe9, 0x51, 0x7f, 0x8f, 0xc7, 0xa2, 0xce, 0x82,
	0x5c, 0x91, 0x1e, 0x74, 0xd9, 0xd0, 0xbd, 0xd5,
	0xf3, 0xfd, 0xda, 0x4d, 0x25, 0xb4, 0xbb, 0x2d,
	0xac, 0x2f, 0x3d, 0x71, 0x85, 0x7b, 0xcf, 0x3c,
	0x7b, 0x3e, 0x0e, 0x22, 0x78, 0x0c, 0x29, 0xbf,
	0xe4, 0xf4, 0x57, 0xb3, 0xcb, 0x49, 0xa0, 0xfc,
	0x1e, 0x05, 0x4e, 0x16, 0xbc, 0xd5, 0xa8, 0xa3,
	0xee, 0x05, 0x35, 0xc6, 0x7c, 0xab, 0x60, 0x14,
	0x55, 0x1a, 0x8e, 0xc5, 0x88, 0x5d, 0xd5, 0x81,
	0xc2, 0x81, 0xa5, 0xc4, 0x60, 0xdb, 0xaf, 0x77,
	0x91, 0xe1, 0xce, 0xa2, 0x7e, 0x7f, 0x42, 0xe3,
	0xb0, 0x13, 0x1c, 0x1f, 0x25, 0x60, 0x21, 0xe2,
	0x40, 0x5f, 0x99, 0xb7, 0x73, 0xec, 0x9b, 0x2b,
	0xf0, 0x65, 0x11, 0xc8, 0xd0, 0x0a, 0x9f, 0xd3
};
static const u8 enc_output007[] __initconst = {
	0x85, 0x04, 0xc2, 0xed, 0x8d, 0xfd, 0x97, 0x5c,
	0xd2, 0xb7, 0xe2, 0xc1, 0x6b, 0xa3, 0xba, 0xf8,
	0xc9, 0x50, 0xc3, 0xc6, 0xa5, 0xe3, 0xa4, 0x7c,
	0xc3, 0x23, 0x49, 0x5e, 0xa9, 0xb9, 0x32, 0xeb,
	0x8a, 0x7c, 0xca, 0xe5, 0xec, 0xfb, 0x7c, 0xc0,
	0xcb, 0x7d, 0xdc, 0x2c, 0x9d, 0x92, 0x55, 0x21,
	0x0a, 0xc8, 0x43, 0x63, 0x59, 0x0a, 0x31, 0x70,
	0x82, 0x67, 0x41, 0x03, 0xf8, 0xdf, 0xf2, 0xac,
	0xa7, 0x02, 0xd4, 0xd5, 0x8a, 0x2d, 0xc8, 0x99,
	0x19, 0x66, 0xd0, 0xf6, 0x88, 0x2c, 0x77, 0xd9,
	0xd4, 0x0d, 0x6c, 0xbd, 0x98, 0xde, 0xe7, 0x7f,
	0xad, 0x7e, 0x8a, 0xfb, 0xe9, 0x4b, 0xe5, 0xf7,
	0xe5, 0x50, 0xa0, 0x90, 0x3f, 0xd6, 0x22, 0x53,
	0xe3, 0xfe, 0x1b, 0xcc, 0x79, 0x3b, 0xec, 0x12,
	0x47, 0x52, 0xa7, 0xd6, 0x04, 0xe3, 0x52, 0xe6,
	0x93, 0x90, 0x91, 0x32, 0x73, 0x79, 0xb8, 0xd0,
	0x31, 0xde, 0x1f, 0x9f, 0x2f, 0x05, 0x38, 0x54,
	0x2f, 0x35, 0x04, 0x39, 0xe0, 0xa7, 0xba, 0xc6,
	0x52, 0xf6, 0x37, 0x65, 0x4c, 0x07, 0xa9, 0x7e,
	0xb3, 0x21, 0x6f, 0x74, 0x8c, 0xc9, 0xde, 0xdb,
	0x65, 0x1b, 0x9b, 0xaa, 0x60, 0xb1, 0x03, 0x30,
	0x6b, 0xb2, 0x03, 0xc4, 0x1c, 0x04, 0xf8, 0x0f,
	0x64, 0xaf, 0x46, 0xe4, 0x65, 0x99, 0x49, 0xe2,
	0xea, 0xce, 0x78, 0x00, 0xd8, 0x8b, 0xd5, 0x2e,
	0xcf, 0xfc, 0x40, 0x49, 0xe8, 0x58, 0xdc, 0x34,
	0x9c, 0x8c, 0x61, 0xbf, 0x0a, 0x8e, 0xec, 0x39,
	0xa9, 0x30, 0x05, 0x5a, 0xd2, 0x56, 0x01, 0xc7,
	0xda, 0x8f, 0x4e, 0xbb, 0x43, 0xa3, 0x3a, 0xf9,
	0x15, 0x2a, 0xd0, 0xa0, 0x7a, 0x87, 0x34, 0x82,
	0xfe, 0x8a, 0xd1, 0x2d, 0x5e, 0xc7, 0xbf, 0x04,
	0x53, 0x5f, 0x3b, 0x36, 0xd4, 0x25, 0x5c, 0x34,
	0x7a, 0x8d, 0xd5, 0x05, 0xce, 0x72, 0xca, 0xef,
	0x7a, 0x4b, 0xbc, 0xb0, 0x10, 0x5c, 0x96, 0x42,
	0x3a, 0x00, 0x98, 0xcd, 0x15, 0xe8, 0xb7, 0x53
};
static const u8 enc_assoc007[] __initconst = { };
static const u8 enc_nonce007[] __initconst = {
	0xde, 0x7b, 0xef, 0xc3, 0x65, 0x1b, 0x68, 0xb0
};
static const u8 enc_key007[] __initconst = {
	0x8d, 0xb8, 0x91, 0x48, 0xf0, 0xe7, 0x0a, 0xbd,
	0xf9, 0x3f, 0xcd, 0xd9, 0xa0, 0x1e, 0x42, 0x4c,
	0xe7, 0xde, 0x25, 0x3d, 0xa3, 0xd7, 0x05, 0x80,
	0x8d, 0xf2, 0x82, 0xac, 0x44, 0x16, 0x51, 0x01
};

static const u8 enc_input008[] __initconst = {
	0xc3, 0x09, 0x94, 0x62, 0xe6, 0x46, 0x2e, 0x10,
	0xbe, 0x00, 0xe4, 0xfc, 0xf3, 0x40, 0xa3, 0xe2,
	0x0f, 0xc2, 0x8b, 0x28, 0xdc, 0xba, 0xb4, 0x3c,
	0xe4, 0x21, 0x58, 0x61, 0xcd, 0x8b, 0xcd, 0xfb,
	0xac, 0x94, 0xa1, 0x45, 0xf5, 0x1c, 0xe1, 0x12,
	0xe0, 0x3b, 0x67, 0x21, 0x54, 0x5e, 0x8c, 0xaa,
	0xcf, 0xdb, 0xb4, 0x51, 0xd4, 0x13, 0xda, 0xe6,
	0x83, 0x89, 0xb6, 0x92, 0xe9, 0x21, 0x76, 0xa4,
	0x93, 0x7d, 0x0e, 0xfd, 0x96, 0x36, 0x03, 0x91,
	0x43, 0x5c, 0x92, 0x49, 0x62, 0x61, 0x7b, 0xeb,
	0x43, 0x89, 0xb8, 0x12, 0x20, 0x43, 0xd4, 0x47,
	0x06, 0x84, 0xee, 0x47, 0xe9, 0x8a, 0x73, 0x15,
	0x0f, 0x72, 0xcf, 0xed, 0xce, 0x96, 0xb2, 0x7f,
	0x21, 0x45, 0x76, 0xeb, 0x26, 0x28, 0x83, 0x6a,
	0xad, 0xaa, 0xa6, 0x81, 0xd8, 0x55, 0xb1, 0xa3,
	0x85, 0xb3, 0x0c, 0xdf, 0xf1, 0x69, 0x2d, 0x97,
	0x05, 0x2a, 0xbc, 0x7c, 0x7b, 0x25, 0xf8, 0x80,
	0x9d, 0x39, 0x25, 0xf3, 0x62, 0xf0, 0x66, 0x5e,
	0xf4, 0xa0, 0xcf, 0xd8, 0xfd, 0x4f, 0xb1, 0x1f,
	0x60, 0x3a, 0x08, 0x47, 0xaf, 0xe1, 0xf6, 0x10,
	0x77, 0x09, 0xa7, 0x27, 0x8f, 0x9a, 0x97, 0x5a,
	0x26, 0xfa, 0xfe, 0x41, 0x32, 0x83, 0x10, 0xe0,
	0x1d, 0xbf, 0x64, 0x0d, 0xf4, 0x1c, 0x32, 0x35,
	0xe5, 0x1b, 0x36, 0xef, 0xd4, 0x4a, 0x93, 0x4d,
	0x00, 0x7c, 0xec, 0x02, 0x07, 0x8b, 0x5d, 0x7d,
	0x1b, 0x0e, 0xd1, 0xa6, 0xa5, 0x5d, 0x7d, 0x57,
	0x88, 0xa8, 0xcc, 0x81, 0xb4, 0x86, 0x4e, 0xb4,
	0x40, 0xe9, 0x1d, 0xc3, 0xb1, 0x24, 0x3e, 0x7f,
	0xcc, 0x8a, 0x24, 0x9b, 0xdf, 0x6d, 0xf0, 0x39,
	0x69, 0x3e, 0x4c, 0xc0, 0x96, 0xe4, 0x13, 0xda,
	0x90, 0xda, 0xf4, 0x95, 0x66, 0x8b, 0x17, 0x17,
	0xfe, 0x39, 0x43, 0x25, 0xaa, 0xda, 0xa0, 0x43,
	0x3c, 0xb1, 0x41, 0x02, 0xa3, 0xf0, 0xa7, 0x19,
	0x59, 0xbc, 0x1d, 0x7d, 0x6c, 0x6d, 0x91, 0x09,
	0x5c, 0xb7, 0x5b, 0x01, 0xd1, 0x6f, 0x17, 0x21,
	0x97, 0xbf, 0x89, 0x71, 0xa5, 0xb0, 0x6e, 0x07,
	0x45, 0xfd, 0x9d, 0xea, 0x07, 0xf6, 0x7a, 0x9f,
	0x10, 0x18, 0x22, 0x30, 0x73, 0xac, 0xd4, 0x6b,
	0x72, 0x44, 0xed, 0xd9, 0x19, 0x9b, 0x2d, 0x4a,
	0x41, 0xdd, 0xd1, 0x85, 0x5e, 0x37, 0x19, 0xed,
	0xd2, 0x15, 0x8f, 0x5e, 0x91, 0xdb, 0x33, 0xf2,
	0xe4, 0xdb, 0xff, 0x98, 0xfb, 0xa3, 0xb5, 0xca,
	0x21, 0x69, 0x08, 0xe7, 0x8a, 0xdf, 0x90, 0xff,
	0x3e, 0xe9, 0x20, 0x86, 0x3c, 0xe9, 0xfc, 0x0b,
	0xfe, 0x5c, 0x61, 0xaa, 0x13, 0x92, 0x7f, 0x7b,
	0xec, 0xe0, 0x6d, 0xa8, 0x23, 0x22, 0xf6, 0x6b,
	0x77, 0xc4, 0xfe, 0x40, 0x07, 0x3b, 0xb6, 0xf6,
	0x8e, 0x5f, 0xd4, 0xb9, 0xb7, 0x0f, 0x21, 0x04,
	0xef, 0x83, 0x63, 0x91, 0x69, 0x40, 0xa3, 0x48,
	0x5c, 0xd2, 0x60, 0xf9, 0x4f, 0x6c, 0x47, 0x8b,
	0x3b, 0xb1, 0x9f, 0x8e, 0xee, 0x16, 0x8a, 0x13,
	0xfc, 0x46, 0x17, 0xc3, 0xc3, 0x32, 0x56, 0xf8,
	0x3c, 0x85, 0x3a, 0xb6, 0x3e, 0xaa, 0x89, 0x4f,
	0xb3, 0xdf, 0x38, 0xfd, 0xf1, 0xe4, 0x3a, 0xc0,
	0xe6, 0x58, 0xb5, 0x8f, 0xc5, 0x29, 0xa2, 0x92,
	0x4a, 0xb6, 0xa0, 0x34, 0x7f, 0xab, 0xb5, 0x8a,
	0x90, 0xa1, 0xdb, 0x4d, 0xca, 0xb6, 0x2c, 0x41,
	0x3c, 0xf7, 0x2b, 0x21, 0xc3, 0xfd, 0xf4, 0x17,
	0x5c, 0xb5, 0x33, 0x17, 0x68, 0x2b, 0x08, 0x30,
	0xf3, 0xf7, 0x30, 0x3c, 0x96, 0xe6, 0x6a, 0x20,
	0x97, 0xe7, 0x4d, 0x10, 0x5f, 0x47, 0x5f, 0x49,
	0x96, 0x09, 0xf0, 0x27, 0x91, 0xc8, 0xf8, 0x5a,
	0x2e, 0x79, 0xb5, 0xe2, 0xb8, 0xe8, 0xb9, 0x7b,
	0xd5, 0x10, 0xcb, 0xff, 0x5d, 0x14, 0x73, 0xf3
};
static const u8 enc_output008[] __initconst = {
	0x14, 0xf6, 0x41, 0x37, 0xa6, 0xd4, 0x27, 0xcd,
	0xdb, 0x06, 0x3e, 0x9a, 0x4e, 0xab, 0xd5, 0xb1,
	0x1e, 0x6b, 0xd2, 0xbc, 0x11, 0xf4, 0x28, 0x93,
	0x63, 0x54, 0xef, 0xbb, 0x5e, 0x1d, 0x3a, 0x1d,
	0x37, 0x3c, 0x0a, 0x6c, 0x1e, 0xc2, 0xd1, 0x2c,
	0xb5, 0xa3, 0xb5, 0x7b, 0xb8, 0x8f, 0x25, 0xa6,
	0x1b, 0x61, 0x1c, 0xec, 0x28, 0x58, 0x26, 0xa4,
	0xa8, 0x33, 0x28, 0x25, 0x5c, 0x45, 0x05, 0xe5,
	0x6c, 0x99, 0xe5, 0x45, 0xc4, 0xa2, 0x03, 0x84,
	0x03, 0x73, 0x1e, 0x8c, 0x49, 0xac, 0x20, 0xdd,
	0x8d, 0xb3, 0xc4, 0xf5, 0xe7, 0x4f, 0xf1, 0xed,
	0xa1, 0x98, 0xde, 0xa4, 0x96, 0xdd, 0x2f, 0xab,
	0xab, 0x97, 0xcf, 0x3e, 0xd2, 0x9e, 0xb8, 0x13,
	0x07, 0x28, 0x29, 0x19, 0xaf, 0xfd, 0xf2, 0x49,
	0x43, 0xea, 0x49, 0x26, 0x91, 0xc1, 0x07, 0xd6,
	0xbb, 0x81, 0x75, 0x35, 0x0d, 0x24, 0x7f, 0xc8,
	0xda, 0xd4, 0xb7, 0xeb, 0xe8, 0x5c, 0x09, 0xa2,
	0x2f, 0xdc, 0x28, 0x7d, 0x3a, 0x03, 0xfa, 0x94,
	0xb5, 0x1d, 0x17, 0x99, 0x36, 0xc3, 0x1c, 0x18,
	0x34, 0xe3, 0x9f, 0xf5, 0x55, 0x7c, 0xb0, 0x60,
	0x9d, 0xff, 0xac, 0xd4, 0x61, 0xf2, 0xad, 0xf8,
	0xce, 0xc7, 0xbe, 0x5c, 0xd2, 0x95, 0xa8, 0x4b,
	0x77, 0x13, 0x19, 0x59, 0x26, 0xc9, 0xb7, 0x8f,
	0x6a, 0xcb, 0x2d, 0x37, 0x91, 0xea, 0x92, 0x9c,
	0x94, 0x5b, 0xda, 0x0b, 0xce, 0xfe, 0x30, 0x20,
	0xf8, 0x51, 0xad, 0xf2, 0xbe, 0xe7, 0xc7, 0xff,
	0xb3, 0x33, 0x91, 0x6a, 0xc9, 0x1a, 0x41, 0xc9,
	0x0f, 0xf3, 0x10, 0x0e, 0xfd, 0x53, 0xff, 0x6c,
	0x16, 0x52, 0xd9, 0xf3, 0xf7, 0x98, 0x2e, 0xc9,
	0x07, 0x31, 0x2c, 0x0c, 0x72, 0xd7, 0xc5, 0xc6,
	0x08, 0x2a, 0x7b, 0xda, 0xbd, 0x7e, 0x02, 0xea,
	0x1a, 0xbb, 0xf2, 0x04, 0x27, 0x61, 0x28, 0x8e,
	0xf5, 0x04, 0x03, 0x1f, 0x4c, 0x07, 0x55, 0x82,
	0xec, 0x1e, 0xd7, 0x8b, 0x2f, 0x65, 0x56, 0xd1,
	0xd9, 0x1e, 0x3c, 0xe9, 0x1f, 0x5e, 0x98, 0x70,
	0x38, 0x4a, 0x8c, 0x49, 0xc5, 0x43, 0xa0, 0xa1,
	0x8b, 0x74, 0x9d, 0x4c, 0x62, 0x0d, 0x10, 0x0c,
	0xf4, 0x6c, 0x8f, 0xe0, 0xaa, 0x9a, 0x8d, 0xb7,
	0xe0, 0xbe, 0x4c, 0x87, 0xf1, 0x98, 0x2f, 0xcc,
	0xed, 0xc0, 0x52, 0x29, 0xdc, 0x83, 0xf8, 0xfc,
	0x2c, 0x0e, 0xa8, 0x51, 0x4d, 0x80, 0x0d, 0xa3,
	0xfe, 0xd8, 0x37, 0xe7, 0x41, 0x24, 0xfc, 0xfb,
	0x75, 0xe3, 0x71, 0x7b, 0x57, 0x45, 0xf5, 0x97,
	0x73, 0x65, 0x63, 0x14, 0x74, 0xb8, 0x82, 0x9f,
	0xf8, 0x60, 0x2f, 0x8a, 0xf2, 0x4e, 0xf1, 0x39,
	0xda, 0x33, 0x91, 0xf8, 0x36, 0xe0, 0x8d, 0x3f,
	0x1f, 0x3b, 0x56, 0xdc, 0xa0, 0x8f, 0x3c, 0x9d,
	0x71, 0x52, 0xa7, 0xb8, 0xc0, 0xa5, 0xc6, 0xa2,
	0x73, 0xda, 0xf4, 0x4b, 0x74, 0x5b, 0x00, 0x3d,
	0x99, 0xd7, 0x96, 0xba, 0xe6, 0xe1, 0xa6, 0x96,
	0x38, 0xad, 0xb3, 0xc0, 0xd2, 0xba, 0x91, 0x6b,
	0xf9, 0x19, 0xdd, 0x3b, 0xbe, 0xbe, 0x9c, 0x20,
	0x50, 0xba, 0xa1, 0xd0, 0xce, 0x11, 0xbd, 0x95,
	0xd8, 0xd1, 0xdd, 0x33, 0x85, 0x74, 0xdc, 0xdb,
	0x66, 0x76, 0x44, 0xdc, 0x03, 0x74, 0x48, 0x35,
	0x98, 0xb1, 0x18, 0x47, 0x94, 0x7d, 0xff, 0x62,
	0xe4, 0x58, 0x78, 0xab, 0xed, 0x95, 0x36, 0xd9,
	0x84, 0x91, 0x82, 0x64, 0x41, 0xbb, 0x58, 0xe6,
	0x1c, 0x20, 0x6d, 0x15, 0x6b, 0x13, 0x96, 0xe8,
	0x35, 0x7f, 0xdc, 0x40, 0x2c, 0xe9, 0xbc, 0x8a,
	0x4f, 0x92, 0xec, 0x06, 0x2d, 0x50, 0xdf, 0x93,
	0x5d, 0x65, 0x5a, 0xa8, 0xfc, 0x20, 0x50, 0x14,
	0xa9, 0x8a, 0x7e, 0x1d, 0x08, 0x1f, 0xe2, 0x99,
	0xd0, 0xbe, 0xfb, 0x3a, 0x21, 0x9d, 0xad, 0x86,
	0x54, 0xfd, 0x0d, 0x98, 0x1c, 0x5a, 0x6f, 0x1f,
	0x9a, 0x40, 0xcd, 0xa2, 0xff, 0x6a, 0xf1, 0x54
};
static const u8 enc_assoc008[] __initconst = { };
static const u8 enc_nonce008[] __initconst = {
	0x0e, 0x0d, 0x57, 0xbb, 0x7b, 0x40, 0x54, 0x02
};
static const u8 enc_key008[] __initconst = {
	0xf2, 0xaa, 0x4f, 0x99, 0xfd, 0x3e, 0xa8, 0x53,
	0xc1, 0x44, 0xe9, 0x81, 0x18, 0xdc, 0xf5, 0xf0,
	0x3e, 0x44, 0x15, 0x59, 0xe0, 0xc5, 0x44, 0x86,
	0xc3, 0x91, 0xa8, 0x75, 0xc0, 0x12, 0x46, 0xba
};

static const u8 enc_input009[] __initconst = {
	0xe6, 0xc3, 0xdb, 0x63, 0x55, 0x15, 0xe3, 0x5b,
	0xb7, 0x4b, 0x27, 0x8b, 0x5a, 0xdd, 0xc2, 0xe8,
	0x3a, 0x6b, 0xd7, 0x81, 0x96, 0x35, 0x97, 0xca,
	0xd7, 0x68, 0xe8, 0xef, 0xce, 0xab, 0xda, 0x09,
	0x6e, 0xd6, 0x8e, 0xcb, 0x55, 0xb5, 0xe1, 0xe5,
	0x57, 0xfd, 0xc4, 0xe3, 0xe0, 0x18, 0x4f, 0x85,
	0xf5, 0x3f, 0x7e, 0x4b, 0x88, 0xc9, 0x52, 0x44,
	0x0f, 0xea, 0xaf, 0x1f, 0x71, 0x48, 0x9f, 0x97,
	0x6d, 0xb9, 0x6f, 0x00, 0xa6, 0xde, 0x2b, 0x77,
	0x8b, 0x15, 0xad, 0x10, 0xa0, 0x2b, 0x7b, 0x41,
	0x90, 0x03, 0x2d, 0x69, 0xae, 0xcc, 0x77, 0x7c,
	0xa5, 0x9d, 0x29, 0x22, 0xc2, 0xea, 0xb4, 0x00,
	0x1a, 0xd2, 0x7a, 0x98, 0x8a, 0xf9, 0xf7, 0x82,
	0xb0, 0xab, 0xd8, 0xa6, 0x94, 0x8d, 0x58, 0x2f,
	0x01, 0x9e, 0x00, 0x20, 0xfc, 0x49, 0xdc, 0x0e,
	0x03, 0xe8, 0x45, 0x10, 0xd6, 0xa8, 0xda, 0x55,
	0x10, 0x9a, 0xdf, 0x67, 0x22, 0x8b, 0x43, 0xab,
	0x00, 0xbb, 0x02, 0xc8, 0xdd, 0x7b, 0x97, 0x17,
	0xd7, 0x1d, 0x9e, 0x02, 0x5e, 0x48, 0xde, 0x8e,
	0xcf, 0x99, 0x07, 0x95, 0x92, 0x3c, 0x5f, 0x9f,
	0xc5, 0x8a, 0xc0, 0x23, 0xaa, 0xd5, 0x8c, 0x82,
	0x6e, 0x16, 0x92, 0xb1, 0x12, 0x17, 0x07, 0xc3,
	0xfb, 0x36, 0xf5, 0x6c, 0x35, 0xd6, 0x06, 0x1f,
	0x9f, 0xa7, 0x94, 0xa2, 0x38, 0x63, 0x9c, 0xb0,
	0x71, 0xb3, 0xa5, 0xd2, 0xd8, 0xba, 0x9f, 0x08,
	0x01, 0xb3, 0xff, 0x04, 0x97, 0x73, 0x45, 0x1b,
	0xd5, 0xa9, 0x9c, 0x80, 0xaf, 0x04, 0x9a, 0x85,
	0xdb, 0x32, 0x5b, 0x5d, 0x1a, 0xc1, 0x36, 0x28,
	0x10, 0x79, 0xf1, 0x3c, 0xbf, 0x1a, 0x41, 0x5c,
	0x4e, 0xdf, 0xb2, 0x7c, 0x79, 0x3b, 0x7a, 0x62,
	0x3d, 0x4b, 0xc9, 0x9b, 0x2a, 0x2e, 0x7c, 0xa2,
	0xb1, 0x11, 0x98, 0xa7, 0x34, 0x1a, 0x00, 0xf3,
	0xd1, 0xbc, 0x18, 0x22, 0xba, 0x02, 0x56, 0x62,
	0x31, 0x10, 0x11, 0x6d, 0xe0, 0x54, 0x9d, 0x40,
	0x1f, 0x26, 0x80, 0x41, 0xca, 0x3f, 0x68, 0x0f,
	0x32, 0x1d, 0x0a, 0x8e, 0x79, 0xd8, 0xa4, 0x1b,
	0x29, 0x1c, 0x90, 0x8e, 0xc5, 0xe3, 0xb4, 0x91,
	0x37, 0x9a, 0x97, 0x86, 0x99, 0xd5, 0x09, 0xc5,
	0xbb, 0xa3, 0x3f, 0x21, 0x29, 0x82, 0x14, 0x5c,
	0xab, 0x25, 0xfb, 0xf2, 0x4f, 0x58, 0x26, 0xd4,
	0x83, 0xaa, 0x66, 0x89, 0x67, 0x7e, 0xc0, 0x49,
	0xe1, 0x11, 0x10, 0x7f, 0x7a, 0xda, 0x29, 0x04,
	0xff, 0xf0, 0xcb, 0x09, 0x7c, 0x9d, 0xfa, 0x03,
	0x6f, 0x81, 0x09, 0x31, 0x60, 0xfb, 0x08, 0xfa,
	0x74, 0xd3, 0x64, 0x44, 0x7c, 0x55, 0x85, 0xec,
	0x9c, 0x6e, 0x25, 0xb7, 0x6c, 0xc5, 0x37, 0xb6,
	0x83, 0x87, 0x72, 0x95, 0x8b, 0x9d, 0xe1, 0x69,
	0x5c, 0x31, 0x95, 0x42, 0xa6, 0x2c, 0xd1, 0x36,
	0x47, 0x1f, 0xec, 0x54, 0xab, 0xa2, 0x1c, 0xd8,
	0x00, 0xcc, 0xbc, 0x0d, 0x65, 0xe2, 0x67, 0xbf,
	0xbc, 0xea, 0xee, 0x9e, 0xe4, 0x36, 0x95, 0xbe,
	0x73, 0xd9, 0xa6, 0xd9, 0x0f, 0xa0, 0xcc, 0x82,
	0x76, 0x26, 0xad, 0x5b, 0x58, 0x6c, 0x4e, 0xab,
	0x29, 0x64, 0xd3, 0xd9, 0xa9, 0x08, 0x8c, 0x1d,
	0xa1, 0x4f, 0x80, 0xd8, 0x3f, 0x94, 0xfb, 0xd3,
	0x7b, 0xfc, 0xd1, 0x2b, 0xc3, 0x21, 0xeb, 0xe5,
	0x1c, 0x84, 0x23, 0x7f, 0x4b, 0xfa, 0xdb, 0x34,
	0x18, 0xa2, 0xc2, 0xe5, 0x13, 0xfe, 0x6c, 0x49,
	0x81, 0xd2, 0x73, 0xe7, 0xe2, 0xd7, 0xe4, 0x4f,
	0x4b, 0x08, 0x6e, 0xb1, 0x12, 0x22, 0x10, 0x9d,
	0xac, 0x51, 0x1e, 0x17, 0xd9, 0x8a, 0x0b, 0x42,
	0x88, 0x16, 0x81, 0x37, 0x7c, 0x6a, 0xf7, 0xef,
	0x2d, 0xe3, 0xd9, 0xf8, 0x5f, 0xe0, 0x53, 0x27,
	0x74, 0xb9, 0xe2, 0xd6, 0x1c, 0x80, 0x2c, 0x52,
	0x65
};
static const u8 enc_output009[] __initconst = {
	0xfd, 0x81, 0x8d, 0xd0, 0x3d, 0xb4, 0xd5, 0xdf,
	0xd3, 0x42, 0x47, 0x5a, 0x6d, 0x19, 0x27, 0x66,
	0x4b, 0x2e, 0x0c, 0x27, 0x9c, 0x96, 0x4c, 0x72,
	0x02, 0xa3, 0x65, 0xc3, 0xb3, 0x6f, 0x2e, 0xbd,
	0x63, 0x8a, 0x4a, 0x5d, 0x29, 0xa2, 0xd0, 0x28,
	0x48, 0xc5, 0x3d, 0x98, 0xa3, 0xbc, 0xe0, 0xbe,
	0x3b, 0x3f, 0xe6, 0x8a, 0xa4, 0x7f, 0x53, 0x06,
	0xfa, 0x7f, 0x27, 0x76, 0x72, 0x31, 0xa1, 0xf5,
	0xd6, 0x0c, 0x52, 0x47, 0xba, 0xcd, 0x4f, 0xd7,
	0xeb, 0x05, 0x48, 0x0d, 0x7c, 0x35, 0x4a, 0x09,
	0xc9, 0x76, 0x71, 0x02, 0xa3, 0xfb, 0xb7, 0x1a,
	0x65, 0xb7, 0xed, 0x98, 0xc6, 0x30, 0x8a, 0x00,
	0xae, 0xa1, 0x31, 0xe5, 0xb5, 0x9e, 0x6d, 0x62,
	0xda, 0xda, 0x07, 0x0f, 0x38, 0x38, 0xd3, 0xcb,
	0xc1, 0xb0, 0xad, 0xec, 0x72, 0xec, 0xb1, 0xa2,
	0x7b, 0x59, 0xf3, 0x3d, 0x2b, 0xef, 0xcd, 0x28,
	0x5b, 0x83, 0xcc, 0x18, 0x91, 0x88, 0xb0, 0x2e,
	0xf9, 0x29, 0x31, 0x18, 0xf9, 0x4e, 0xe9, 0x0a,
	0x91, 0x92, 0x9f, 0xae, 0x2d, 0xad, 0xf4, 0xe6,
	0x1a, 0xe2, 0xa4, 0xee, 0x47, 0x15, 0xbf, 0x83,
	0x6e, 0xd7, 0x72, 0x12, 0x3b, 0x2d, 0x24, 0xe9,
	0xb2, 0x55, 0xcb, 0x3c, 0x10, 0xf0, 0x24, 0x8a,
	0x4a, 0x02, 0xea, 0x90, 0x25, 0xf0, 0xb4, 0x79,
	0x3a, 0xef, 0x6e, 0xf5, 0x52, 0xdf, 0xb0, 0x0a,
	0xcd, 0x24, 0x1c, 0xd3, 0x2e, 0x22, 0x74, 0xea,
	0x21, 0x6f, 0xe9, 0xbd, 0xc8, 0x3e, 0x36, 0x5b,
	0x19, 0xf1, 0xca, 0x99, 0x0a, 0xb4, 0xa7, 0x52,
	0x1a, 0x4e, 0xf2, 0xad, 0x8d, 0x56, 0x85, 0xbb,
	0x64, 0x89, 0xba, 0x26, 0xf9, 0xc7, 0xe1, 0x89,
	0x19, 0x22, 0x77, 0xc3, 0xa8, 0xfc, 0xff, 0xad,
	0xfe, 0xb9, 0x48, 0xae, 0x12, 0x30, 0x9f, 0x19,
	0xfb, 0x1b, 0xef, 0x14, 0x87, 0x8a, 0x78, 0x71,
	0xf3, 0xf4, 0xb7, 0x00, 0x9c, 0x1d, 0xb5, 0x3d,
	0x49, 0x00, 0x0c, 0x06, 0xd4, 0x50, 0xf9, 0x54,
	0x45, 0xb2, 0x5b, 0x43, 0xdb, 0x6d, 0xcf, 0x1a,
	0xe9, 0x7a, 0x7a, 0xcf, 0xfc, 0x8a, 0x4e, 0x4d,
	0x0b, 0x07, 0x63, 0x28, 0xd8, 0xe7, 0x08, 0x95,
	0xdf, 0xa6, 0x72, 0x93, 0x2e, 0xbb, 0xa0, 0x42,
	0x89, 0x16, 0xf1, 0xd9, 0x0c, 0xf9, 0xa1, 0x16,
	0xfd, 0xd9, 0x03, 0xb4, 0x3b, 0x8a, 0xf5, 0xf6,
	0xe7, 0x6b, 0x2e, 0x8e, 0x4c, 0x3d, 0xe2, 0xaf,
	0x08, 0x45, 0x03, 0xff, 0x09, 0xb6, 0xeb, 0x2d,
	0xc6, 0x1b, 0x88, 0x94, 0xac, 0x3e, 0xf1, 0x9f,
	0x0e, 0x0e, 0x2b, 0xd5, 0x00, 0x4d, 0x3f, 0x3b,
	0x53, 0xae, 0xaf, 0x1c, 0x33, 0x5f, 0x55, 0x6e,
	0x8d, 0xaf, 0x05, 0x7a, 0x10, 0x34, 0xc9, 0xf4,
	0x66, 0xcb, 0x62, 0x12, 0xa6, 0xee, 0xe8, 0x1c,
	0x5d, 0x12, 0x86, 0xdb, 0x6f, 0x1c, 0x33, 0xc4,
	0x1c, 0xda, 0x82, 0x2d, 0x3b, 0x59, 0xfe, 0xb1,
	0xa4, 0x59, 0x41, 0x86, 0xd0, 0xef, 0xae, 0xfb,
	0xda, 0x6d, 0x11, 0xb8, 0xca, 0xe9, 0x6e, 0xff,
	0xf7, 0xa9, 0xd9, 0x70, 0x30, 0xfc, 0x53, 0xe2,
	0xd7, 0xa2, 0x4e, 0xc7, 0x91, 0xd9, 0x07, 0x06,
	0xaa, 0xdd, 0xb0, 0x59, 0x28, 0x1d, 0x00, 0x66,
	0xc5, 0x54, 0xc2, 0xfc, 0x06, 0xda, 0x05, 0x90,
	0x52, 0x1d, 0x37, 0x66, 0xee, 0xf0, 0xb2, 0x55,
	0x8a, 0x5d, 0xd2, 0x38, 0x86, 0x94, 0x9b, 0xfc,
	0x10, 0x4c, 0xa1, 0xb9, 0x64, 0x3e, 0x44, 0xb8,
	0x5f, 0xb0, 0x0c, 0xec, 0xe0, 0xc9, 0xe5, 0x62,
	0x75, 0x3f, 0x09, 0xd5, 0xf5, 0xd9, 0x26, 0xba,
	0x9e, 0xd2, 0xf4, 0xb9, 0x48, 0x0a, 0xbc, 0xa2,
	0xd6, 0x7c, 0x36, 0x11, 0x7d, 0x26, 0x81, 0x89,
	0xcf, 0xa4, 0xad, 0x73, 0x0e, 0xee, 0xcc, 0x06,
	0xa9, 0xdb, 0xb1, 0xfd, 0xfb, 0x09, 0x7f, 0x90,
	0x42, 0x37, 0x2f, 0xe1, 0x9c, 0x0f, 0x6f, 0xcf,
	0x43, 0xb5, 0xd9, 0x90, 0xe1, 0x85, 0xf5, 0xa8,
	0xae
};
static const u8 enc_assoc009[] __initconst = {
	0x5a, 0x27, 0xff, 0xeb, 0xdf, 0x84, 0xb2, 0x9e,
	0xef
};
static const u8 enc_nonce009[] __initconst = {
	0xef, 0x2d, 0x63, 0xee, 0x6b, 0x80, 0x8b, 0x78
};
static const u8 enc_key009[] __initconst = {
	0xea, 0xbc, 0x56, 0x99, 0xe3, 0x50, 0xff, 0xc5,
	0xcc, 0x1a, 0xd7, 0xc1, 0x57, 0x72, 0xea, 0x86,
	0x5b, 0x89, 0x88, 0x61, 0x3d, 0x2f, 0x9b, 0xb2,
	0xe7, 0x9c, 0xec, 0x74, 0x6e, 0x3e, 0xf4, 0x3b
};

static const u8 enc_input010[] __initconst = {
	0x42, 0x93, 0xe4, 0xeb, 0x97, 0xb0, 0x57, 0xbf,
	0x1a, 0x8b, 0x1f, 0xe4, 0x5f, 0x36, 0x20, 0x3c,
	0xef, 0x0a, 0xa9, 0x48, 0x5f, 0x5f, 0x37, 0x22,
	0x3a, 0xde, 0xe3, 0xae, 0xbe, 0xad, 0x07, 0xcc,
	0xb1, 0xf6, 0xf5, 0xf9, 0x56, 0xdd, 0xe7, 0x16,
	0x1e, 0x7f, 0xdf, 0x7a, 0x9e, 0x75, 0xb7, 0xc7,
	0xbe, 0xbe, 0x8a, 0x36, 0x04, 0xc0, 0x10, 0xf4,
	0x95, 0x20, 0x03, 0xec, 0xdc, 0x05, 0xa1, 0x7d,
	0xc4, 0xa9, 0x2c, 0x82, 0xd0, 0xbc, 0x8b, 0xc5,
	0xc7, 0x45, 0x50, 0xf6, 0xa2, 0x1a, 0xb5, 0x46,
	0x3b, 0x73, 0x02, 0xa6, 0x83, 0x4b, 0x73, 0x82,
	0x58, 0x5e, 0x3b, 0x65, 0x2f, 0x0e, 0xfd, 0x2b,
	0x59, 0x16, 0xce, 0xa1, 0x60, 0x9c, 0xe8, 0x3a,
	0x99, 0xed, 0x8d, 0x5a, 0xcf, 0xf6, 0x83, 0xaf,
	0xba, 0xd7, 0x73, 0x73, 0x40, 0x97, 0x3d, 0xca,
	0xef, 0x07, 0x57, 0xe6, 0xd9, 0x70, 0x0e, 0x95,
	0xae, 0xa6, 0x8d, 0x04, 0xcc, 0xee, 0xf7, 0x09,
	0x31, 0x77, 0x12, 0xa3, 0x23, 0x97, 0x62, 0xb3,
	0x7b, 0x32, 0xfb, 0x80, 0x14, 0x48, 0x81, 0xc3,
	0xe5, 0xea, 0x91, 0x39, 0x52, 0x81, 0xa2, 0x4f,
	0xe4, 0xb3, 0x09, 0xff, 0xde, 0x5e, 0xe9, 0x58,
	0x84, 0x6e, 0xf9, 0x3d, 0xdf, 0x25, 0xea, 0xad,
	0xae, 0xe6, 0x9a, 0xd1, 0x89, 0x55, 0xd3, 0xde,
	0x6c, 0x52, 0xdb, 0x70, 0xfe, 0x37, 0xce, 0x44,
	0x0a, 0xa8, 0x25, 0x5f, 0x92, 0xc1, 0x33, 0x4a,
	0x4f, 0x9b, 0x62, 0x35, 0xff, 0xce, 0xc0, 0xa9,
	0x60, 0xce, 0x52, 0x00, 0x97, 0x51, 0x35, 0x26,
	0x2e, 0xb9, 0x36, 0xa9, 0x87, 0x6e, 0x1e, 0xcc,
	0x91, 0x78, 0x53, 0x98, 0x86, 0x5b, 0x9c, 0x74,
	0x7d, 0x88, 0x33, 0xe1, 0xdf, 0x37, 0x69, 0x2b,
	0xbb, 0xf1, 0x4d, 0xf4, 0xd1, 0xf1, 0x39, 0x93,
	0x17, 0x51, 0x19, 0xe3, 0x19, 0x1e, 0x76, 0x37,
	0x25, 0xfb, 0x09, 0x27, 0x6a, 0xab, 0x67, 0x6f,
	0x14, 0x12, 0x64, 0xe7, 0xc4, 0x07, 0xdf, 0x4d,
	0x17, 0xbb, 0x6d, 0xe0, 0xe9, 0xb9, 0xab, 0xca,
	0x10, 0x68, 0xaf, 0x7e, 0xb7, 0x33, 0x54, 0x73,
	0x07, 0x6e, 0xf7, 0x81, 0x97, 0x9c, 0x05, 0x6f,
	0x84, 0x5f, 0xd2, 0x42, 0xfb, 0x38, 0xcf, 0xd1,
	0x2f, 0x14, 0x30, 0x88, 0x98, 0x4d, 0x5a, 0xa9,
	0x76, 0xd5, 0x4f, 0x3e, 0x70, 0x6c, 0x85, 0x76,
	0xd7, 0x01, 0xa0, 0x1a, 0xc8, 0x4e, 0xaa, 0xac,
	0x78, 0xfe, 0x46, 0xde, 0x6a, 0x05, 0x46, 0xa7,
	0x43, 0x0c, 0xb9, 0xde, 0xb9, 0x68, 0xfb, 0xce,
	0x42, 0x99, 0x07, 0x4d, 0x0b, 0x3b, 0x5a, 0x30,
	0x35, 0xa8, 0xf9, 0x3a, 0x73, 0xef, 0x0f, 0xdb,
	0x1e, 0x16, 0x42, 0xc4, 0xba, 0xae, 0x58, 0xaa,
	0xf8, 0xe5, 0x75, 0x2f, 0x1b, 0x15, 0x5c, 0xfd,
	0x0a, 0x97, 0xd0, 0xe4, 0x37, 0x83, 0x61, 0x5f,
	0x43, 0xa6, 0xc7, 0x3f, 0x38, 0x59, 0xe6, 0xeb,
	0xa3, 0x90, 0xc3, 0xaa, 0xaa, 0x5a, 0xd3, 0x34,
	0xd4, 0x17, 0xc8, 0x65, 0x3e, 0x57, 0xbc, 0x5e,
	0xdd, 0x9e, 0xb7, 0xf0, 0x2e, 0x5b, 0xb2, 0x1f,
	0x8a, 0x08, 0x0d, 0x45, 0x91, 0x0b, 0x29, 0x53,
	0x4f, 0x4c, 0x5a, 0x73, 0x56, 0xfe, 0xaf, 0x41,
	0x01, 0x39, 0x0a, 0x24, 0x3c, 0x7e, 0xbe, 0x4e,
	0x53, 0xf3, 0xeb, 0x06, 0x66, 0x51, 0x28, 0x1d,
	0xbd, 0x41, 0x0a, 0x01, 0xab, 0x16, 0x47, 0x27,
	0x47, 0x47, 0xf7, 0xcb, 0x46, 0x0a, 0x70, 0x9e,
	0x01, 0x9c, 0x09, 0xe1, 0x2a, 0x00, 0x1a, 0xd8,
	0xd4, 0x79, 0x9d, 0x80, 0x15, 0x8e, 0x53, 0x2a,
	0x65, 0x83, 0x78, 0x3e, 0x03, 0x00, 0x07, 0x12,
	0x1f, 0x33, 0x3e, 0x7b, 0x13, 0x37, 0xf1, 0xc3,
	0xef, 0xb7, 0xc1, 0x20, 0x3c, 0x3e, 0x67, 0x66,
	0x5d, 0x88, 0xa7, 0x7d, 0x33, 0x50, 0x77, 0xb0,
	0x28, 0x8e, 0xe7, 0x2c, 0x2e, 0x7a, 0xf4, 0x3c,
	0x8d, 0x74, 0x83, 0xaf, 0x8e, 0x87, 0x0f, 0xe4,
	0x50, 0xff, 0x84, 0x5c, 0x47, 0x0c, 0x6a, 0x49,
	0xbf, 0x42, 0x86, 0x77, 0x15, 0x48, 0xa5, 0x90,
	0x5d, 0x93, 0xd6, 0x2a, 0x11, 0xd5, 0xd5, 0x11,
	0xaa, 0xce, 0xe7, 0x6f, 0xa5, 0xb0, 0x09, 0x2c,
	0x8d, 0xd3, 0x92, 0xf0, 0x5a, 0x2a, 0xda, 0x5b,
	0x1e, 0xd5, 0x9a, 0xc4, 0xc4, 0xf3, 0x49, 0x74,
	0x41, 0xca, 0xe8, 0xc1, 0xf8, 0x44, 0xd6, 0x3c,
	0xae, 0x6c, 0x1d, 0x9a, 0x30, 0x04, 0x4d, 0x27,
	0x0e, 0xb1, 0x5f, 0x59, 0xa2, 0x24, 0xe8, 0xe1,
	0x98, 0xc5, 0x6a, 0x4c, 0xfe, 0x41, 0xd2, 0x27,
	0x42, 0x52, 0xe1, 0xe9, 0x7d, 0x62, 0xe4, 0x88,
	0x0f, 0xad, 0xb2, 0x70, 0xcb, 0x9d, 0x4c, 0x27,
	0x2e, 0x76, 0x1e, 0x1a, 0x63, 0x65, 0xf5, 0x3b,
	0xf8, 0x57, 0x69, 0xeb, 0x5b, 0x38, 0x26, 0x39,
	0x33, 0x25, 0x45, 0x3e, 0x91, 0xb8, 0xd8, 0xc7,
	0xd5, 0x42, 0xc0, 0x22, 0x31, 0x74, 0xf4, 0xbc,
	0x0c, 0x23, 0xf1, 0xca, 0xc1, 0x8d, 0xd7, 0xbe,
	0xc9, 0x62, 0xe4, 0x08, 0x1a, 0xcf, 0x36, 0xd5,
	0xfe, 0x55, 0x21, 0x59, 0x91, 0x87, 0x87, 0xdf,
	0x06, 0xdb, 0xdf, 0x96, 0x45, 0x58, 0xda, 0x05,
	0xcd, 0x50, 0x4d, 0xd2, 0x7d, 0x05, 0x18, 0x73,
	0x6a, 0x8d, 0x11, 0x85, 0xa6, 0x88, 0xe8, 0xda,
	0xe6, 0x30, 0x33, 0xa4, 0x89, 0x31, 0x75, 0xbe,
	0x69, 0x43, 0x84, 0x43, 0x50, 0x87, 0xdd, 0x71,
	0x36, 0x83, 0xc3, 0x78, 0x74, 0x24, 0x0a, 0xed,
	0x7b, 0xdb, 0xa4, 0x24, 0x0b, 0xb9, 0x7e, 0x5d,
	0xff, 0xde, 0xb1, 0xef, 0x61, 0x5a, 0x45, 0x33,
	0xf6, 0x17, 0x07, 0x08, 0x98, 0x83, 0x92, 0x0f,
	0x23, 0x6d, 0xe6, 0xaa, 0x17, 0x54, 0xad, 0x6a,
	0xc8, 0xdb, 0x26, 0xbe, 0xb8, 0xb6, 0x08, 0xfa,
	0x68, 0xf1, 0xd7, 0x79, 0x6f, 0x18, 0xb4, 0x9e,
	0x2d, 0x3f, 0x1b, 0x64, 0xaf, 0x8d, 0x06, 0x0e,
	0x49, 0x28, 0xe0, 0x5d, 0x45, 0x68, 0x13, 0x87,
	0xfa, 0xde, 0x40, 0x7b, 0xd2, 0xc3, 0x94, 0xd5,
	0xe1, 0xd9, 0xc2, 0xaf, 0x55, 0x89, 0xeb, 0xb4,
	0x12, 0x59, 0xa8, 0xd4, 0xc5, 0x29, 0x66, 0x38,
	0xe6, 0xac, 0x22, 0x22, 0xd9, 0x64, 0x9b, 0x34,
	0x0a, 0x32, 0x9f, 0xc2, 0xbf, 0x17, 0x6c, 0x3f,
	0x71, 0x7a, 0x38, 0x6b, 0x98, 0xfb, 0x49, 0x36,
	0x89, 0xc9, 0xe2, 0xd6, 0xc7, 0x5d, 0xd0, 0x69,
	0x5f, 0x23, 0x35, 0xc9, 0x30, 0xe2, 0xfd, 0x44,
	0x58, 0x39, 0xd7, 0x97, 0xfb, 0x5c, 0x00, 0xd5,
	0x4f, 0x7a, 0x1a, 0x95, 0x8b, 0x62, 0x4b, 0xce,
	0xe5, 0x91, 0x21, 0x7b, 0x30, 0x00, 0xd6, 0xdd,
	0x6d, 0x02, 0x86, 0x49, 0x0f, 0x3c, 0x1a, 0x27,
	0x3c, 0xd3, 0x0e, 0x71, 0xf2, 0xff, 0xf5, 0x2f,
	0x87, 0xac, 0x67, 0x59, 0x81, 0xa3, 0xf7, 0xf8,
	0xd6, 0x11, 0x0c, 0x84, 0xa9, 0x03, 0xee, 0x2a,
	0xc4, 0xf3, 0x22, 0xab, 0x7c, 0xe2, 0x25, 0xf5,
	0x67, 0xa3, 0xe4, 0x11, 0xe0, 0x59, 0xb3, 0xca,
	0x87, 0xa0, 0xae, 0xc9, 0xa6, 0x62, 0x1b, 0x6e,
	0x4d, 0x02, 0x6b, 0x07, 0x9d, 0xfd, 0xd0, 0x92,
	0x06, 0xe1, 0xb2, 0x9a, 0x4a, 0x1f, 0x1f, 0x13,
	0x49, 0x99, 0x97, 0x08, 0xde, 0x7f, 0x98, 0xaf,
	0x51, 0x98, 0xee, 0x2c, 0xcb, 0xf0, 0x0b, 0xc6,
	0xb6, 0xb7, 0x2d, 0x9a, 0xb1, 0xac, 0xa6, 0xe3,
	0x15, 0x77, 0x9d, 0x6b, 0x1a, 0xe4, 0xfc, 0x8b,
	0xf2, 0x17, 0x59, 0x08, 0x04, 0x58, 0x81, 0x9d,
	0x1b, 0x1b, 0x69, 0x55, 0xc2, 0xb4, 0x3c, 0x1f,
	0x50, 0xf1, 0x7f, 0x77, 0x90, 0x4c, 0x66, 0x40,
	0x5a, 0xc0, 0x33, 0x1f, 0xcb, 0x05, 0x6d, 0x5c,
	0x06, 0x87, 0x52, 0xa2, 0x8f, 0x26, 0xd5, 0x4f
};
static const u8 enc_output010[] __initconst = {
	0xe5, 0x26, 0xa4, 0x3d, 0xbd, 0x33, 0xd0, 0x4b,
	0x6f, 0x05, 0xa7, 0x6e, 0x12, 0x7a, 0xd2, 0x74,
	0xa6, 0xdd, 0xbd, 0x95, 0xeb, 0xf9, 0xa4, 0xf1,
	0x59, 0x93, 0x91, 0x70, 0xd9, 0xfe, 0x9a, 0xcd,
	0x53, 0x1f, 0x3a, 0xab, 0xa6, 0x7c, 0x9f, 0xa6,
	0x9e, 0xbd, 0x99, 0xd9, 0xb5, 0x97, 0x44, 0xd5,
	0x14, 0x48, 0x4d, 0x9d, 0xc0, 0xd0, 0x05, 0x96,
	0xeb, 0x4c, 0x78, 0x55, 0x09, 0x08, 0x01, 0x02,
	0x30, 0x90, 0x7b, 0x96, 0x7a, 0x7b, 0x5f, 0x30,
	0x41, 0x24, 0xce, 0x68, 0x61, 0x49, 0x86, 0x57,
	0x82, 0xdd, 0x53, 0x1c, 0x51, 0x28, 0x2b, 0x53,
	0x6e, 0x2d, 0xc2, 0x20, 0x4c, 0xdd, 0x8f, 0x65,
	0x10, 0x20, 0x50, 0xdd, 0x9d, 0x50, 0xe5, 0x71,
	0x40, 0x53, 0x69, 0xfc, 0x77, 0x48, 0x11, 0xb9,
	0xde, 0xa4, 0x8d, 0x58, 0xe4, 0xa6, 0x1a, 0x18,
	0x47, 0x81, 0x7e, 0xfc, 0xdd, 0xf6, 0xef, 0xce,
	0x2f, 0x43, 0x68, 0xd6, 0x06, 0xe2, 0x74, 0x6a,
	0xad, 0x90, 0xf5, 0x37, 0xf3, 0x3d, 0x82, 0x69,
	0x40, 0xe9, 0x6b, 0xa7, 0x3d, 0xa8, 0x1e, 0xd2,
	0x02, 0x7c, 0xb7, 0x9b, 0xe4, 0xda, 0x8f, 0x95,
	0x06, 0xc5, 0xdf, 0x73, 0xa3, 0x20, 0x9a, 0x49,
	0xde, 0x9c, 0xbc, 0xee, 0x14, 0x3f, 0x81, 0x5e,
	0xf8, 0x3b, 0x59, 0x3c, 0xe1, 0x68, 0x12, 0x5a,
	0x3a, 0x76, 0x3a, 0x3f, 0xf7, 0x87, 0x33, 0x0a,
	0x01, 0xb8, 0xd4, 0xed, 0xb6, 0xbe, 0x94, 0x5e,
	0x70, 0x40, 0x56, 0x67, 0x1f, 0x50, 0x44, 0x19,
	0xce, 0x82, 0x70, 0x10, 0x87, 0x13, 0x20, 0x0b,
	0x4c, 0x5a, 0xb6, 0xf6, 0xa7, 0xae, 0x81, 0x75,
	0x01, 0x81, 0xe6, 0x4b, 0x57, 0x7c, 0xdd, 0x6d,
	0xf8, 0x1c, 0x29, 0x32, 0xf7, 0xda, 0x3c, 0x2d,
	0xf8, 0x9b, 0x25, 0x6e, 0x00, 0xb4, 0xf7, 0x2f,
	0xf7, 0x04, 0xf7, 0xa1, 0x56, 0xac, 0x4f, 0x1a,
	0x64, 0xb8, 0x47, 0x55, 0x18, 0x7b, 0x07, 0x4d,
	0xbd, 0x47, 0x24, 0x80, 0x5d, 0xa2, 0x70, 0xc5,
	0xdd, 0x8e, 0x82, 0xd4, 0xeb, 0xec, 0xb2, 0x0c,
	0x39, 0xd2, 0x97, 0xc1, 0xcb, 0xeb, 0xf4, 0x77,
	0x59, 0xb4, 0x87, 0xef, 0xcb, 0x43, 0x2d, 0x46,
	0x54, 0xd1, 0xa7, 0xd7, 0x15, 0x99, 0x0a, 0x43,
	0xa1, 0xe0, 0x99, 0x33, 0x71, 0xc1, 0xed, 0xfe,
	0x72, 0x46, 0x33, 0x8e, 0x91, 0x08, 0x9f, 0xc8,
	0x2e, 0xca, 0xfa, 0xdc, 0x59, 0xd5, 0xc3, 0x76,
	0x84, 0x9f, 0xa3, 0x37, 0x68, 0xc3, 0xf0, 0x47,
	0x2c, 0x68, 0xdb, 0x5e, 0xc3, 0x49, 0x4c, 0xe8,
	0x92, 0x85, 0xe2, 0x23, 0xd3, 0x3f, 0xad, 0x32,
	0xe5, 0x2b, 0x82, 0xd7, 0x8f, 0x99, 0x0a, 0x59,
	0x5c, 0x45, 0xd9, 0xb4, 0x51, 0x52, 0xc2, 0xae,
	0xbf, 0x80, 0xcf, 0xc9, 0xc9, 0x51, 0x24, 0x2a,
	0x3b, 0x3a, 0x4d, 0xae, 0xeb, 0xbd, 0x22, 0xc3,
	0x0e, 0x0f, 0x59, 0x25, 0x92, 0x17, 0xe9, 0x74,
	0xc7, 0x8b, 0x70, 0x70, 0x36, 0x55, 0x95, 0x75,
	0x4b, 0xad, 0x61, 0x2b, 0x09, 0xbc, 0x82, 0xf2,
	0x6e, 0x94, 0x43, 0xae, 0xc3, 0xd5, 0xcd, 0x8e,
	0xfe, 0x5b, 0x9a, 0x88, 0x43, 0x01, 0x75, 0xb2,
	0x23, 0x09, 0xf7, 0x89, 0x83, 0xe7, 0xfa, 0xf9,
	0xb4, 0x9b, 0xf8, 0xef, 0xbd, 0x1c, 0x92, 0xc1,
	0xda, 0x7e, 0xfe, 0x05, 0xba, 0x5a, 0xcd, 0x07,
	0x6a, 0x78, 0x9e, 0x5d, 0xfb, 0x11, 0x2f, 0x79,
	0x38, 0xb6, 0xc2, 0x5b, 0x6b, 0x51, 0xb4, 0x71,
	0xdd, 0xf7, 0x2a, 0xe4, 0xf4, 0x72, 0x76, 0xad,
	0xc2, 0xdd, 0x64, 0x5d, 0x79, 0xb6, 0xf5, 0x7a,
	0x77, 0x20, 0x05, 0x3d, 0x30, 0x06, 0xd4, 0x4c,
	0x0a, 0x2c, 0x98, 0x5a, 0xb9, 0xd4, 0x98, 0xa9,
	0x3f, 0xc6, 0x12, 0xea, 0x3b, 0x4b, 0xc5, 0x79,
	0x64, 0x63, 0x6b, 0x09, 0x54, 0x3b, 0x14, 0x27,
	0xba, 0x99, 0x80, 0xc8, 0x72, 0xa8, 0x12, 0x90,
	0x29, 0xba, 0x40, 0x54, 0x97, 0x2b, 0x7b, 0xfe,
	0xeb, 0xcd, 0x01, 0x05, 0x44, 0x72, 0xdb, 0x99,
	0xe4, 0x61, 0xc9, 0x69, 0xd6, 0xb9, 0x28, 0xd1,
	0x05, 0x3e, 0xf9, 0x0b, 0x49, 0x0a, 0x49, 0xe9,
	0x8d, 0x0e, 0xa7, 0x4a, 0x0f, 0xaf, 0x32, 0xd0,
	0xe0, 0xb2, 0x3a, 0x55, 0x58, 0xfe, 0x5c, 0x28,
	0x70, 0x51, 0x23, 0xb0, 0x7b, 0x6a, 0x5f, 0x1e,
	0xb8, 0x17, 0xd7, 0x94, 0x15, 0x8f, 0xee, 0x20,
	0xc7, 0x42, 0x25, 0x3e, 0x9a, 0x14, 0xd7, 0x60,
	0x72, 0x39, 0x47, 0x48, 0xa9, 0xfe, 0xdd, 0x47,
	0x0a, 0xb1, 0xe6, 0x60, 0x28, 0x8c, 0x11, 0x68,
	0xe1, 0xff, 0xd7, 0xce, 0xc8, 0xbe, 0xb3, 0xfe,
	0x27, 0x30, 0x09, 0x70, 0xd7, 0xfa, 0x02, 0x33,
	0x3a, 0x61, 0x2e, 0xc7, 0xff, 0xa4, 0x2a, 0xa8,
	0x6e, 0xb4, 0x79, 0x35, 0x6d, 0x4c, 0x1e, 0x38,
	0xf8, 0xee, 0xd4, 0x84, 0x4e, 0x6e, 0x28, 0xa7,
	0xce, 0xc8, 0xc1, 0xcf, 0x80, 0x05, 0xf3, 0x04,
	0xef, 0xc8, 0x18, 0x28, 0x2e, 0x8d, 0x5e, 0x0c,
	0xdf, 0xb8, 0x5f, 0x96, 0xe8, 0xc6, 0x9c, 0x2f,
	0xe5, 0xa6, 0x44, 0xd7, 0xe7, 0x99, 0x44, 0x0c,
	0xec, 0xd7, 0x05, 0x60, 0x97, 0xbb, 0x74, 0x77,
	0x58, 0xd5, 0xbb, 0x48, 0xde, 0x5a, 0xb2, 0x54,
	0x7f, 0x0e, 0x46, 0x70, 0x6a, 0x6f, 0x78, 0xa5,
	0x08, 0x89, 0x05, 0x4e, 0x7e, 0xa0, 0x69, 0xb4,
	0x40, 0x60, 0x55, 0x77, 0x75, 0x9b, 0x19, 0xf2,
	0xd5, 0x13, 0x80, 0x77, 0xf9, 0x4b, 0x3f, 0x1e,
	0xee, 0xe6, 0x76, 0x84, 0x7b, 0x8c, 0xe5, 0x27,
	0xa8, 0x0a, 0x91, 0x01, 0x68, 0x71, 0x8a, 0x3f,
	0x06, 0xab, 0xf6, 0xa9, 0xa5, 0xe6, 0x72, 0x92,
	0xe4, 0x67, 0xe2, 0xa2, 0x46, 0x35, 0x84, 0x55,
	0x7d, 0xca, 0xa8, 0x85, 0xd0, 0xf1, 0x3f, 0xbe,
	0xd7, 0x34, 0x64, 0xfc, 0xae, 0xe3, 0xe4, 0x04,
	0x9f, 0x66, 0x02, 0xb9, 0x88, 0x10, 0xd9, 0xc4,
	0x4c, 0x31, 0x43, 0x7a, 0x93, 0xe2, 0x9b, 0x56,
	0x43, 0x84, 0xdc, 0xdc, 0xde, 0x1d, 0xa4, 0x02,
	0x0e, 0xc2, 0xef, 0xc3, 0xf8, 0x78, 0xd1, 0xb2,
	0x6b, 0x63, 0x18, 0xc9, 0xa9, 0xe5, 0x72, 0xd8,
	0xf3, 0xb9, 0xd1, 0x8a, 0xc7, 0x1a, 0x02, 0x27,
	0x20, 0x77, 0x10, 0xe5, 0xc8, 0xd4, 0x4a, 0x47,
	0xe5, 0xdf, 0x5f, 0x01, 0xaa, 0xb0, 0xd4, 0x10,
	0xbb, 0x69, 0xe3, 0x36, 0xc8, 0xe1, 0x3d, 0x43,
	0xfb, 0x86, 0xcd, 0xcc, 0xbf, 0xf4, 0x88, 0xe0,
	0x20, 0xca, 0xb7, 0x1b, 0xf1, 0x2f, 0x5c, 0xee,
	0xd4, 0xd3, 0xa3, 0xcc, 0xa4, 0x1e, 0x1c, 0x47,
	0xfb, 0xbf, 0xfc, 0xa2, 0x41, 0x55, 0x9d, 0xf6,
	0x5a, 0x5e, 0x65, 0x32, 0x34, 0x7b, 0x52, 0x8d,
	0xd5, 0xd0, 0x20, 0x60, 0x03, 0xab, 0x3f, 0x8c,
	0xd4, 0x21, 0xea, 0x2a, 0xd9, 0xc4, 0xd0, 0xd3,
	0x65, 0xd8, 0x7a, 0x13, 0x28, 0x62, 0x32, 0x4b,
	0x2c, 0x87, 0x93, 0xa8, 0xb4, 0x52, 0x45, 0x09,
	0x44, 0xec, 0xec, 0xc3, 0x17, 0xdb, 0x9a, 0x4d,
	0x5c, 0xa9, 0x11, 0xd4, 0x7d, 0xaf, 0x9e, 0xf1,
	0x2d, 0xb2, 0x66, 0xc5, 0x1d, 0xed, 0xb7, 0xcd,
	0x0b, 0x25, 0x5e, 0x30, 0x47, 0x3f, 0x40, 0xf4,
	0xa1, 0xa0, 0x00, 0x94, 0x10, 0xc5, 0x6a, 0x63,
	0x1a, 0xd5, 0x88, 0x92, 0x8e, 0x82, 0x39, 0x87,
	0x3c, 0x78, 0x65, 0x58, 0x42, 0x75, 0x5b, 0xdd,
	0x77, 0x3e, 0x09, 0x4e, 0x76, 0x5b, 0xe6, 0x0e,
	0x4d, 0x38, 0xb2, 0xc0, 0xb8, 0x95, 0x01, 0x7a,
	0x10, 0xe0, 0xfb, 0x07, 0xf2, 0xab, 0x2d, 0x8c,
	0x32, 0xed, 0x2b, 0xc0, 0x46, 0xc2, 0xf5, 0x38,
	0x83, 0xf0, 0x17, 0xec, 0xc1, 0x20, 0x6a, 0x9a,
	0x0b, 0x00, 0xa0, 0x98, 0x22, 0x50, 0x23, 0xd5,
	0x80, 0x6b, 0xf6, 0x1f, 0xc3, 0xcc, 0x97, 0xc9,
	0x24, 0x9f, 0xf3, 0xaf, 0x43, 0x14, 0xd5, 0xa0
};
static const u8 enc_assoc010[] __initconst = {
	0xd2, 0xa1, 0x70, 0xdb, 0x7a, 0xf8, 0xfa, 0x27,
	0xba, 0x73, 0x0f, 0xbf, 0x3d, 0x1e, 0x82, 0xb2
};
static const u8 enc_nonce010[] __initconst = {
	0xdb, 0x92, 0x0f, 0x7f, 0x17, 0x54, 0x0c, 0x30
};
static const u8 enc_key010[] __initconst = {
	0x47, 0x11, 0xeb, 0x86, 0x2b, 0x2c, 0xab, 0x44,
	0x34, 0xda, 0x7f, 0x57, 0x03, 0x39, 0x0c, 0xaf,
	0x2c, 0x14, 0xfd, 0x65, 0x23, 0xe9, 0x8e, 0x74,
	0xd5, 0x08, 0x68, 0x08, 0xe7, 0xb4, 0x72, 0xd7
};

static const u8 enc_input011[] __initconst = {
	0x7a, 0x57, 0xf2, 0xc7, 0x06, 0x3f, 0x50, 0x7b,
	0x36, 0x1a, 0x66, 0x5c, 0xb9, 0x0e, 0x5e, 0x3b,
	0x45, 0x60, 0xbe, 0x9a, 0x31, 0x9f, 0xff, 0x5d,
	0x66, 0x34, 0xb4, 0xdc, 0xfb, 0x9d, 0x8e, 0xee,
	0x6a, 0x33, 0xa4, 0x07, 0x3c, 0xf9, 0x4c, 0x30,
	0xa1, 0x24, 0x52, 0xf9, 0x50, 0x46, 0x88, 0x20,
	0x02, 0x32, 0x3a, 0x0e, 0x99, 0x63, 0xaf, 0x1f,
	0x15, 0x28, 0x2a, 0x05, 0xff, 0x57, 0x59, 0x5e,
	0x18, 0xa1, 0x1f, 0xd0, 0x92, 0x5c, 0x88, 0x66,
	0x1b, 0x00, 0x64, 0xa5, 0x93, 0x8d, 0x06, 0x46,
	0xb0, 0x64, 0x8b, 0x8b, 0xef, 0x99, 0x05, 0x35,
	0x85, 0xb3, 0xf3, 0x33, 0xbb, 0xec, 0x66, 0xb6,
	0x3d, 0x57, 0x42, 0xe3, 0xb4, 0xc6, 0xaa, 0xb0,
	0x41, 0x2a, 0xb9, 0x59, 0xa9, 0xf6, 0x3e, 0x15,
	0x26, 0x12, 0x03, 0x21, 0x4c, 0x74, 0x43, 0x13,
	0x2a, 0x03, 0x27, 0x09, 0xb4, 0xfb, 0xe7, 0xb7,
	0x40, 0xff, 0x5e, 0xce, 0x48, 0x9a, 0x60, 0xe3,
	0x8b, 0x80, 0x8c, 0x38, 0x2d, 0xcb, 0x93, 0x37,
	0x74, 0x05, 0x52, 0x6f, 0x73, 0x3e, 0xc3, 0xbc,
	0xca, 0x72, 0x0a, 0xeb, 0xf1, 0x3b, 0xa0, 0x95,
	0xdc, 0x8a, 0xc4, 0xa9, 0xdc, 0xca, 0x44, 0xd8,
	0x08, 0x63, 0x6a, 0x36, 0xd3, 0x3c, 0xb8, 0xac,
	0x46, 0x7d, 0xfd, 0xaa, 0xeb, 0x3e, 0x0f, 0x45,
	0x8f, 0x49, 0xda, 0x2b, 0xf2, 0x12, 0xbd, 0xaf,
	0x67, 0x8a, 0x63, 0x48, 0x4b, 0x55, 0x5f, 0x6d,
	0x8c, 0xb9, 0x76, 0x34, 0x84, 0xae, 0xc2, 0xfc,
	0x52, 0x64, 0x82, 0xf7, 0xb0, 0x06, 0xf0, 0x45,
	0x73, 0x12, 0x50, 0x30, 0x72, 0xea, 0x78, 0x9a,
	0xa8, 0xaf, 0xb5, 0xe3, 0xbb, 0x77, 0x52, 0xec,
	0x59, 0x84, 0xbf, 0x6b, 0x8f, 0xce, 0x86, 0x5e,
	0x1f, 0x23, 0xe9, 0xfb, 0x08, 0x86, 0xf7, 0x10,
	0xb9, 0xf2, 0x44, 0x96, 0x44, 0x63, 0xa9, 0xa8,
	0x78, 0x00, 0x23, 0xd6, 0xc7, 0xe7, 0x6e, 0x66,
	0x4f, 0xcc, 0xee, 0x15, 0xb3, 0xbd, 0x1d, 0xa0,
	0xe5, 0x9c, 0x1b, 0x24, 0x2c, 0x4d, 0x3c, 0x62,
	0x35, 0x9c, 0x88, 0x59, 0x09, 0xdd, 0x82, 0x1b,
	0xcf, 0x0a, 0x83, 0x6b, 0x3f, 0xae, 0x03, 0xc4,
	0xb4, 0xdd, 0x7e, 0x5b, 0x28, 0x76, 0x25, 0x96,
	0xd9, 0xc9, 0x9d, 0x5f, 0x86, 0xfa, 0xf6, 0xd7,
	0xd2, 0xe6, 0x76, 0x1d, 0x0f, 0xa1, 0xdc, 0x74,
	0x05, 0x1b, 0x1d, 0xe0, 0xcd, 0x16, 0xb0, 0xa8,
	0x8a, 0x34, 0x7b, 0x15, 0x11, 0x77, 0xe5, 0x7b,
	0x7e, 0x20, 0xf7, 0xda, 0x38, 0xda, 0xce, 0x70,
	0xe9, 0xf5, 0x6c, 0xd9, 0xbe, 0x0c, 0x4c, 0x95,
	0x4c, 0xc2, 0x9b, 0x34, 0x55, 0x55, 0xe1, 0xf3,
	0x46, 0x8e, 0x48, 0x74, 0x14, 0x4f, 0x9d, 0xc9,
	0xf5, 0xe8, 0x1a, 0xf0, 0x11, 0x4a, 0xc1, 0x8d,
	0xe0, 0x93, 0xa0, 0xbe, 0x09, 0x1c, 0x2b, 0x4e,
	0x0f, 0xb2, 0x87, 0x8b, 0x84, 0xfe, 0x92, 0x32,
	0x14, 0xd7, 0x93, 0xdf, 0xe7, 0x44, 0xbc, 0xc5,
	0xae, 0x53, 0x69, 0xd8, 0xb3, 0x79, 0x37, 0x80,
	0xe3, 0x17, 0x5c, 0xec, 0x53, 0x00, 0x9a, 0xe3,
	0x8e, 0xdc, 0x38, 0xb8, 0x66, 0xf0, 0xd3, 0xad,
	0x1d, 0x02, 0x96, 0x86, 0x3e, 0x9d, 0x3b, 0x5d,
	0xa5, 0x7f, 0x21, 0x10, 0xf1, 0x1f, 0x13, 0x20,
	0xf9, 0x57, 0x87, 0x20, 0xf5, 0x5f, 0xf1, 0x17,
	0x48, 0x0a, 0x51, 0x5a, 0xcd, 0x19, 0x03, 0xa6,
	0x5a, 0xd1, 0x12, 0x97, 0xe9, 0x48, 0xe2, 0x1d,
	0x83, 0x75, 0x50, 0xd9, 0x75, 0x7d, 0x6a, 0x82,
	0xa1, 0xf9, 0x4e, 0x54, 0x87, 0x89, 0xc9, 0x0c,
	0xb7, 0x5b, 0x6a, 0x91, 0xc1, 0x9c, 0xb2, 0xa9,
	0xdc, 0x9a, 0xa4, 0x49, 0x0a, 0x6d, 0x0d, 0xbb,
	0xde, 0x86, 0x44, 0xdd, 0x5d, 0x89, 0x2b, 0x96,
	0x0f, 0x23, 0x95, 0xad, 0xcc, 0xa2, 0xb3, 0xb9,
	0x7e, 0x74, 0x38, 0xba, 0x9f, 0x73, 0xae, 0x5f,
	0xf8, 0x68, 0xa2, 0xe0, 0xa9, 0xce, 0xbd, 0x40,
	0xd4, 0x4c, 0x6b, 0xd2, 0x56, 0x62, 0xb0, 0xcc,
	0x63, 0x7e, 0x5b, 0xd3, 0xae, 0xd1, 0x75, 0xce,
	0xbb, 0xb4, 0x5b, 0xa8, 0xf8, 0xb4, 0xac, 0x71,
	0x75, 0xaa, 0xc9, 0x9f, 0xbb, 0x6c, 0xad, 0x0f,
	0x55, 0x5d, 0xe8, 0x85, 0x7d, 0xf9, 0x21, 0x35,
	0xea, 0x92, 0x85, 0x2b, 0x00, 0xec, 0x84, 0x90,
	0x0a, 0x63, 0x96, 0xe4, 0x6b, 0xa9, 0x77, 0xb8,
	0x91, 0xf8, 0x46, 0x15, 0x72, 0x63, 0x70, 0x01,
	0x40, 0xa3, 0xa5, 0x76, 0x62, 0x2b, 0xbf, 0xf1,
	0xe5, 0x8d, 0x9f, 0xa3, 0xfa, 0x9b, 0x03, 0xbe,
	0xfe, 0x65, 0x6f, 0xa2, 0x29, 0x0d, 0x54, 0xb4,
	0x71, 0xce, 0xa9, 0xd6, 0x3d, 0x88, 0xf9, 0xaf,
	0x6b, 0xa8, 0x9e, 0xf4, 0x16, 0x96, 0x36, 0xb9,
	0x00, 0xdc, 0x10, 0xab, 0xb5, 0x08, 0x31, 0x1f,
	0x00, 0xb1, 0x3c, 0xd9, 0x38, 0x3e, 0xc6, 0x04,
	0xa7, 0x4e, 0xe8, 0xae, 0xed, 0x98, 0xc2, 0xf7,
	0xb9, 0x00, 0x5f, 0x8c, 0x60, 0xd1, 0xe5, 0x15,
	0xf7, 0xae, 0x1e, 0x84, 0x88, 0xd1, 0xf6, 0xbc,
	0x3a, 0x89, 0x35, 0x22, 0x83, 0x7c, 0xca, 0xf0,
	0x33, 0x82, 0x4c, 0x79, 0x3c, 0xfd, 0xb1, 0xae,
	0x52, 0x62, 0x55, 0xd2, 0x41, 0x60, 0xc6, 0xbb,
	0xfa, 0x0e, 0x59, 0xd6, 0xa8, 0xfe, 0x5d, 0xed,
	0x47, 0x3d, 0xe0, 0xea, 0x1f, 0x6e, 0x43, 0x51,
	0xec, 0x10, 0x52, 0x56, 0x77, 0x42, 0x6b, 0x52,
	0x87, 0xd8, 0xec, 0xe0, 0xaa, 0x76, 0xa5, 0x84,
	0x2a, 0x22, 0x24, 0xfd, 0x92, 0x40, 0x88, 0xd5,
	0x85, 0x1c, 0x1f, 0x6b, 0x47, 0xa0, 0xc4, 0xe4,
	0xef, 0xf4, 0xea, 0xd7, 0x59, 0xac, 0x2a, 0x9e,
	0x8c, 0xfa, 0x1f, 0x42, 0x08, 0xfe, 0x4f, 0x74,
	0xa0, 0x26, 0xf5, 0xb3, 0x84, 0xf6, 0x58, 0x5f,
	0x26, 0x66, 0x3e, 0xd7, 0xe4, 0x22, 0x91, 0x13,
	0xc8, 0xac, 0x25, 0x96, 0x23, 0xd8, 0x09, 0xea,
	0x45, 0x75, 0x23, 0xb8, 0x5f, 0xc2, 0x90, 0x8b,
	0x09, 0xc4, 0xfc, 0x47, 0x6c, 0x6d, 0x0a, 0xef,
	0x69, 0xa4, 0x38, 0x19, 0xcf, 0x7d, 0xf9, 0x09,
	0x73, 0x9b, 0x60, 0x5a, 0xf7, 0x37, 0xb5, 0xfe,
	0x9f, 0xe3, 0x2b, 0x4c, 0x0d, 0x6e, 0x19, 0xf1,
	0xd6, 0xc0, 0x70, 0xf3, 0x9d, 0x22, 0x3c, 0xf9,
	0x49, 0xce, 0x30, 0x8e, 0x44, 0xb5, 0x76, 0x15,
	0x8f, 0x52, 0xfd, 0xa5, 0x04, 0xb8, 0x55, 0x6a,
	0x36, 0x59, 0x7c, 0xc4, 0x48, 0xb8, 0xd7, 0xab,
	0x05, 0x66, 0xe9, 0x5e, 0x21, 0x6f, 0x6b, 0x36,
	0x29, 0xbb, 0xe9, 0xe3, 0xa2, 0x9a, 0xa8, 0xcd,
	0x55, 0x25, 0x11, 0xba, 0x5a, 0x58, 0xa0, 0xde,
	0xae, 0x19, 0x2a, 0x48, 0x5a, 0xff, 0x36, 0xcd,
	0x6d, 0x16, 0x7a, 0x73, 0x38, 0x46, 0xe5, 0x47,
	0x59, 0xc8, 0xa2, 0xf6, 0xe2, 0x6c, 0x83, 0xc5,
	0x36, 0x2c, 0x83, 0x7d, 0xb4, 0x01, 0x05, 0x69,
	0xe7, 0xaf, 0x5c, 0xc4, 0x64, 0x82, 0x12, 0x21,
	0xef, 0xf7, 0xd1, 0x7d, 0xb8, 0x8d, 0x8c, 0x98,
	0x7c, 0x5f, 0x7d, 0x92, 0x88, 0xb9, 0x94, 0x07,
	0x9c, 0xd8, 0xe9, 0x9c, 0x17, 0x38, 0xe3, 0x57,
	0x6c, 0xe0, 0xdc, 0xa5, 0x92, 0x42, 0xb3, 0xbd,
	0x50, 0xa2, 0x7e, 0xb5, 0xb1, 0x52, 0x72, 0x03,
	0x97, 0xd8, 0xaa, 0x9a, 0x1e, 0x75, 0x41, 0x11,
	0xa3, 0x4f, 0xcc, 0xd4, 0xe3, 0x73, 0xad, 0x96,
	0xdc, 0x47, 0x41, 0x9f, 0xb0, 0xbe, 0x79, 0x91,
	0xf5, 0xb6, 0x18, 0xfe, 0xc2, 0x83, 0x18, 0x7d,
	0x73, 0xd9, 0x4f, 0x83, 0x84, 0x03, 0xb3, 0xf0,
	0x77, 0x66, 0x3d, 0x83, 0x63, 0x2e, 0x2c, 0xf9,
	0xdd, 0xa6, 0x1f, 0x89, 0x82, 0xb8, 0x23, 0x42,
	0xeb, 0xe2, 0xca, 0x70, 0x82, 0x61, 0x41, 0x0a,
	0x6d, 0x5f, 0x75, 0xc5, 0xe2, 0xc4, 0x91, 0x18,
	0x44, 0x22, 0xfa, 0x34, 0x10, 0xf5, 0x20, 0xdc,
	0xb7, 0xdd, 0x2a, 0x20, 0x77, 0xf5, 0xf9, 0xce,
	0xdb, 0xa0, 0x0a, 0x52, 0x2a, 0x4e, 0xdd, 0xcc,
	0x97, 0xdf, 0x05, 0xe4, 0x5e, 0xb7, 0xaa, 0xf0,
	0xe2, 0x80, 0xff, 0xba, 0x1a, 0x0f, 0xac, 0xdf,
	0x02, 0x32, 0xe6, 0xf7, 0xc7, 0x17, 0x13, 0xb7,
	0xfc, 0x98, 0x48, 0x8c, 0x0d, 0x82, 0xc9, 0x80,
	0x7a, 0xe2, 0x0a, 0xc5, 0xb4, 0xde, 0x7c, 0x3c,
	0x79, 0x81, 0x0e, 0x28, 0x65, 0x79, 0x67, 0x82,
	0x69, 0x44, 0x66, 0x09, 0xf7, 0x16, 0x1a, 0xf9,
	0x7d, 0x80, 0xa1, 0x79, 0x14, 0xa9, 0xc8, 0x20,
	0xfb, 0xa2, 0x46, 0xbe, 0x08, 0x35, 0x17, 0x58,
	0xc1, 0x1a, 0xda, 0x2a, 0x6b, 0x2e, 0x1e, 0xe6,
	0x27, 0x55, 0x7b, 0x19, 0xe2, 0xfb, 0x64, 0xfc,
	0x5e, 0x15, 0x54, 0x3c, 0xe7, 0xc2, 0x11, 0x50,
	0x30, 0xb8, 0x72, 0x03, 0x0b, 0x1a, 0x9f, 0x86,
	0x27, 0x11, 0x5c, 0x06, 0x2b, 0xbd, 0x75, 0x1a,
	0x0a, 0xda, 0x01, 0xfa, 0x5c, 0x4a, 0xc1, 0x80,
	0x3a, 0x6e, 0x30, 0xc8, 0x2c, 0xeb, 0x56, 0xec,
	0x89, 0xfa, 0x35, 0x7b, 0xb2, 0xf0, 0x97, 0x08,
	0x86, 0x53, 0xbe, 0xbd, 0x40, 0x41, 0x38, 0x1c,
	0xb4, 0x8b, 0x79, 0x2e, 0x18, 0x96, 0x94, 0xde,
	0xe8, 0xca, 0xe5, 0x9f, 0x92, 0x9f, 0x15, 0x5d,
	0x56, 0x60, 0x5c, 0x09, 0xf9, 0x16, 0xf4, 0x17,
	0x0f, 0xf6, 0x4c, 0xda, 0xe6, 0x67, 0x89, 0x9f,
	0xca, 0x6c, 0xe7, 0x9b, 0x04, 0x62, 0x0e, 0x26,
	0xa6, 0x52, 0xbd, 0x29, 0xff, 0xc7, 0xa4, 0x96,
	0xe6, 0x6a, 0x02, 0xa5, 0x2e, 0x7b, 0xfe, 0x97,
	0x68, 0x3e, 0x2e, 0x5f, 0x3b, 0x0f, 0x36, 0xd6,
	0x98, 0x19, 0x59, 0x48, 0xd2, 0xc6, 0xe1, 0x55,
	0x1a, 0x6e, 0xd6, 0xed, 0x2c, 0xba, 0xc3, 0x9e,
	0x64, 0xc9, 0x95, 0x86, 0x35, 0x5e, 0x3e, 0x88,
	0x69, 0x99, 0x4b, 0xee, 0xbe, 0x9a, 0x99, 0xb5,
	0x6e, 0x58, 0xae, 0xdd, 0x22, 0xdb, 0xdd, 0x6b,
	0xfc, 0xaf, 0x90, 0xa3, 0x3d, 0xa4, 0xc1, 0x15,
	0x92, 0x18, 0x8d, 0xd2, 0x4b, 0x7b, 0x06, 0xd1,
	0x37, 0xb5, 0xe2, 0x7c, 0x2c, 0xf0, 0x25, 0xe4,
	0x94, 0x2a, 0xbd, 0xe3, 0x82, 0x70, 0x78, 0xa3,
	0x82, 0x10, 0x5a, 0x90, 0xd7, 0xa4, 0xfa, 0xaf,
	0x1a, 0x88, 0x59, 0xdc, 0x74, 0x12, 0xb4, 0x8e,
	0xd7, 0x19, 0x46, 0xf4, 0x84, 0x69, 0x9f, 0xbb,
	0x70, 0xa8, 0x4c, 0x52, 0x81, 0xa9, 0xff, 0x76,
	0x1c, 0xae, 0xd8, 0x11, 0x3d, 0x7f, 0x7d, 0xc5,
	0x12, 0x59, 0x28, 0x18, 0xc2, 0xa2, 0xb7, 0x1c,
	0x88, 0xf8, 0xd6, 0x1b, 0xa6, 0x7d, 0x9e, 0xde,
	0x29, 0xf8, 0xed, 0xff, 0xeb, 0x92, 0x24, 0x4f,
	0x05, 0xaa, 0xd9, 0x49, 0xba, 0x87, 0x59, 0x51,
	0xc9, 0x20, 0x5c, 0x9b, 0x74, 0xcf, 0x03, 0xd9,
	0x2d, 0x34, 0xc7, 0x5b, 0xa5, 0x40, 0xb2, 0x99,
	0xf5, 0xcb, 0xb4, 0xf6, 0xb7, 0x72, 0x4a, 0xd6,
	0xbd, 0xb0, 0xf3, 0x93, 0xe0, 0x1b, 0xa8, 0x04,
	0x1e, 0x35, 0xd4, 0x80, 0x20, 0xf4, 0x9c, 0x31,
	0x6b, 0x45, 0xb9, 0x15, 0xb0, 0x5e, 0xdd, 0x0a,
	0x33, 0x9c, 0x83, 0xcd, 0x58, 0x89, 0x50, 0x56,
	0xbb, 0x81, 0x00, 0x91, 0x32, 0xf3, 0x1b, 0x3e,
	0xcf, 0x45, 0xe1, 0xf9, 0xe1, 0x2c, 0x26, 0x78,
	0x93, 0x9a, 0x60, 0x46, 0xc9, 0xb5, 0x5e, 0x6a,
	0x28, 0x92, 0x87, 0x3f, 0x63, 0x7b, 0xdb, 0xf7,
	0xd0, 0x13, 0x9d, 0x32, 0x40, 0x5e, 0xcf, 0xfb,
	0x79, 0x68, 0x47, 0x4c, 0xfd, 0x01, 0x17, 0xe6,
	0x97, 0x93, 0x78, 0xbb, 0xa6, 0x27, 0xa3, 0xe8,
	0x1a, 0xe8, 0x94, 0x55, 0x7d, 0x08, 0xe5, 0xdc,
	0x66, 0xa3, 0x69, 0xc8, 0xca, 0xc5, 0xa1, 0x84,
	0x55, 0xde, 0x08, 0x91, 0x16, 0x3a, 0x0c, 0x86,
	0xab, 0x27, 0x2b, 0x64, 0x34, 0x02, 0x6c, 0x76,
	0x8b, 0xc6, 0xaf, 0xcc, 0xe1, 0xd6, 0x8c, 0x2a,
	0x18, 0x3d, 0xa6, 0x1b, 0x37, 0x75, 0x45, 0x73,
	0xc2, 0x75, 0xd7, 0x53, 0x78, 0x3a, 0xd6, 0xe8,
	0x29, 0xd2, 0x4a, 0xa8, 0x1e, 0x82, 0xf6, 0xb6,
	0x81, 0xde, 0x21, 0xed, 0x2b, 0x56, 0xbb, 0xf2,
	0xd0, 0x57, 0xc1, 0x7c, 0xd2, 0x6a, 0xd2, 0x56,
	0xf5, 0x13, 0x5f, 0x1c, 0x6a, 0x0b, 0x74, 0xfb,
	0xe9, 0xfe, 0x9e, 0xea, 0x95, 0xb2, 0x46, 0xab,
	0x0a, 0xfc, 0xfd, 0xf3, 0xbb, 0x04, 0x2b, 0x76,
	0x1b, 0xa4, 0x74, 0xb0, 0xc1, 0x78, 0xc3, 0x69,
	0xe2, 0xb0, 0x01, 0xe1, 0xde, 0x32, 0x4c, 0x8d,
	0x1a, 0xb3, 0x38, 0x08, 0xd5, 0xfc, 0x1f, 0xdc,
	0x0e, 0x2c, 0x9c, 0xb1, 0xa1, 0x63, 0x17, 0x22,
	0xf5, 0x6c, 0x93, 0x70, 0x74, 0x00, 0xf8, 0x39,
	0x01, 0x94, 0xd1, 0x32, 0x23, 0x56, 0x5d, 0xa6,
	0x02, 0x76, 0x76, 0x93, 0xce, 0x2f, 0x19, 0xe9,
	0x17, 0x52, 0xae, 0x6e, 0x2c, 0x6d, 0x61, 0x7f,
	0x3b, 0xaa, 0xe0, 0x52, 0x85, 0xc5, 0x65, 0xc1,
	0xbb, 0x8e, 0x5b, 0x21, 0xd5, 0xc9, 0x78, 0x83,
	0x07, 0x97, 0x4c, 0x62, 0x61, 0x41, 0xd4, 0xfc,
	0xc9, 0x39, 0xe3, 0x9b, 0xd0, 0xcc, 0x75, 0xc4,
	0x97, 0xe6, 0xdd, 0x2a, 0x5f, 0xa6, 0xe8, 0x59,
	0x6c, 0x98, 0xb9, 0x02, 0xe2, 0xa2, 0xd6, 0x68,
	0xee, 0x3b, 0x1d, 0xe3, 0x4d, 0x5b, 0x30, 0xef,
	0x03, 0xf2, 0xeb, 0x18, 0x57, 0x36, 0xe8, 0xa1,
	0xf4, 0x47, 0xfb, 0xcb, 0x8f, 0xcb, 0xc8, 0xf3,
	0x4f, 0x74, 0x9d, 0x9d, 0xb1, 0x8d, 0x14, 0x44,
	0xd9, 0x19, 0xb4, 0x54, 0x4f, 0x75, 0x19, 0x09,
	0xa0, 0x75, 0xbc, 0x3b, 0x82, 0xc6, 0x3f, 0xb8,
	0x83, 0x19, 0x6e, 0xd6, 0x37, 0xfe, 0x6e, 0x8a,
	0x4e, 0xe0, 0x4a, 0xab, 0x7b, 0xc8, 0xb4, 0x1d,
	0xf4, 0xed, 0x27, 0x03, 0x65, 0xa2, 0xa1, 0xae,
	0x11, 0xe7, 0x98, 0x78, 0x48, 0x91, 0xd2, 0xd2,
	0xd4, 0x23, 0x78, 0x50, 0xb1, 0x5b, 0x85, 0x10,
	0x8d, 0xca, 0x5f, 0x0f, 0x71, 0xae, 0x72, 0x9a,
	0xf6, 0x25, 0x19, 0x60, 0x06, 0xf7, 0x10, 0x34,
	0x18, 0x0d, 0xc9, 0x9f, 0x7b, 0x0c, 0x9b, 0x8f,
	0x91, 0x1b, 0x9f, 0xcd, 0x10, 0xee, 0x75, 0xf9,
	0x97, 0x66, 0xfc, 0x4d, 0x33, 0x6e, 0x28, 0x2b,
	0x92, 0x85, 0x4f, 0xab, 0x43, 0x8d, 0x8f, 0x7d,
	0x86, 0xa7, 0xc7, 0xd8, 0xd3, 0x0b, 0x8b, 0x57,
	0xb6, 0x1d, 0x95, 0x0d, 0xe9, 0xbc, 0xd9, 0x03,
	0xd9, 0x10, 0x19, 0xc3, 0x46, 0x63, 0x55, 0x87,
	0x61, 0x79, 0x6c, 0x95, 0x0e, 0x9c, 0xdd, 0xca,
	0xc3, 0xf3, 0x64, 0xf0, 0x7d, 0x76, 0xb7, 0x53,
	0x67, 0x2b, 0x1e, 0x44, 0x56, 0x81, 0xea, 0x8f,
	0x5c, 0x42, 0x16, 0xb8, 0x28, 0xeb, 0x1b, 0x61,
	0x10, 0x1e, 0xbf, 0xec, 0xa8
};
static const u8 enc_output011[] __initconst = {
	0x6a, 0xfc, 0x4b, 0x25, 0xdf, 0xc0, 0xe4, 0xe8,
	0x17, 0x4d, 0x4c, 0xc9, 0x7e, 0xde, 0x3a, 0xcc,
	0x3c, 0xba, 0x6a, 0x77, 0x47, 0xdb, 0xe3, 0x74,
	0x7a, 0x4d, 0x5f, 0x8d, 0x37, 0x55, 0x80, 0x73,
	0x90, 0x66, 0x5d, 0x3a, 0x7d, 0x5d, 0x86, 0x5e,
	0x8d, 0xfd, 0x83, 0xff, 0x4e, 0x74, 0x6f, 0xf9,
	0xe6, 0x70, 0x17, 0x70, 0x3e, 0x96, 0xa7, 0x7e,
	0xcb, 0xab, 0x8f, 0x58, 0x24, 0x9b, 0x01, 0xfd,
	0xcb, 0xe6, 0x4d, 0x9b, 0xf0, 0x88, 0x94, 0x57,
	0x66, 0xef, 0x72, 0x4c, 0x42, 0x6e, 0x16, 0x19,
	0x15, 0xea, 0x70, 0x5b, 0xac, 0x13, 0xdb, 0x9f,
	0x18, 0xe2, 0x3c, 0x26, 0x97, 0xbc, 0xdc, 0x45,
	0x8c, 0x6c, 0x24, 0x69, 0x9c, 0xf7, 0x65, 0x1e,
	0x18, 0x59, 0x31, 0x7c, 0xe4, 0x73, 0xbc, 0x39,
	0x62, 0xc6, 0x5c, 0x9f, 0xbf, 0xfa, 0x90, 0x03,
	0xc9, 0x72, 0x26, 0xb6, 0x1b, 0xc2, 0xb7, 0x3f,
	0xf2, 0x13, 0x77, 0xf2, 0x8d, 0xb9, 0x47, 0xd0,
	0x53, 0xdd, 0xc8, 0x91, 0x83, 0x8b, 0xb1, 0xce,
	0xa3, 0xfe, 0xcd, 0xd9, 0xdd, 0x92, 0x7b, 0xdb,
	0xb8, 0xfb, 0xc9, 0x2d, 0x01, 0x59, 0x39, 0x52,
	0xad, 0x1b, 0xec, 0xcf, 0xd7, 0x70, 0x13, 0x21,
	0xf5, 0x47, 0xaa, 0x18, 0x21, 0x5c, 0xc9, 0x9a,
	0xd2, 0x6b, 0x05, 0x9c, 0x01, 0xa1, 0xda, 0x35,
	0x5d, 0xb3, 0x70, 0xe6, 0xa9, 0x80, 0x8b, 0x91,
	0xb7, 0xb3, 0x5f, 0x24, 0x9a, 0xb7, 0xd1, 0x6b,
	0xa1, 0x1c, 0x50, 0xba, 0x49, 0xe0, 0xee, 0x2e,
	0x75, 0xac, 0x69, 0xc0, 0xeb, 0x03, 0xdd, 0x19,
	0xe5, 0xf6, 0x06, 0xdd, 0xc3, 0xd7, 0x2b, 0x07,
	0x07, 0x30, 0xa7, 0x19, 0x0c, 0xbf, 0xe6, 0x18,
	0xcc, 0xb1, 0x01, 0x11, 0x85, 0x77, 0x1d, 0x96,
	0xa7, 0xa3, 0x00, 0x84, 0x02, 0xa2, 0x83, 0x68,
	0xda, 0x17, 0x27, 0xc8, 0x7f, 0x23, 0xb7, 0xf4,
	0x13, 0x85, 0xcf, 0xdd, 0x7a, 0x7d, 0x24, 0x57,
	0xfe, 0x05, 0x93, 0xf5, 0x74, 0xce, 0xed, 0x0c,
	0x20, 0x98, 0x8d, 0x92, 0x30, 0xa1, 0x29, 0x23,
	0x1a, 0xa0, 0x4f, 0x69, 0x56, 0x4c, 0xe1, 0xc8,
	0xce, 0xf6, 0x9a, 0x0c, 0xa4, 0xfa, 0x04, 0xf6,
	0x62, 0x95, 0xf2, 0xfa, 0xc7, 0x40, 0x68, 0x40,
	0x8f, 0x41, 0xda, 0xb4, 0x26, 0x6f, 0x70, 0xab,
	0x40, 0x61, 0xa4, 0x0e, 0x75, 0xfb, 0x86, 0xeb,
	0x9d, 0x9a, 0x1f, 0xec, 0x76, 0x99, 0xe7, 0xea,
	0xaa, 0x1e, 0x2d, 0xb5, 0xd4, 0xa6, 0x1a, 0xb8,
	0x61, 0x0a, 0x1d, 0x16, 0x5b, 0x98, 0xc2, 0x31,
	0x40, 0xe7, 0x23, 0x1d, 0x66, 0x99, 0xc8, 0xc0,
	0xd7, 0xce, 0xf3, 0x57, 0x40, 0x04, 0x3f, 0xfc,
	0xea, 0xb3, 0xfc, 0xd2, 0xd3, 0x99, 0xa4, 0x94,
	0x69, 0xa0, 0xef, 0xd1, 0x85, 0xb3, 0xa6, 0xb1,
	0x28, 0xbf, 0x94, 0x67, 0x22, 0xc3, 0x36, 0x46,
	0xf8, 0xd2, 0x0f, 0x5f, 0xf4, 0x59, 0x80, 0xe6,
	0x2d, 0x43, 0x08, 0x7d, 0x19, 0x09, 0x97, 0xa7,
	0x4c, 0x3d, 0x8d, 0xba, 0x65, 0x62, 0xa3, 0x71,
	0x33, 0x29, 0x62, 0xdb, 0xc1, 0x33, 0x34, 0x1a,
	0x63, 0x33, 0x16, 0xb6, 0x64, 0x7e, 0xab, 0x33,
	0xf0, 0xe6, 0x26, 0x68, 0xba, 0x1d, 0x2e, 0x38,
	0x08, 0xe6, 0x02, 0xd3, 0x25, 0x2c, 0x47, 0x23,
	0x58, 0x34, 0x0f, 0x9d, 0x63, 0x4f, 0x63, 0xbb,
	0x7f, 0x3b, 0x34, 0x38, 0xa7, 0xb5, 0x8d, 0x65,
	0xd9, 0x9f, 0x79, 0x55, 0x3e, 0x4d, 0xe7, 0x73,
	0xd8, 0xf6, 0x98, 0x97, 0x84, 0x60, 0x9c, 0xc8,
	0xa9, 0x3c, 0xf6, 0xdc, 0x12, 0x5c, 0xe1, 0xbb,
	0x0b, 0x8b, 0x98, 0x9c, 0x9d, 0x26, 0x7c, 0x4a,
	0xe6, 0x46, 0x36, 0x58, 0x21, 0x4a, 0xee, 0xca,
	0xd7, 0x3b, 0xc2, 0x6c, 0x49, 0x2f, 0xe5, 0xd5,
	0x03, 0x59, 0x84, 0x53, 0xcb, 0xfe, 0x92, 0x71,
	0x2e, 0x7c, 0x21, 0xcc, 0x99, 0x85, 0x7f, 0xb8,
	0x74, 0x90, 0x13, 0x42, 0x3f, 0xe0, 0x6b, 0x1d,
	0xf2, 0x4d, 0x54, 0xd4, 0xfc, 0x3a, 0x05, 0xe6,
	0x74, 0xaf, 0xa6, 0xa0, 0x2a, 0x20, 0x23, 0x5d,
	0x34, 0x5c, 0xd9, 0x3e, 0x4e, 0xfa, 0x93, 0xe7,
	0xaa, 0xe9, 0x6f, 0x08, 0x43, 0x67, 0x41, 0xc5,
	0xad, 0xfb, 0x31, 0x95, 0x82, 0x73, 0x32, 0xd8,
	0xa6, 0xa3, 0xed, 0x0e, 0x2d, 0xf6, 0x5f, 0xfd,
	0x80, 0xa6, 0x7a, 0xe0, 0xdf, 0x78, 0x15, 0x29,
	0x74, 0x33, 0xd0, 0x9e, 0x83, 0x86, 0x72, 0x22,
	0x57, 0x29, 0xb9, 0x9e, 0x5d, 0xd3, 0x1a, 0xb5,
	0x96, 0x72, 0x41, 0x3d, 0xf1, 0x64, 0x43, 0x67,
	0xee, 0xaa, 0x5c, 0xd3, 0x9a, 0x96, 0x13, 0x11,
	0x5d, 0xf3, 0x0c, 0x87, 0x82, 0x1e, 0x41, 0x9e,
	0xd0, 0x27, 0xd7, 0x54, 0x3b, 0x67, 0x73, 0x09,
	0x91, 0xe9, 0xd5, 0x36, 0xa7, 0xb5, 0x55, 0xe4,
	0xf3, 0x21, 0x51, 0x49, 0x22, 0x07, 0x55, 0x4f,
	0x44, 0x4b, 0xd2, 0x15, 0x93, 0x17, 0x2a, 0xfa,
	0x4d, 0x4a, 0x57, 0xdb, 0x4c, 0xa6, 0xeb, 0xec,
	0x53, 0x25, 0x6c, 0x21, 0xed, 0x00, 0x4c, 0x3b,
	0xca, 0x14, 0x57, 0xa9, 0xd6, 0x6a, 0xcd, 0x8d,
	0x5e, 0x74, 0xac, 0x72, 0xc1, 0x97, 0xe5, 0x1b,
	0x45, 0x4e, 0xda, 0xfc, 0xcc, 0x40, 0xe8, 0x48,
	0x88, 0x0b, 0xa3, 0xe3, 0x8d, 0x83, 0x42, 0xc3,
	0x23, 0xfd, 0x68, 0xb5, 0x8e, 0xf1, 0x9d, 0x63,
	0x77, 0xe9, 0xa3, 0x8e, 0x8c, 0x26, 0x6b, 0xbd,
	0x72, 0x73, 0x35, 0x0c, 0x03, 0xf8, 0x43, 0x78,
	0x52, 0x71, 0x15, 0x1f, 0x71, 0x5d, 0x6e, 0xed,
	0xb9, 0xcc, 0x86, 0x30, 0xdb, 0x2b, 0xd3, 0x82,
	0x88, 0x23, 0x71, 0x90, 0x53, 0x5c, 0xa9, 0x2f,
	0x76, 0x01, 0xb7, 0x9a, 0xfe, 0x43, 0x55, 0xa3,
	0x04, 0x9b, 0x0e, 0xe4, 0x59, 0xdf, 0xc9, 0xe9,
	0xb1, 0xea, 0x29, 0x28, 0x3c, 0x5c, 0xae, 0x72,
	0x84, 0xb6, 0xc6, 0xeb, 0x0c, 0x27, 0x07, 0x74,
	0x90, 0x0d, 0x31, 0xb0, 0x00, 0x77, 0xe9, 0x40,
	0x70, 0x6f, 0x68, 0xa7, 0xfd, 0x06, 0xec, 0x4b,
	0xc0, 0xb7, 0xac, 0xbc, 0x33, 0xb7, 0x6d, 0x0a,
	0xbd, 0x12, 0x1b, 0x59, 0xcb, 0xdd, 0x32, 0xf5,
	0x1d, 0x94, 0x57, 0x76, 0x9e, 0x0c, 0x18, 0x98,
	0x71, 0xd7, 0x2a, 0xdb, 0x0b, 0x7b, 0xa7, 0x71,
	0xb7, 0x67, 0x81, 0x23, 0x96, 0xae, 0xb9, 0x7e,
	0x32, 0x43, 0x92, 0x8a, 0x19, 0xa0, 0xc4, 0xd4,
	0x3b, 0x57, 0xf9, 0x4a, 0x2c, 0xfb, 0x51, 0x46,
	0xbb, 0xcb, 0x5d, 0xb3, 0xef, 0x13, 0x93, 0x6e,
	0x68, 0x42, 0x54, 0x57, 0xd3, 0x6a, 0x3a, 0x8f,
	0x9d, 0x66, 0xbf, 0xbd, 0x36, 0x23, 0xf5, 0x93,
	0x83, 0x7b, 0x9c, 0xc0, 0xdd, 0xc5, 0x49, 0xc0,
	0x64, 0xed, 0x07, 0x12, 0xb3, 0xe6, 0xe4, 0xe5,
	0x38, 0x95, 0x23, 0xb1, 0xa0, 0x3b, 0x1a, 0x61,
	0xda, 0x17, 0xac, 0xc3, 0x58, 0xdd, 0x74, 0x64,
	0x22, 0x11, 0xe8, 0x32, 0x1d, 0x16, 0x93, 0x85,
	0x99, 0xa5, 0x9c, 0x34, 0x55, 0xb1, 0xe9, 0x20,
	0x72, 0xc9, 0x28, 0x7b, 0x79, 0x00, 0xa1, 0xa6,
	0xa3, 0x27, 0x40, 0x18, 0x8a, 0x54, 0xe0, 0xcc,
	0xe8, 0x4e, 0x8e, 0x43, 0x96, 0xe7, 0x3f, 0xc8,
	0xe9, 0xb2, 0xf9, 0xc9, 0xda, 0x04, 0x71, 0x50,
	0x47, 0xe4, 0xaa, 0xce, 0xa2, 0x30, 0xc8, 0xe4,
	0xac, 0xc7, 0x0d, 0x06, 0x2e, 0xe6, 0xe8, 0x80,
	0x36, 0x29, 0x9e, 0x01, 0xb8, 0xc3, 0xf0, 0xa0,
	0x5d, 0x7a, 0xca, 0x4d, 0xa0, 0x57, 0xbd, 0x2a,
	0x45, 0xa7, 0x7f, 0x9c, 0x93, 0x07, 0x8f, 0x35,
	0x67, 0x92, 0xe3, 0xe9, 0x7f, 0xa8, 0x61, 0x43,
	0x9e, 0x25, 0x4f, 0x33, 0x76, 0x13, 0x6e, 0x12,
	0xb9, 0xdd, 0xa4, 0x7c, 0x08, 0x9f, 0x7c, 0xe7,
	0x0a, 0x8d, 0x84, 0x06, 0xa4, 0x33, 0x17, 0x34,
	0x5e, 0x10, 0x7c, 0xc0, 0xa8, 0x3d, 0x1f, 0x42,
	0x20, 0x51, 0x65, 0x5d, 0x09, 0xc3, 0xaa, 0xc0,
	0xc8, 0x0d, 0xf0, 0x79, 0xbc, 0x20, 0x1b, 0x95,
	0xe7, 0x06, 0x7d, 0x47, 0x20, 0x03, 0x1a, 0x74,
	0xdd, 0xe2, 0xd4, 0xae, 0x38, 0x71, 0x9b, 0xf5,
	0x80, 0xec, 0x08, 0x4e, 0x56, 0xba, 0x76, 0x12,
	0x1a, 0xdf, 0x48, 0xf3, 0xae, 0xb3, 0xe6, 0xe6,
	0xbe, 0xc0, 0x91, 0x2e, 0x01, 0xb3, 0x01, 0x86,
	0xa2, 0xb9, 0x52, 0xd1, 0x21, 0xae, 0xd4, 0x97,
	0x1d, 0xef, 0x41, 0x12, 0x95, 0x3d, 0x48, 0x45,
	0x1c, 0x56, 0x32, 0x8f, 0xb8, 0x43, 0xbb, 0x19,
	0xf3, 0xca, 0xe9, 0xeb, 0x6d, 0x84, 0xbe, 0x86,
	0x06, 0xe2, 0x36, 0xb2, 0x62, 0x9d, 0xd3, 0x4c,
	0x48, 0x18, 0x54, 0x13, 0x4e, 0xcf, 0xfd, 0xba,
	0x84, 0xb9, 0x30, 0x53, 0xcf, 0xfb, 0xb9, 0x29,
	0x8f, 0xdc, 0x9f, 0xef, 0x60, 0x0b, 0x64, 0xf6,
	0x8b, 0xee, 0xa6, 0x91, 0xc2, 0x41, 0x6c, 0xf6,
	0xfa, 0x79, 0x67, 0x4b, 0xc1, 0x3f, 0xaf, 0x09,
	0x81, 0xd4, 0x5d, 0xcb, 0x09, 0xdf, 0x36, 0x31,
	0xc0, 0x14, 0x3c, 0x7c, 0x0e, 0x65, 0x95, 0x99,
	0x6d, 0xa3, 0xf4, 0xd7, 0x38, 0xee, 0x1a, 0x2b,
	0x37, 0xe2, 0xa4, 0x3b, 0x4b, 0xd0, 0x65, 0xca,
	0xf8, 0xc3, 0xe8, 0x15, 0x20, 0xef, 0xf2, 0x00,
	0xfd, 0x01, 0x09, 0xc5, 0xc8, 0x17, 0x04, 0x93,
	0xd0, 0x93, 0x03, 0x55, 0xc5, 0xfe, 0x32, 0xa3,
	0x3e, 0x28, 0x2d, 0x3b, 0x93, 0x8a, 0xcc, 0x07,
	0x72, 0x80, 0x8b, 0x74, 0x16, 0x24, 0xbb, 0xda,
	0x94, 0x39, 0x30, 0x8f, 0xb1, 0xcd, 0x4a, 0x90,
	0x92, 0x7c, 0x14, 0x8f, 0x95, 0x4e, 0xac, 0x9b,
	0xd8, 0x8f, 0x1a, 0x87, 0xa4, 0x32, 0x27, 0x8a,
	0xba, 0xf7, 0x41, 0xcf, 0x84, 0x37, 0x19, 0xe6,
	0x06, 0xf5, 0x0e, 0xcf, 0x36, 0xf5, 0x9e, 0x6c,
	0xde, 0xbc, 0xff, 0x64, 0x7e, 0x4e, 0x59, 0x57,
	0x48, 0xfe, 0x14, 0xf7, 0x9c, 0x93, 0x5d, 0x15,
	0xad, 0xcc, 0x11, 0xb1, 0x17, 0x18, 0xb2, 0x7e,
	0xcc, 0xab, 0xe9, 0xce, 0x7d, 0x77, 0x5b, 0x51,
	0x1b, 0x1e, 0x20, 0xa8, 0x32, 0x06, 0x0e, 0x75,
	0x93, 0xac, 0xdb, 0x35, 0x37, 0x1f, 0xe9, 0x19,
	0x1d, 0xb4, 0x71, 0x97, 0xd6, 0x4e, 0x2c, 0x08,
	0xa5, 0x13, 0xf9, 0x0e, 0x7e, 0x78, 0x6e, 0x14,
	0xe0, 0xa9, 0xb9, 0x96, 0x4c, 0x80, 0x82, 0xba,
	0x17, 0xb3, 0x9d, 0x69, 0xb0, 0x84, 0x46, 0xff,
	0xf9, 0x52, 0x79, 0x94, 0x58, 0x3a, 0x62, 0x90,
	0x15, 0x35, 0x71, 0x10, 0x37, 0xed, 0xa1, 0x8e,
	0x53, 0x6e, 0xf4, 0x26, 0x57, 0x93, 0x15, 0x93,
	0xf6, 0x81, 0x2c, 0x5a, 0x10, 0xda, 0x92, 0xad,
	0x2f, 0xdb, 0x28, 0x31, 0x2d, 0x55, 0x04, 0xd2,
	0x06, 0x28, 0x8c, 0x1e, 0xdc, 0xea, 0x54, 0xac,
	0xff, 0xb7, 0x6c, 0x30, 0x15, 0xd4, 0xb4, 0x0d,
	0x00, 0x93, 0x57, 0xdd, 0xd2, 0x07, 0x07, 0x06,
	0xd9, 0x43, 0x9b, 0xcd, 0x3a, 0xf4, 0x7d, 0x4c,
	0x36, 0x5d, 0x23, 0xa2, 0xcc, 0x57, 0x40, 0x91,
	0xe9, 0x2c, 0x2f, 0x2c, 0xd5, 0x30, 0x9b, 0x17,
	0xb0, 0xc9, 0xf7, 0xa7, 0x2f, 0xd1, 0x93, 0x20,
	0x6b, 0xc6, 0xc1, 0xe4, 0x6f, 0xcb, 0xd1, 0xe7,
	0x09, 0x0f, 0x9e, 0xdc, 0xaa, 0x9f, 0x2f, 0xdf,
	0x56, 0x9f, 0xd4, 0x33, 0x04, 0xaf, 0xd3, 0x6c,
	0x58, 0x61, 0xf0, 0x30, 0xec, 0xf2, 0x7f, 0xf2,
	0x9c, 0xdf, 0x39, 0xbb, 0x6f, 0xa2, 0x8c, 0x7e,
	0xc4, 0x22, 0x51, 0x71, 0xc0, 0x4d, 0x14, 0x1a,
	0xc4, 0xcd, 0x04, 0xd9, 0x87, 0x08, 0x50, 0x05,
	0xcc, 0xaf, 0xf6, 0xf0, 0x8f, 0x92, 0x54, 0x58,
	0xc2, 0xc7, 0x09, 0x7a, 0x59, 0x02, 0x05, 0xe8,
	0xb0, 0x86, 0xd9, 0xbf, 0x7b, 0x35, 0x51, 0x4d,
	0xaf, 0x08, 0x97, 0x2c, 0x65, 0xda, 0x2a, 0x71,
	0x3a, 0xa8, 0x51, 0xcc, 0xf2, 0x73, 0x27, 0xc3,
	0xfd, 0x62, 0xcf, 0xe3, 0xb2, 0xca, 0xcb, 0xbe,
	0x1a, 0x0a, 0xa1, 0x34, 0x7b, 0x77, 0xc4, 0x62,
	0x68, 0x78, 0x5f, 0x94, 0x07, 0x04, 0x65, 0x16,
	0x4b, 0x61, 0xcb, 0xff, 0x75, 0x26, 0x50, 0x66,
	0x1f, 0x6e, 0x93, 0xf8, 0xc5, 0x51, 0xeb, 0xa4,
	0x4a, 0x48, 0x68, 0x6b, 0xe2, 0x5e, 0x44, 0xb2,
	0x50, 0x2c, 0x6c, 0xae, 0x79, 0x4e, 0x66, 0x35,
	0x81, 0x50, 0xac, 0xbc, 0x3f, 0xb1, 0x0c, 0xf3,
	0x05, 0x3c, 0x4a, 0xa3, 0x6c, 0x2a, 0x79, 0xb4,
	0xb7, 0xab, 0xca, 0xc7, 0x9b, 0x8e, 0xcd, 0x5f,
	0x11, 0x03, 0xcb, 0x30, 0xa3, 0xab, 0xda, 0xfe,
	0x64, 0xb9, 0xbb, 0xd8, 0x5e, 0x3a, 0x1a, 0x56,
	0xe5, 0x05, 0x48, 0x90, 0x1e, 0x61, 0x69, 0x1b,
	0x22, 0xe6, 0x1a, 0x3c, 0x75, 0xad, 0x1f, 0x37,
	0x28, 0xdc, 0xe4, 0x6d, 0xbd, 0x42, 0xdc, 0xd3,
	0xc8, 0xb6, 0x1c, 0x48, 0xfe, 0x94, 0x77, 0x7f,
	0xbd, 0x62, 0xac, 0xa3, 0x47, 0x27, 0xcf, 0x5f,
	0xd9, 0xdb, 0xaf, 0xec, 0xf7, 0x5e, 0xc1, 0xb0,
	0x9d, 0x01, 0x26, 0x99, 0x7e, 0x8f, 0x03, 0x70,
	0xb5, 0x42, 0xbe, 0x67, 0x28, 0x1b, 0x7c, 0xbd,
	0x61, 0x21, 0x97, 0xcc, 0x5c, 0xe1, 0x97, 0x8f,
	0x8d, 0xde, 0x2b, 0xaa, 0xa7, 0x71, 0x1d, 0x1e,
	0x02, 0x73, 0x70, 0x58, 0x32, 0x5b, 0x1d, 0x67,
	0x3d, 0xe0, 0x74, 0x4f, 0x03, 0xf2, 0x70, 0x51,
	0x79, 0xf1, 0x61, 0x70, 0x15, 0x74, 0x9d, 0x23,
	0x89, 0xde, 0xac, 0xfd, 0xde, 0xd0, 0x1f, 0xc3,
	0x87, 0x44, 0x35, 0x4b, 0xe5, 0xb0, 0x60, 0xc5,
	0x22, 0xe4, 0x9e, 0xca, 0xeb, 0xd5, 0x3a, 0x09,
	0x45, 0xa4, 0xdb, 0xfa, 0x3f, 0xeb, 0x1b, 0xc7,
	0xc8, 0x14, 0x99, 0x51, 0x92, 0x10, 0xed, 0xed,
	0x28, 0xe0, 0xa1, 0xf8, 0x26, 0xcf, 0xcd, 0xcb,
	0x63, 0xa1, 0x3b, 0xe3, 0xdf, 0x7e, 0xfe, 0xa6,
	0xf0, 0x81, 0x9a, 0xbf, 0x55, 0xde, 0x54, 0xd5,
	0x56, 0x60, 0x98, 0x10, 0x68, 0xf4, 0x38, 0x96,
	0x8e, 0x6f, 0x1d, 0x44, 0x7f, 0xd6, 0x2f, 0xfe,
	0x55, 0xfb, 0x0c, 0x7e, 0x67, 0xe2, 0x61, 0x44,
	0xed, 0xf2, 0x35, 0x30, 0x5d, 0xe9, 0xc7, 0xd6,
	0x6d, 0xe0, 0xa0, 0xed, 0xf3, 0xfc, 0xd8, 0x3e,
	0x0a, 0x7b, 0xcd, 0xaf, 0x65, 0x68, 0x18, 0xc0,
	0xec, 0x04, 0x1c, 0x74, 0x6d, 0xe2, 0x6e, 0x79,
	0xd4, 0x11, 0x2b, 0x62, 0xd5, 0x27, 0xad, 0x4f,
	0x01, 0x59, 0x73, 0xcc, 0x6a, 0x53, 0xfb, 0x2d,
	0xd5, 0x4e, 0x99, 0x21, 0x65, 0x4d, 0xf5, 0x82,
	0xf7, 0xd8, 0x42, 0xce, 0x6f, 0x3d, 0x36, 0x47,
	0xf1, 0x05, 0x16, 0xe8, 0x1b, 0x6a, 0x8f, 0x93,
	0xf2, 0x8f, 0x37, 0x40, 0x12, 0x28, 0xa3, 0xe6,
	0xb9, 0x17, 0x4a, 0x1f, 0xb1, 0xd1, 0x66, 0x69,
	0x86, 0xc4, 0xfc, 0x97, 0xae, 0x3f, 0x8f, 0x1e,
	0x2b, 0xdf, 0xcd, 0xf9, 0x3c
};
static const u8 enc_assoc011[] __initconst = {
	0xd6, 0x31, 0xda, 0x5d, 0x42, 0x5e, 0xd7
};
static const u8 enc_nonce011[] __initconst = {
	0xfd, 0x87, 0xd4, 0xd8, 0x62, 0xfd, 0xec, 0xaa
};
static const u8 enc_key011[] __initconst = {
	0x35, 0x4e, 0xb5, 0x70, 0x50, 0x42, 0x8a, 0x85,
	0xf2, 0xfb, 0xed, 0x7b, 0xd0, 0x9e, 0x97, 0xca,
	0xfa, 0x98, 0x66, 0x63, 0xee, 0x37, 0xcc, 0x52,
	0xfe, 0xd1, 0xdf, 0x95, 0x15, 0x34, 0x29, 0x38
};

static const u8 enc_input012[] __initconst = {
	0x74, 0xa6, 0x3e, 0xe4, 0xb1, 0xcb, 0xaf, 0xb0,
	0x40, 0xe5, 0x0f, 0x9e, 0xf1, 0xf2, 0x89, 0xb5,
	0x42, 0x34, 0x8a, 0xa1, 0x03, 0xb7, 0xe9, 0x57,
	0x46, 0xbe, 0x20, 0xe4, 0x6e, 0xb0, 0xeb, 0xff,
	0xea, 0x07, 0x7e, 0xef, 0xe2, 0x55, 0x9f, 0xe5,
	0x78, 0x3a, 0xb7, 0x83, 0xc2, 0x18, 0x40, 0x7b,
	0xeb, 0xcd, 0x81, 0xfb, 0x90, 0x12, 0x9e, 0x46,
	0xa9, 0xd6, 0x4a, 0xba, 0xb0, 0x62, 0xdb, 0x6b,
	0x99, 0xc4, 0xdb, 0x54, 0x4b, 0xb8, 0xa5, 0x71,
	0xcb, 0xcd, 0x63, 0x32, 0x55, 0xfb, 0x31, 0xf0,
	0x38, 0xf5, 0xbe, 0x78, 0xe4, 0x45, 0xce, 0x1b,
	0x6a, 0x5b, 0x0e, 0xf4, 0x16, 0xe4, 0xb1, 0x3d,
	0xf6, 0x63, 0x7b, 0xa7, 0x0c, 0xde, 0x6f, 0x8f,
	0x74, 0xdf, 0xe0, 0x1e, 0x9d, 0xce, 0x8f, 0x24,
	0xef, 0x23, 0x35, 0x33, 0x7b, 0x83, 0x34, 0x23,
	0x58, 0x74, 0x14, 0x77, 0x1f, 0xc2, 0x4f, 0x4e,
	0xc6, 0x89, 0xf9, 0x52, 0x09, 0x37, 0x64, 0x14,
	0xc4, 0x01, 0x6b, 0x9d, 0x77, 0xe8, 0x90, 0x5d,
	0xa8, 0x4a, 0x2a, 0xef, 0x5c, 0x7f, 0xeb, 0xbb,
	0xb2, 0xc6, 0x93, 0x99, 0x66, 0xdc, 0x7f, 0xd4,
	0x9e, 0x2a, 0xca, 0x8d, 0xdb, 0xe7, 0x20, 0xcf,
	0xe4, 0x73, 0xae, 0x49, 0x7d, 0x64, 0x0f, 0x0e,
	0x28, 0x46, 0xa9, 0xa8, 0x32, 0xe4, 0x0e, 0xf6,
	0x51, 0x53, 0xb8, 0x3c, 0xb1, 0xff, 0xa3, 0x33,
	0x41, 0x75, 0xff, 0xf1, 0x6f, 0xf1, 0xfb, 0xbb,
	0x83, 0x7f, 0x06, 0x9b, 0xe7, 0x1b, 0x0a, 0xe0,
	0x5c, 0x33, 0x60, 0x5b, 0xdb, 0x5b, 0xed, 0xfe,
	0xa5, 0x16, 0x19, 0x72, 0xa3, 0x64, 0x23, 0x00,
	0x02, 0xc7, 0xf3, 0x6a, 0x81, 0x3e, 0x44, 0x1d,
	0x79, 0x15, 0x5f, 0x9a, 0xde, 0xe2, 0xfd, 0x1b,
	0x73, 0xc1, 0xbc, 0x23, 0xba, 0x31, 0xd2, 0x50,
	0xd5, 0xad, 0x7f, 0x74, 0xa7, 0xc9, 0xf8, 0x3e,
	0x2b, 0x26, 0x10, 0xf6, 0x03, 0x36, 0x74, 0xe4,
	0x0e, 0x6a, 0x72, 0xb7, 0x73, 0x0a, 0x42, 0x28,
	0xc2, 0xad, 0x5e, 0x03, 0xbe, 0xb8, 0x0b, 0xa8,
	0x5b, 0xd4, 0xb8, 0xba, 0x52, 0x89, 0xb1, 0x9b,
	0xc1, 0xc3, 0x65, 0x87, 0xed, 0xa5, 0xf4, 0x86,
	0xfd, 0x41, 0x80, 0x91, 0x27, 0x59, 0x53, 0x67,
	0x15, 0x78, 0x54, 0x8b, 0x2d, 0x3d, 0xc7, 0xff,
	0x02, 0x92, 0x07, 0x5f, 0x7a, 0x4b, 0x60, 0x59,
	0x3c, 0x6f, 0x5c, 0xd8, 0xec, 0x95, 0xd2, 0xfe,
	0xa0, 0x3b, 0xd8, 0x3f, 0xd1, 0x69, 0xa6, 0xd6,
	0x41, 0xb2, 0xf4, 0x4d, 0x12, 0xf4, 0x58, 0x3e,
	0x66, 0x64, 0x80, 0x31, 0x9b, 0xa8, 0x4c, 0x8b,
	0x07, 0xb2, 0xec, 0x66, 0x94, 0x66, 0x47, 0x50,
	0x50, 0x5f, 0x18, 0x0b, 0x0e, 0xd6, 0xc0, 0x39,
	0x21, 0x13, 0x9e, 0x33, 0xbc, 0x79, 0x36, 0x02,
	0x96, 0x70, 0xf0, 0x48, 0x67, 0x2f, 0x26, 0xe9,
	0x6d, 0x10, 0xbb, 0xd6, 0x3f, 0xd1, 0x64, 0x7a,
	0x2e, 0xbe, 0x0c, 0x61, 0xf0, 0x75, 0x42, 0x38,
	0x23, 0xb1, 0x9e, 0x9f, 0x7c, 0x67, 0x66, 0xd9,
	0x58, 0x9a, 0xf1, 0xbb, 0x41, 0x2a, 0x8d, 0x65,
	0x84, 0x94, 0xfc, 0xdc, 0x6a, 0x50, 0x64, 0xdb,
	0x56, 0x33, 0x76, 0x00, 0x10, 0xed, 0xbe, 0xd2,
	0x12, 0xf6, 0xf6, 0x1b, 0xa2, 0x16, 0xde, 0xae,
	0x31, 0x95, 0xdd, 0xb1, 0x08, 0x7e, 0x4e, 0xee,
	0xe7, 0xf9, 0xa5, 0xfb, 0x5b, 0x61, 0x43, 0x00,
	0x40, 0xf6, 0x7e, 0x02, 0x04, 0x32, 0x4e, 0x0c,
	0xe2, 0x66, 0x0d, 0xd7, 0x07, 0x98, 0x0e, 0xf8,
	0x72, 0x34, 0x6d, 0x95, 0x86, 0xd7, 0xcb, 0x31,
	0x54, 0x47, 0xd0, 0x38, 0x29, 0x9c, 0x5a, 0x68,
	0xd4, 0x87, 0x76, 0xc9, 0xe7, 0x7e, 0xe3, 0xf4,
	0x81, 0x6d, 0x18, 0xcb, 0xc9, 0x05, 0xaf, 0xa0,
	0xfb, 0x66, 0xf7, 0xf1, 0x1c, 0xc6, 0x14, 0x11,
	0x4f, 0x2b, 0x79, 0x42, 0x8b, 0xbc, 0xac, 0xe7,
	0x6c, 0xfe, 0x0f, 0x58, 0xe7, 0x7c, 0x78, 0x39,
	0x30, 0xb0, 0x66, 0x2c, 0x9b, 0x6d, 0x3a, 0xe1,
	0xcf, 0xc9, 0xa4, 0x0e, 0x6d, 0x6d, 0x8a, 0xa1,
	0x3a, 0xe7, 0x28, 0xd4, 0x78, 0x4c, 0xa6, 0xa2,
	0x2a, 0xa6, 0x03, 0x30, 0xd7, 0xa8, 0x25, 0x66,
	0x87, 0x2f, 0x69, 0x5c, 0x4e, 0xdd, 0xa5, 0x49,
	0x5d, 0x37, 0x4a, 0x59, 0xc4, 0xaf, 0x1f, 0xa2,
	0xe4, 0xf8, 0xa6, 0x12, 0x97, 0xd5, 0x79, 0xf5,
	0xe2, 0x4a, 0x2b, 0x5f, 0x61, 0xe4, 0x9e, 0xe3,
	0xee, 0xb8, 0xa7, 0x5b, 0x2f, 0xf4, 0x9e, 0x6c,
	0xfb, 0xd1, 0xc6, 0x56, 0x77, 0xba, 0x75, 0xaa,
	0x3d, 0x1a, 0xa8, 0x0b, 0xb3, 0x68, 0x24, 0x00,
	0x10, 0x7f, 0xfd, 0xd7, 0xa1, 0x8d, 0x83, 0x54,
	0x4f, 0x1f, 0xd8, 0x2a, 0xbe, 0x8a, 0x0c, 0x87,
	0xab, 0xa2, 0xde, 0xc3, 0x39, 0xbf, 0x09, 0x03,
	0xa5, 0xf3, 0x05, 0x28, 0xe1, 0xe1, 0xee, 0x39,
	0x70, 0x9c, 0xd8, 0x81, 0x12, 0x1e, 0x02, 0x40,
	0xd2, 0x6e, 0xf0, 0xeb, 0x1b, 0x3d, 0x22, 0xc6,
	0xe5, 0xe3, 0xb4, 0x5a, 0x98, 0xbb, 0xf0, 0x22,
	0x28, 0x8d, 0xe5, 0xd3, 0x16, 0x48, 0x24, 0xa5,
	0xe6, 0x66, 0x0c, 0xf9, 0x08, 0xf9, 0x7e, 0x1e,
	0xe1, 0x28, 0x26, 0x22, 0xc7, 0xc7, 0x0a, 0x32,
	0x47, 0xfa, 0xa3, 0xbe, 0x3c, 0xc4, 0xc5, 0x53,
	0x0a, 0xd5, 0x94, 0x4a, 0xd7, 0x93, 0xd8, 0x42,
	0x99, 0xb9, 0x0a, 0xdb, 0x56, 0xf7, 0xb9, 0x1c,
	0x53, 0x4f, 0xfa, 0xd3, 0x74, 0xad, 0xd9, 0x68,
	0xf1, 0x1b, 0xdf, 0x61, 0xc6, 0x5e, 0xa8, 0x48,
	0xfc, 0xd4, 0x4a, 0x4c, 0x3c, 0x32, 0xf7, 0x1c,
	0x96, 0x21, 0x9b, 0xf9, 0xa3, 0xcc, 0x5a, 0xce,
	0xd5, 0xd7, 0x08, 0x24, 0xf6, 0x1c, 0xfd, 0xdd,
	0x38, 0xc2, 0x32, 0xe9, 0xb8, 0xe7, 0xb6, 0xfa,
	0x9d, 0x45, 0x13, 0x2c, 0x83, 0xfd, 0x4a, 0x69,
	0x82, 0xcd, 0xdc, 0xb3, 0x76, 0x0c, 0x9e, 0xd8,
	0xf4, 0x1b, 0x45, 0x15, 0xb4, 0x97, 0xe7, 0x58,
	0x34, 0xe2, 0x03, 0x29, 0x5a, 0xbf, 0xb6, 0xe0,
	0x5d, 0x13, 0xd9, 0x2b, 0xb4, 0x80, 0xb2, 0x45,
	0x81, 0x6a, 0x2e, 0x6c, 0x89, 0x7d, 0xee, 0xbb,
	0x52, 0xdd, 0x1f, 0x18, 0xe7, 0x13, 0x6b, 0x33,
	0x0e, 0xea, 0x36, 0x92, 0x77, 0x7b, 0x6d, 0x9c,
	0x5a, 0x5f, 0x45, 0x7b, 0x7b, 0x35, 0x62, 0x23,
	0xd1, 0xbf, 0x0f, 0xd0, 0x08, 0x1b, 0x2b, 0x80,
	0x6b, 0x7e, 0xf1, 0x21, 0x47, 0xb0, 0x57, 0xd1,
	0x98, 0x72, 0x90, 0x34, 0x1c, 0x20, 0x04, 0xff,
	0x3d, 0x5c, 0xee, 0x0e, 0x57, 0x5f, 0x6f, 0x24,
	0x4e, 0x3c, 0xea, 0xfc, 0xa5, 0xa9, 0x83, 0xc9,
	0x61, 0xb4, 0x51, 0x24, 0xf8, 0x27, 0x5e, 0x46,
	0x8c, 0xb1, 0x53, 0x02, 0x96, 0x35, 0xba, 0xb8,
	0x4c, 0x71, 0xd3, 0x15, 0x59, 0x35, 0x22, 0x20,
	0xad, 0x03, 0x9f, 0x66, 0x44, 0x3b, 0x9c, 0x35,
	0x37, 0x1f, 0x9b, 0xbb, 0xf3, 0xdb, 0x35, 0x63,
	0x30, 0x64, 0xaa, 0xa2, 0x06, 0xa8, 0x5d, 0xbb,
	0xe1, 0x9f, 0x70, 0xec, 0x82, 0x11, 0x06, 0x36,
	0xec, 0x8b, 0x69, 0x66, 0x24, 0x44, 0xc9, 0x4a,
	0x57, 0xbb, 0x9b, 0x78, 0x13, 0xce, 0x9c, 0x0c,
	0xba, 0x92, 0x93, 0x63, 0xb8, 0xe2, 0x95, 0x0f,
	0x0f, 0x16, 0x39, 0x52, 0xfd, 0x3a, 0x6d, 0x02,
	0x4b, 0xdf, 0x13, 0xd3, 0x2a, 0x22, 0xb4, 0x03,
	0x7c, 0x54, 0x49, 0x96, 0x68, 0x54, 0x10, 0xfa,
	0xef, 0xaa, 0x6c, 0xe8, 0x22, 0xdc, 0x71, 0x16,
	0x13, 0x1a, 0xf6, 0x28, 0xe5, 0x6d, 0x77, 0x3d,
	0xcd, 0x30, 0x63, 0xb1, 0x70, 0x52, 0xa1, 0xc5,
	0x94, 0x5f, 0xcf, 0xe8, 0xb8, 0x26, 0x98, 0xf7,
	0x06, 0xa0, 0x0a, 0x70, 0xfa, 0x03, 0x80, 0xac,
	0xc1, 0xec, 0xd6, 0x4c, 0x54, 0xd7, 0xfe, 0x47,
	0xb6, 0x88, 0x4a, 0xf7, 0x71, 0x24, 0xee, 0xf3,
	0xd2, 0xc2, 0x4a, 0x7f, 0xfe, 0x61, 0xc7, 0x35,
	0xc9, 0x37, 0x67, 0xcb, 0x24, 0x35, 0xda, 0x7e,
	0xca, 0x5f, 0xf3, 0x8d, 0xd4, 0x13, 0x8e, 0xd6,
	0xcb, 0x4d, 0x53, 0x8f, 0x53, 0x1f, 0xc0, 0x74,
	0xf7, 0x53, 0xb9, 0x5e, 0x23, 0x37, 0xba, 0x6e,
	0xe3, 0x9d, 0x07, 0x55, 0x25, 0x7b, 0xe6, 0x2a,
	0x64, 0xd1, 0x32, 0xdd, 0x54, 0x1b, 0x4b, 0xc0,
	0xe1, 0xd7, 0x69, 0x58, 0xf8, 0x93, 0x29, 0xc4,
	0xdd, 0x23, 0x2f, 0xa5, 0xfc, 0x9d, 0x7e, 0xf8,
	0xd4, 0x90, 0xcd, 0x82, 0x55, 0xdc, 0x16, 0x16,
	0x9f, 0x07, 0x52, 0x9b, 0x9d, 0x25, 0xed, 0x32,
	0xc5, 0x7b, 0xdf, 0xf6, 0x83, 0x46, 0x3d, 0x65,
	0xb7, 0xef, 0x87, 0x7a, 0x12, 0x69, 0x8f, 0x06,
	0x7c, 0x51, 0x15, 0x4a, 0x08, 0xe8, 0xac, 0x9a,
	0x0c, 0x24, 0xa7, 0x27, 0xd8, 0x46, 0x2f, 0xe7,
	0x01, 0x0e, 0x1c, 0xc6, 0x91, 0xb0, 0x6e, 0x85,
	0x65, 0xf0, 0x29, 0x0d, 0x2e, 0x6b, 0x3b, 0xfb,
	0x4b, 0xdf, 0xe4, 0x80, 0x93, 0x03, 0x66, 0x46,
	0x3e, 0x8a, 0x6e, 0xf3, 0x5e, 0x4d, 0x62, 0x0e,
	0x49, 0x05, 0xaf, 0xd4, 0xf8, 0x21, 0x20, 0x61,
	0x1d, 0x39, 0x17, 0xf4, 0x61, 0x47, 0x95, 0xfb,
	0x15, 0x2e, 0xb3, 0x4f, 0xd0, 0x5d, 0xf5, 0x7d,
	0x40, 0xda, 0x90, 0x3c, 0x6b, 0xcb, 0x17, 0x00,
	0x13, 0x3b, 0x64, 0x34, 0x1b, 0xf0, 0xf2, 0xe5,
	0x3b, 0xb2, 0xc7, 0xd3, 0x5f, 0x3a, 0x44, 0xa6,
	0x9b, 0xb7, 0x78, 0x0e, 0x42, 0x5d, 0x4c, 0xc1,
	0xe9, 0xd2, 0xcb, 0xb7, 0x78, 0xd1, 0xfe, 0x9a,
	0xb5, 0x07, 0xe9, 0xe0, 0xbe, 0xe2, 0x8a, 0xa7,
	0x01, 0x83, 0x00, 0x8c, 0x5c, 0x08, 0xe6, 0x63,
	0x12, 0x92, 0xb7, 0xb7, 0xa6, 0x19, 0x7d, 0x38,
	0x13, 0x38, 0x92, 0x87, 0x24, 0xf9, 0x48, 0xb3,
	0x5e, 0x87, 0x6a, 0x40, 0x39, 0x5c, 0x3f, 0xed,
	0x8f, 0xee, 0xdb, 0x15, 0x82, 0x06, 0xda, 0x49,
	0x21, 0x2b, 0xb5, 0xbf, 0x32, 0x7c, 0x9f, 0x42,
	0x28, 0x63, 0xcf, 0xaf, 0x1e, 0xf8, 0xc6, 0xa0,
	0xd1, 0x02, 0x43, 0x57, 0x62, 0xec, 0x9b, 0x0f,
	0x01, 0x9e, 0x71, 0xd8, 0x87, 0x9d, 0x01, 0xc1,
	0x58, 0x77, 0xd9, 0xaf, 0xb1, 0x10, 0x7e, 0xdd,
	0xa6, 0x50, 0x96, 0xe5, 0xf0, 0x72, 0x00, 0x6d,
	0x4b, 0xf8, 0x2a, 0x8f, 0x19, 0xf3, 0x22, 0x88,
	0x11, 0x4a, 0x8b, 0x7c, 0xfd, 0xb7, 0xed, 0xe1,
	0xf6, 0x40, 0x39, 0xe0, 0xe9, 0xf6, 0x3d, 0x25,
	0xe6, 0x74, 0x3c, 0x58, 0x57, 0x7f, 0xe1, 0x22,
	0x96, 0x47, 0x31, 0x91, 0xba, 0x70, 0x85, 0x28,
	0x6b, 0x9f, 0x6e, 0x25, 0xac, 0x23, 0x66, 0x2f,
	0x29, 0x88, 0x28, 0xce, 0x8c, 0x5c, 0x88, 0x53,
	0xd1, 0x3b, 0xcc, 0x6a, 0x51, 0xb2, 0xe1, 0x28,
	0x3f, 0x91, 0xb4, 0x0d, 0x00, 0x3a, 0xe3, 0xf8,
	0xc3, 0x8f, 0xd7, 0x96, 0x62, 0x0e, 0x2e, 0xfc,
	0xc8, 0x6c, 0x77, 0xa6, 0x1d, 0x22, 0xc1, 0xb8,
	0xe6, 0x61, 0xd7, 0x67, 0x36, 0x13, 0x7b, 0xbb,
	0x9b, 0x59, 0x09, 0xa6, 0xdf, 0xf7, 0x6b, 0xa3,
	0x40, 0x1a, 0xf5, 0x4f, 0xb4, 0xda, 0xd3, 0xf3,
	0x81, 0x93, 0xc6, 0x18, 0xd9, 0x26, 0xee, 0xac,
	0xf0, 0xaa, 0xdf, 0xc5, 0x9c, 0xca, 0xc2, 0xa2,
	0xcc, 0x7b, 0x5c, 0x24, 0xb0, 0xbc, 0xd0, 0x6a,
	0x4d, 0x89, 0x09, 0xb8, 0x07, 0xfe, 0x87, 0xad,
	0x0a, 0xea, 0xb8, 0x42, 0xf9, 0x5e, 0xb3, 0x3e,
	0x36, 0x4c, 0xaf, 0x75, 0x9e, 0x1c, 0xeb, 0xbd,
	0xbc, 0xbb, 0x80, 0x40, 0xa7, 0x3a, 0x30, 0xbf,
	0xa8, 0x44, 0xf4, 0xeb, 0x38, 0xad, 0x29, 0xba,
	0x23, 0xed, 0x41, 0x0c, 0xea, 0xd2, 0xbb, 0x41,
	0x18, 0xd6, 0xb9, 0xba, 0x65, 0x2b, 0xa3, 0x91,
	0x6d, 0x1f, 0xa9, 0xf4, 0xd1, 0x25, 0x8d, 0x4d,
	0x38, 0xff, 0x64, 0xa0, 0xec, 0xde, 0xa6, 0xb6,
	0x79, 0xab, 0x8e, 0x33, 0x6c, 0x47, 0xde, 0xaf,
	0x94, 0xa4, 0xa5, 0x86, 0x77, 0x55, 0x09, 0x92,
	0x81, 0x31, 0x76, 0xc7, 0x34, 0x22, 0x89, 0x8e,
	0x3d, 0x26, 0x26, 0xd7, 0xfc, 0x1e, 0x16, 0x72,
	0x13, 0x33, 0x63, 0xd5, 0x22, 0xbe, 0xb8, 0x04,
	0x34, 0x84, 0x41, 0xbb, 0x80, 0xd0, 0x9f, 0x46,
	0x48, 0x07, 0xa7, 0xfc, 0x2b, 0x3a, 0x75, 0x55,
	0x8c, 0xc7, 0x6a, 0xbd, 0x7e, 0x46, 0x08, 0x84,
	0x0f, 0xd5, 0x74, 0xc0, 0x82, 0x8e, 0xaa, 0x61,
	0x05, 0x01, 0xb2, 0x47, 0x6e, 0x20, 0x6a, 0x2d,
	0x58, 0x70, 0x48, 0x32, 0xa7, 0x37, 0xd2, 0xb8,
	0x82, 0x1a, 0x51, 0xb9, 0x61, 0xdd, 0xfd, 0x9d,
	0x6b, 0x0e, 0x18, 0x97, 0xf8, 0x45, 0x5f, 0x87,
	0x10, 0xcf, 0x34, 0x72, 0x45, 0x26, 0x49, 0x70,
	0xe7, 0xa3, 0x78, 0xe0, 0x52, 0x89, 0x84, 0x94,
	0x83, 0x82, 0xc2, 0x69, 0x8f, 0xe3, 0xe1, 0x3f,
	0x60, 0x74, 0x88, 0xc4, 0xf7, 0x75, 0x2c, 0xfb,
	0xbd, 0xb6, 0xc4, 0x7e, 0x10, 0x0a, 0x6c, 0x90,
	0x04, 0x9e, 0xc3, 0x3f, 0x59, 0x7c, 0xce, 0x31,
	0x18, 0x60, 0x57, 0x73, 0x46, 0x94, 0x7d, 0x06,
	0xa0, 0x6d, 0x44, 0xec, 0xa2, 0x0a, 0x9e, 0x05,
	0x15, 0xef, 0xca, 0x5c, 0xbf, 0x00, 0xeb, 0xf7,
	0x3d, 0x32, 0xd4, 0xa5, 0xef, 0x49, 0x89, 0x5e,
	0x46, 0xb0, 0xa6, 0x63, 0x5b, 0x8a, 0x73, 0xae,
	0x6f, 0xd5, 0x9d, 0xf8, 0x4f, 0x40, 0xb5, 0xb2,
	0x6e, 0xd3, 0xb6, 0x01, 0xa9, 0x26, 0xa2, 0x21,
	0xcf, 0x33, 0x7a, 0x3a, 0xa4, 0x23, 0x13, 0xb0,
	0x69, 0x6a, 0xee, 0xce, 0xd8, 0x9d, 0x01, 0x1d,
	0x50, 0xc1, 0x30, 0x6c, 0xb1, 0xcd, 0xa0, 0xf0,
	0xf0, 0xa2, 0x64, 0x6f, 0xbb, 0xbf, 0x5e, 0xe6,
	0xab, 0x87, 0xb4, 0x0f, 0x4f, 0x15, 0xaf, 0xb5,
	0x25, 0xa1, 0xb2, 0xd0, 0x80, 0x2c, 0xfb, 0xf9,
	0xfe, 0xd2, 0x33, 0xbb, 0x76, 0xfe, 0x7c, 0xa8,
	0x66, 0xf7, 0xe7, 0x85, 0x9f, 0x1f, 0x85, 0x57,
	0x88, 0xe1, 0xe9, 0x63, 0xe4, 0xd8, 0x1c, 0xa1,
	0xfb, 0xda, 0x44, 0x05, 0x2e, 0x1d, 0x3a, 0x1c,
	0xff, 0xc8, 0x3b, 0xc0, 0xfe, 0xda, 0x22, 0x0b,
	0x43, 0xd6, 0x88, 0x39, 0x4c, 0x4a, 0xa6, 0x69,
	0x18, 0x93, 0x42, 0x4e, 0xb5, 0xcc, 0x66, 0x0d,
	0x09, 0xf8, 0x1e, 0x7c, 0xd3, 0x3c, 0x99, 0x0d,
	0x50, 0x1d, 0x62, 0xe9, 0x57, 0x06, 0xbf, 0x19,
	0x88, 0xdd, 0xad, 0x7b, 0x4f, 0xf9, 0xc7, 0x82,
	0x6d, 0x8d, 0xc8, 0xc4, 0xc5, 0x78, 0x17, 0x20,
	0x15, 0xc5, 0x52, 0x41, 0xcf, 0x5b, 0xd6, 0x7f,
	0x94, 0x02, 0x41, 0xe0, 0x40, 0x22, 0x03, 0x5e,
	0xd1, 0x53, 0xd4, 0x86, 0xd3, 0x2c, 0x9f, 0x0f,
	0x96, 0xe3, 0x6b, 0x9a, 0x76, 0x32, 0x06, 0x47,
	0x4b, 0x11, 0xb3, 0xdd, 0x03, 0x65, 0xbd, 0x9b,
	0x01, 0xda, 0x9c, 0xb9, 0x7e, 0x3f, 0x6a, 0xc4,
	0x7b, 0xea, 0xd4, 0x3c, 0xb9, 0xfb, 0x5c, 0x6b,
	0x64, 0x33, 0x52, 0xba, 0x64, 0x78, 0x8f, 0xa4,
	0xaf, 0x7a, 0x61, 0x8d, 0xbc, 0xc5, 0x73, 0xe9,
	0x6b, 0x58, 0x97, 0x4b, 0xbf, 0x63, 0x22, 0xd3,
	0x37, 0x02, 0x54, 0xc5, 0xb9, 0x16, 0x4a, 0xf0,
	0x19, 0xd8, 0x94, 0x57, 0xb8, 0x8a, 0xb3, 0x16,
	0x3b, 0xd0, 0x84, 0x8e, 0x67, 0xa6, 0xa3, 0x7d,
	0x78, 0xec, 0x00
};
static const u8 enc_output012[] __initconst = {
	0x52, 0x34, 0xb3, 0x65, 0x3b, 0xb7, 0xe5, 0xd3,
	0xab, 0x49, 0x17, 0x60, 0xd2, 0x52, 0x56, 0xdf,
	0xdf, 0x34, 0x56, 0x82, 0xe2, 0xbe, 0xe5, 0xe1,
	0x28, 0xd1, 0x4e, 0x5f, 0x4f, 0x01, 0x7d, 0x3f,
	0x99, 0x6b, 0x30, 0x6e, 0x1a, 0x7c, 0x4c, 0x8e,
	0x62, 0x81, 0xae, 0x86, 0x3f, 0x6b, 0xd0, 0xb5,
	0xa9, 0xcf, 0x50, 0xf1, 0x02, 0x12, 0xa0, 0x0b,
	0x24, 0xe9, 0xe6, 0x72, 0x89, 0x2c, 0x52, 0x1b,
	0x34, 0x38, 0xf8, 0x75, 0x5f, 0xa0, 0x74, 0xe2,
	0x99, 0xdd, 0xa6, 0x4b, 0x14, 0x50, 0x4e, 0xf1,
	0xbe, 0xd6, 0x9e, 0xdb, 0xb2, 0x24, 0x27, 0x74,
	0x12, 0x4a, 0x78, 0x78, 0x17, 0xa5, 0x58, 0x8e,
	0x2f, 0xf9, 0xf4, 0x8d, 0xee, 0x03, 0x88, 0xae,
	0xb8, 0x29, 0xa1, 0x2f, 0x4b, 0xee, 0x92, 0xbd,
	0x87, 0xb3, 0xce, 0x34, 0x21, 0x57, 0x46, 0x04,
	0x49, 0x0c, 0x80, 0xf2, 0x01, 0x13, 0xa1, 0x55,
	0xb3, 0xff, 0x44, 0x30, 0x3c, 0x1c, 0xd0, 0xef,
	0xbc, 0x18, 0x74, 0x26, 0xad, 0x41, 0x5b, 0x5b,
	0x3e, 0x9a, 0x7a, 0x46, 0x4f, 0x16, 0xd6, 0x74,
	0x5a, 0xb7, 0x3a, 0x28, 0x31, 0xd8, 0xae, 0x26,
	0xac, 0x50, 0x53, 0x86, 0xf2, 0x56, 0xd7, 0x3f,
	0x29, 0xbc, 0x45, 0x68, 0x8e, 0xcb, 0x98, 0x64,
	0xdd, 0xc9, 0xba, 0xb8, 0x4b, 0x7b, 0x82, 0xdd,
	0x14, 0xa7, 0xcb, 0x71, 0x72, 0x00, 0x5c, 0xad,
	0x7b, 0x6a, 0x89, 0xa4, 0x3d, 0xbf, 0xb5, 0x4b,
	0x3e, 0x7c, 0x5a, 0xcf, 0xb8, 0xa1, 0xc5, 0x6e,
	0xc8, 0xb6, 0x31, 0x57, 0x7b, 0xdf, 0xa5, 0x7e,
	0xb1, 0xd6, 0x42, 0x2a, 0x31, 0x36, 0xd1, 0xd0,
	0x3f, 0x7a, 0xe5, 0x94, 0xd6, 0x36, 0xa0, 0x6f,
	0xb7, 0x40, 0x7d, 0x37, 0xc6, 0x55, 0x7c, 0x50,
	0x40, 0x6d, 0x29, 0x89, 0xe3, 0x5a, 0xae, 0x97,
	0xe7, 0x44, 0x49, 0x6e, 0xbd, 0x81, 0x3d, 0x03,
	0x93, 0x06, 0x12, 0x06, 0xe2, 0x41, 0x12, 0x4a,
	0xf1, 0x6a, 0xa4, 0x58, 0xa2, 0xfb, 0xd2, 0x15,
	0xba, 0xc9, 0x79, 0xc9, 0xce, 0x5e, 0x13, 0xbb,
	0xf1, 0x09, 0x04, 0xcc, 0xfd, 0xe8, 0x51, 0x34,
	0x6a, 0xe8, 0x61, 0x88, 0xda, 0xed, 0x01, 0x47,
	0x84, 0xf5, 0x73, 0x25, 0xf9, 0x1c, 0x42, 0x86,
	0x07, 0xf3, 0x5b, 0x1a, 0x01, 0xb3, 0xeb, 0x24,
	0x32, 0x8d, 0xf6, 0xed, 0x7c, 0x4b, 0xeb, 0x3c,
	0x36, 0x42, 0x28, 0xdf, 0xdf, 0xb6, 0xbe, 0xd9,
	0x8c, 0x52, 0xd3, 0x2b, 0x08, 0x90, 0x8c, 0xe7,
	0x98, 0x31, 0xe2, 0x32, 0x8e, 0xfc, 0x11, 0x48,
	0x00, 0xa8, 0x6a, 0x42, 0x4a, 0x02, 0xc6, 0x4b,
	0x09, 0xf1, 0xe3, 0x49, 0xf3, 0x45, 0x1f, 0x0e,
	0xbc, 0x56, 0xe2, 0xe4, 0xdf, 0xfb, 0xeb, 0x61,
	0xfa, 0x24, 0xc1, 0x63, 0x75, 0xbb, 0x47, 0x75,
	0xaf, 0xe1, 0x53, 0x16, 0x96, 0x21, 0x85, 0x26,
	0x11, 0xb3, 0x76, 0xe3, 0x23, 0xa1, 0x6b, 0x74,
	0x37, 0xd0, 0xde, 0x06, 0x90, 0x71, 0x5d, 0x43,
	0x88, 0x9b, 0x00, 0x54, 0xa6, 0x75, 0x2f, 0xa1,
	0xc2, 0x0b, 0x73, 0x20, 0x1d, 0xb6, 0x21, 0x79,
	0x57, 0x3f, 0xfa, 0x09, 0xbe, 0x8a, 0x33, 0xc3,
	0x52, 0xf0, 0x1d, 0x82, 0x31, 0xd1, 0x55, 0xb5,
	0x6c, 0x99, 0x25, 0xcf, 0x5c, 0x32, 0xce, 0xe9,
	0x0d, 0xfa, 0x69, 0x2c, 0xd5, 0x0d, 0xc5, 0x6d,
	0x86, 0xd0, 0x0c, 0x3b, 0x06, 0x50, 0x79, 0xe8,
	0xc3, 0xae, 0x04, 0xe6, 0xcd, 0x51, 0xe4, 0x26,
	0x9b, 0x4f, 0x7e, 0xa6, 0x0f, 0xab, 0xd8, 0xe5,
	0xde, 0xa9, 0x00, 0x95, 0xbe, 0xa3, 0x9d, 0x5d,
	0xb2, 0x09, 0x70, 0x18, 0x1c, 0xf0, 0xac, 0x29,
	0x23, 0x02, 0x29, 0x28, 0xd2, 0x74, 0x35, 0x57,
	0x62, 0x0f, 0x24, 0xea, 0x5e, 0x33, 0xc2, 0x92,
	0xf3, 0x78, 0x4d, 0x30, 0x1e, 0xa1, 0x99, 0xa9,
	0x82, 0xb0, 0x42, 0x31, 0x8d, 0xad, 0x8a, 0xbc,
	0xfc, 0xd4, 0x57, 0x47, 0x3e, 0xb4, 0x50, 0xdd,
	0x6e, 0x2c, 0x80, 0x4d, 0x22, 0xf1, 0xfb, 0x57,
	0xc4, 0xdd, 0x17, 0xe1, 0x8a, 0x36, 0x4a, 0xb3,
	0x37, 0xca, 0xc9, 0x4e, 0xab, 0xd5, 0x69, 0xc4,
	0xf4, 0xbc, 0x0b, 0x3b, 0x44, 0x4b, 0x29, 0x9c,
	0xee, 0xd4, 0x35, 0x22, 0x21, 0xb0, 0x1f, 0x27,
	0x64, 0xa8, 0x51, 0x1b, 0xf0, 0x9f, 0x19, 0x5c,
	0xfb, 0x5a, 0x64, 0x74, 0x70, 0x45, 0x09, 0xf5,
	0x64, 0xfe, 0x1a, 0x2d, 0xc9, 0x14, 0x04, 0x14,
	0xcf, 0xd5, 0x7d, 0x60, 0xaf, 0x94, 0x39, 0x94,
	0xe2, 0x7d, 0x79, 0x82, 0xd0, 0x65, 0x3b, 0x6b,
	0x9c, 0x19, 0x84, 0xb4, 0x6d, 0xb3, 0x0c, 0x99,
	0xc0, 0x56, 0xa8, 0xbd, 0x73, 0xce, 0x05, 0x84,
	0x3e, 0x30, 0xaa, 0xc4, 0x9b, 0x1b, 0x04, 0x2a,
	0x9f, 0xd7, 0x43, 0x2b, 0x23, 0xdf, 0xbf, 0xaa,
	0xd5, 0xc2, 0x43, 0x2d, 0x70, 0xab, 0xdc, 0x75,
	0xad, 0xac, 0xf7, 0xc0, 0xbe, 0x67, 0xb2, 0x74,
	0xed, 0x67, 0x10, 0x4a, 0x92, 0x60, 0xc1, 0x40,
	0x50, 0x19, 0x8a, 0x8a, 0x8c, 0x09, 0x0e, 0x72,
	0xe1, 0x73, 0x5e, 0xe8, 0x41, 0x85, 0x63, 0x9f,
	0x3f, 0xd7, 0x7d, 0xc4, 0xfb, 0x22, 0x5d, 0x92,
	0x6c, 0xb3, 0x1e, 0xe2, 0x50, 0x2f, 0x82, 0xa8,
	0x28, 0xc0, 0xb5, 0xd7, 0x5f, 0x68, 0x0d, 0x2c,
	0x2d, 0xaf, 0x7e, 0xfa, 0x2e, 0x08, 0x0f, 0x1f,
	0x70, 0x9f, 0xe9, 0x19, 0x72, 0x55, 0xf8, 0xfb,
	0x51, 0xd2, 0x33, 0x5d, 0xa0, 0xd3, 0x2b, 0x0a,
	0x6c, 0xbc, 0x4e, 0xcf, 0x36, 0x4d, 0xdc, 0x3b,
	0xe9, 0x3e, 0x81, 0x7c, 0x61, 0xdb, 0x20, 0x2d,
	0x3a, 0xc3, 0xb3, 0x0c, 0x1e, 0x00, 0xb9, 0x7c,
	0xf5, 0xca, 0x10, 0x5f, 0x3a, 0x71, 0xb3, 0xe4,
	0x20, 0xdb, 0x0c, 0x2a, 0x98, 0x63, 0x45, 0x00,
	0x58, 0xf6, 0x68, 0xe4, 0x0b, 0xda, 0x13, 0x3b,
	0x60, 0x5c, 0x76, 0xdb, 0xb9, 0x97, 0x71, 0xe4,
	0xd9, 0xb7, 0xdb, 0xbd, 0x68, 0xc7, 0x84, 0x84,
	0xaa, 0x7c, 0x68, 0x62, 0x5e, 0x16, 0xfc, 0xba,
	0x72, 0xaa, 0x9a, 0xa9, 0xeb, 0x7c, 0x75, 0x47,
	0x97, 0x7e, 0xad, 0xe2, 0xd9, 0x91, 0xe8, 0xe4,
	0xa5, 0x31, 0xd7, 0x01, 0x8e, 0xa2, 0x11, 0x88,
	0x95, 0xb9, 0xf2, 0x9b, 0xd3, 0x7f, 0x1b, 0x81,
	0x22, 0xf7, 0x98, 0x60, 0x0a, 0x64, 0xa6, 0xc1,
	0xf6, 0x49, 0xc7, 0xe3, 0x07, 0x4d, 0x94, 0x7a,
	0xcf, 0x6e, 0x68, 0x0c, 0x1b, 0x3f, 0x6e, 0x2e,
	0xee, 0x92, 0xfa, 0x52, 0xb3, 0x59, 0xf8, 0xf1,
	0x8f, 0x6a, 0x66, 0xa3, 0x82, 0x76, 0x4a, 0x07,
	0x1a, 0xc7, 0xdd, 0xf5, 0xda, 0x9c, 0x3c, 0x24,
	0xbf, 0xfd, 0x42, 0xa1, 0x10, 0x64, 0x6a, 0x0f,
	0x89, 0xee, 0x36, 0xa5, 0xce, 0x99, 0x48, 0x6a,
	0xf0, 0x9f, 0x9e, 0x69, 0xa4, 0x40, 0x20, 0xe9,
	0x16, 0x15, 0xf7, 0xdb, 0x75, 0x02, 0xcb, 0xe9,
	0x73, 0x8b, 0x3b, 0x49, 0x2f, 0xf0, 0xaf, 0x51,
	0x06, 0x5c, 0xdf, 0x27, 0x27, 0x49, 0x6a, 0xd1,
	0xcc, 0xc7, 0xb5, 0x63, 0xb5, 0xfc, 0xb8, 0x5c,
	0x87, 0x7f, 0x84, 0xb4, 0xcc, 0x14, 0xa9, 0x53,
	0xda, 0xa4, 0x56, 0xf8, 0xb6, 0x1b, 0xcc, 0x40,
	0x27, 0x52, 0x06, 0x5a, 0x13, 0x81, 0xd7, 0x3a,
	0xd4, 0x3b, 0xfb, 0x49, 0x65, 0x31, 0x33, 0xb2,
	0xfa, 0xcd, 0xad, 0x58, 0x4e, 0x2b, 0xae, 0xd2,
	0x20, 0xfb, 0x1a, 0x48, 0xb4, 0x3f, 0x9a, 0xd8,
	0x7a, 0x35, 0x4a, 0xc8, 0xee, 0x88, 0x5e, 0x07,
	0x66, 0x54, 0xb9, 0xec, 0x9f, 0xa3, 0xe3, 0xb9,
	0x37, 0xaa, 0x49, 0x76, 0x31, 0xda, 0x74, 0x2d,
	0x3c, 0xa4, 0x65, 0x10, 0x32, 0x38, 0xf0, 0xde,
	0xd3, 0x99, 0x17, 0xaa, 0x71, 0xaa, 0x8f, 0x0f,
	0x8c, 0xaf, 0xa2, 0xf8, 0x5d, 0x64, 0xba, 0x1d,
	0xa3, 0xef, 0x96, 0x73, 0xe8, 0xa1, 0x02, 0x8d,
	0x0c, 0x6d, 0xb8, 0x06, 0x90, 0xb8, 0x08, 0x56,
	0x2c, 0xa7, 0x06, 0xc9, 0xc2, 0x38, 0xdb, 0x7c,
	0x63, 0xb1, 0x57, 0x8e, 0xea, 0x7c, 0x79, 0xf3,
	0x49, 0x1d, 0xfe, 0x9f, 0xf3, 0x6e, 0xb1, 0x1d,
	0xba, 0x19, 0x80, 0x1a, 0x0a, 0xd3, 0xb0, 0x26,
	0x21, 0x40, 0xb1, 0x7c, 0xf9, 0x4d, 0x8d, 0x10,
	0xc1, 0x7e, 0xf4, 0xf6, 0x3c, 0xa8, 0xfd, 0x7c,
	0xa3, 0x92, 0xb2, 0x0f, 0xaa, 0xcc, 0xa6, 0x11,
	0xfe, 0x04, 0xe3, 0xd1, 0x7a, 0x32, 0x89, 0xdf,
	0x0d, 0xc4, 0x8f, 0x79, 0x6b, 0xca, 0x16, 0x7c,
	0x6e, 0xf9, 0xad, 0x0f, 0xf6, 0xfe, 0x27, 0xdb,
	0xc4, 0x13, 0x70, 0xf1, 0x62, 0x1a, 0x4f, 0x79,
	0x40, 0xc9, 0x9b, 0x8b, 0x21, 0xea, 0x84, 0xfa,
	0xf5, 0xf1, 0x89, 0xce, 0xb7, 0x55, 0x0a, 0x80,
	0x39, 0x2f, 0x55, 0x36, 0x16, 0x9c, 0x7b, 0x08,
	0xbd, 0x87, 0x0d, 0xa5, 0x32, 0xf1, 0x52, 0x7c,
	0xe8, 0x55, 0x60, 0x5b, 0xd7, 0x69, 0xe4, 0xfc,
	0xfa, 0x12, 0x85, 0x96, 0xea, 0x50, 0x28, 0xab,
	0x8a, 0xf7, 0xbb, 0x0e, 0x53, 0x74, 0xca, 0xa6,
	0x27, 0x09, 0xc2, 0xb5, 0xde, 0x18, 0x14, 0xd9,
	0xea, 0xe5, 0x29, 0x1c, 0x40, 0x56, 0xcf, 0xd7,
	0xae, 0x05, 0x3f, 0x65, 0xaf, 0x05, 0x73, 0xe2,
	0x35, 0x96, 0x27, 0x07, 0x14, 0xc0, 0xad, 0x33,
	0xf1, 0xdc, 0x44, 0x7a, 0x89, 0x17, 0x77, 0xd2,
	0x9c, 0x58, 0x60, 0xf0, 0x3f, 0x7b, 0x2d, 0x2e,
	0x57, 0x95, 0x54, 0x87, 0xed, 0xf2, 0xc7, 0x4c,
	0xf0, 0xae, 0x56, 0x29, 0x19, 0x7d, 0x66, 0x4b,
	0x9b, 0x83, 0x84, 0x42, 0x3b, 0x01, 0x25, 0x66,
	0x8e, 0x02, 0xde, 0xb9, 0x83, 0x54, 0x19, 0xf6,
	0x9f, 0x79, 0x0d, 0x67, 0xc5, 0x1d, 0x7a, 0x44,
	0x02, 0x98, 0xa7, 0x16, 0x1c, 0x29, 0x0d, 0x74,
	0xff, 0x85, 0x40, 0x06, 0xef, 0x2c, 0xa9, 0xc6,
	0xf5, 0x53, 0x07, 0x06, 0xae, 0xe4, 0xfa, 0x5f,
	0xd8, 0x39, 0x4d, 0xf1, 0x9b, 0x6b, 0xd9, 0x24,
	0x84, 0xfe, 0x03, 0x4c, 0xb2, 0x3f, 0xdf, 0xa1,
	0x05, 0x9e, 0x50, 0x14, 0x5a, 0xd9, 0x1a, 0xa2,
	0xa7, 0xfa, 0xfa, 0x17, 0xf7, 0x78, 0xd6, 0xb5,
	0x92, 0x61, 0x91, 0xac, 0x36, 0xfa, 0x56, 0x0d,
	0x38, 0x32, 0x18, 0x85, 0x08, 0x58, 0x37, 0xf0,
	0x4b, 0xdb, 0x59, 0xe7, 0xa4, 0x34, 0xc0, 0x1b,
	0x01, 0xaf, 0x2d, 0xde, 0xa1, 0xaa, 0x5d, 0xd3,
	0xec, 0xe1, 0xd4, 0xf7, 0xe6, 0x54, 0x68, 0xf0,
	0x51, 0x97, 0xa7, 0x89, 0xea, 0x24, 0xad, 0xd3,
	0x6e, 0x47, 0x93, 0x8b, 0x4b, 0xb4, 0xf7, 0x1c,
	0x42, 0x06, 0x67, 0xe8, 0x99, 0xf6, 0xf5, 0x7b,
	0x85, 0xb5, 0x65, 0xb5, 0xb5, 0xd2, 0x37, 0xf5,
	0xf3, 0x02, 0xa6, 0x4d, 0x11, 0xa7, 0xdc, 0x51,
	0x09, 0x7f, 0xa0, 0xd8, 0x88, 0x1c, 0x13, 0x71,
	0xae, 0x9c, 0xb7, 0x7b, 0x34, 0xd6, 0x4e, 0x68,
	0x26, 0x83, 0x51, 0xaf, 0x1d, 0xee, 0x8b, 0xbb,
	0x69, 0x43, 0x2b, 0x9e, 0x8a, 0xbc, 0x02, 0x0e,
	0xa0, 0x1b, 0xe0, 0xa8, 0x5f, 0x6f, 0xaf, 0x1b,
	0x8f, 0xe7, 0x64, 0x71, 0x74, 0x11, 0x7e, 0xa8,
	0xd8, 0xf9, 0x97, 0x06, 0xc3, 0xb6, 0xfb, 0xfb,
	0xb7, 0x3d, 0x35, 0x9d, 0x3b, 0x52, 0xed, 0x54,
	0xca, 0xf4, 0x81, 0x01, 0x2d, 0x1b, 0xc3, 0xa7,
	0x00, 0x3d, 0x1a, 0x39, 0x54, 0xe1, 0xf6, 0xff,
	0xed, 0x6f, 0x0b, 0x5a, 0x68, 0xda, 0x58, 0xdd,
	0xa9, 0xcf, 0x5c, 0x4a, 0xe5, 0x09, 0x4e, 0xde,
	0x9d, 0xbc, 0x3e, 0xee, 0x5a, 0x00, 0x3b, 0x2c,
	0x87, 0x10, 0x65, 0x60, 0xdd, 0xd7, 0x56, 0xd1,
	0x4c, 0x64, 0x45, 0xe4, 0x21, 0xec, 0x78, 0xf8,
	0x25, 0x7a, 0x3e, 0x16, 0x5d, 0x09, 0x53, 0x14,
	0xbe, 0x4f, 0xae, 0x87, 0xd8, 0xd1, 0xaa, 0x3c,
	0xf6, 0x3e, 0xa4, 0x70, 0x8c, 0x5e, 0x70, 0xa4,
	0xb3, 0x6b, 0x66, 0x73, 0xd3, 0xbf, 0x31, 0x06,
	0x19, 0x62, 0x93, 0x15, 0xf2, 0x86, 0xe4, 0x52,
	0x7e, 0x53, 0x4c, 0x12, 0x38, 0xcc, 0x34, 0x7d,
	0x57, 0xf6, 0x42, 0x93, 0x8a, 0xc4, 0xee, 0x5c,
	0x8a, 0xe1, 0x52, 0x8f, 0x56, 0x64, 0xf6, 0xa6,
	0xd1, 0x91, 0x57, 0x70, 0xcd, 0x11, 0x76, 0xf5,
	0x59, 0x60, 0x60, 0x3c, 0xc1, 0xc3, 0x0b, 0x7f,
	0x58, 0x1a, 0x50, 0x91, 0xf1, 0x68, 0x8f, 0x6e,
	0x74, 0x74, 0xa8, 0x51, 0x0b, 0xf7, 0x7a, 0x98,
	0x37, 0xf2, 0x0a, 0x0e, 0xa4, 0x97, 0x04, 0xb8,
	0x9b, 0xfd, 0xa0, 0xea, 0xf7, 0x0d, 0xe1, 0xdb,
	0x03, 0xf0, 0x31, 0x29, 0xf8, 0xdd, 0x6b, 0x8b,
	0x5d, 0xd8, 0x59, 0xa9, 0x29, 0xcf, 0x9a, 0x79,
	0x89, 0x19, 0x63, 0x46, 0x09, 0x79, 0x6a, 0x11,
	0xda, 0x63, 0x68, 0x48, 0x77, 0x23, 0xfb, 0x7d,
	0x3a, 0x43, 0xcb, 0x02, 0x3b, 0x7a, 0x6d, 0x10,
	0x2a, 0x9e, 0xac, 0xf1, 0xd4, 0x19, 0xf8, 0x23,
	0x64, 0x1d, 0x2c, 0x5f, 0xf2, 0xb0, 0x5c, 0x23,
	0x27, 0xf7, 0x27, 0x30, 0x16, 0x37, 0xb1, 0x90,
	0xab, 0x38, 0xfb, 0x55, 0xcd, 0x78, 0x58, 0xd4,
	0x7d, 0x43, 0xf6, 0x45, 0x5e, 0x55, 0x8d, 0xb1,
	0x02, 0x65, 0x58, 0xb4, 0x13, 0x4b, 0x36, 0xf7,
	0xcc, 0xfe, 0x3d, 0x0b, 0x82, 0xe2, 0x12, 0x11,
	0xbb, 0xe6, 0xb8, 0x3a, 0x48, 0x71, 0xc7, 0x50,
	0x06, 0x16, 0x3a, 0xe6, 0x7c, 0x05, 0xc7, 0xc8,
	0x4d, 0x2f, 0x08, 0x6a, 0x17, 0x9a, 0x95, 0x97,
	0x50, 0x68, 0xdc, 0x28, 0x18, 0xc4, 0x61, 0x38,
	0xb9, 0xe0, 0x3e, 0x78, 0xdb, 0x29, 0xe0, 0x9f,
	0x52, 0xdd, 0xf8, 0x4f, 0x91, 0xc1, 0xd0, 0x33,
	0xa1, 0x7a, 0x8e, 0x30, 0x13, 0x82, 0x07, 0x9f,
	0xd3, 0x31, 0x0f, 0x23, 0xbe, 0x32, 0x5a, 0x75,
	0xcf, 0x96, 0xb2, 0xec, 0xb5, 0x32, 0xac, 0x21,
	0xd1, 0x82, 0x33, 0xd3, 0x15, 0x74, 0xbd, 0x90,
	0xf1, 0x2c, 0xe6, 0x5f, 0x8d, 0xe3, 0x02, 0xe8,
	0xe9, 0xc4, 0xca, 0x96, 0xeb, 0x0e, 0xbc, 0x91,
	0xf4, 0xb9, 0xea, 0xd9, 0x1b, 0x75, 0xbd, 0xe1,
	0xac, 0x2a, 0x05, 0x37, 0x52, 0x9b, 0x1b, 0x3f,
	0x5a, 0xdc, 0x21, 0xc3, 0x98, 0xbb, 0xaf, 0xa3,
	0xf2, 0x00, 0xbf, 0x0d, 0x30, 0x89, 0x05, 0xcc,
	0xa5, 0x76, 0xf5, 0x06, 0xf0, 0xc6, 0x54, 0x8a,
	0x5d, 0xd4, 0x1e, 0xc1, 0xf2, 0xce, 0xb0, 0x62,
	0xc8, 0xfc, 0x59, 0x42, 0x9a, 0x90, 0x60, 0x55,
	0xfe, 0x88, 0xa5, 0x8b, 0xb8, 0x33, 0x0c, 0x23,
	0x24, 0x0d, 0x15, 0x70, 0x37, 0x1e, 0x3d, 0xf6,
	0xd2, 0xea, 0x92, 0x10, 0xb2, 0xc4, 0x51, 0xac,
	0xf2, 0xac, 0xf3, 0x6b, 0x6c, 0xaa, 0xcf, 0x12,
	0xc5, 0x6c, 0x90, 0x50, 0xb5, 0x0c, 0xfc, 0x1a,
	0x15, 0x52, 0xe9, 0x26, 0xc6, 0x52, 0xa4, 0xe7,
	0x81, 0x69, 0xe1, 0xe7, 0x9e, 0x30, 0x01, 0xec,
	0x84, 0x89, 0xb2, 0x0d, 0x66, 0xdd, 0xce, 0x28,
	0x5c, 0xec, 0x98, 0x46, 0x68, 0x21, 0x9f, 0x88,
	0x3f, 0x1f, 0x42, 0x77, 0xce, 0xd0, 0x61, 0xd4,
	0x20, 0xa7, 0xff, 0x53, 0xad, 0x37, 0xd0, 0x17,
	0x35, 0xc9, 0xfc, 0xba, 0x0a, 0x78, 0x3f, 0xf2,
	0xcc, 0x86, 0x89, 0xe8, 0x4b, 0x3c, 0x48, 0x33,
	0x09, 0x7f, 0xc6, 0xc0, 0xdd, 0xb8, 0xfd, 0x7a,
	0x66, 0x66, 0x65, 0xeb, 0x47, 0xa7, 0x04, 0x28,
	0xa3, 0x19, 0x8e, 0xa9, 0xb1, 0x13, 0x67, 0x62,
	0x70, 0xcf, 0xd6
};
static const u8 enc_assoc012[] __initconst = {
	0xb1, 0x69, 0x83, 0x87, 0x30, 0xaa, 0x5d, 0xb8,
	0x77, 0xe8, 0x21, 0xff, 0x06, 0x59, 0x35, 0xce,
	0x75, 0xfe, 0x38, 0xef, 0xb8, 0x91, 0x43, 0x8c,
	0xcf, 0x70, 0xdd, 0x0a, 0x68, 0xbf, 0xd4, 0xbc,
	0x16, 0x76, 0x99, 0x36, 0x1e, 0x58, 0x79, 0x5e,
	0xd4, 0x29, 0xf7, 0x33, 0x93, 0x48, 0xdb, 0x5f,
	0x01, 0xae, 0x9c, 0xb6, 0xe4, 0x88, 0x6d, 0x2b,
	0x76, 0x75, 0xe0, 0xf3, 0x74, 0xe2, 0xc9
};
static const u8 enc_nonce012[] __initconst = {
	0x05, 0xa3, 0x93, 0xed, 0x30, 0xc5, 0xa2, 0x06
};
static const u8 enc_key012[] __initconst = {
	0xb3, 0x35, 0x50, 0x03, 0x54, 0x2e, 0x40, 0x5e,
	0x8f, 0x59, 0x8e, 0xc5, 0x90, 0xd5, 0x27, 0x2d,
	0xba, 0x29, 0x2e, 0xcb, 0x1b, 0x70, 0x44, 0x1e,
	0x65, 0x91, 0x6e, 0x2a, 0x79, 0x22, 0xda, 0x64
};

/* wycheproof - rfc7539 */
static const u8 enc_input013[] __initconst = {
	0x4c, 0x61, 0x64, 0x69, 0x65, 0x73, 0x20, 0x61,
	0x6e, 0x64, 0x20, 0x47, 0x65, 0x6e, 0x74, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x20, 0x6f, 0x66, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x20, 0x6f, 0x66, 0x20, 0x27, 0x39, 0x39,
	0x3a, 0x20, 0x49, 0x66, 0x20, 0x49, 0x20, 0x63,
	0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x20, 0x79, 0x6f, 0x75, 0x20, 0x6f,
	0x6e, 0x6c, 0x79, 0x20, 0x6f, 0x6e, 0x65, 0x20,
	0x74, 0x69, 0x70, 0x20, 0x66, 0x6f, 0x72, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x66, 0x75, 0x74, 0x75,
	0x72, 0x65, 0x2c, 0x20, 0x73, 0x75, 0x6e, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x20, 0x77, 0x6f,
	0x75, 0x6c, 0x64, 0x20, 0x62, 0x65, 0x20, 0x69,
	0x74, 0x2e
};
static const u8 enc_output013[] __initconst = {
	0xd3, 0x1a, 0x8d, 0x34, 0x64, 0x8e, 0x60, 0xdb,
	0x7b, 0x86, 0xaf, 0xbc, 0x53, 0xef, 0x7e, 0xc2,
	0xa4, 0xad, 0xed, 0x51, 0x29, 0x6e, 0x08, 0xfe,
	0xa9, 0xe2, 0xb5, 0xa7, 0x36, 0xee, 0x62, 0xd6,
	0x3d, 0xbe, 0xa4, 0x5e, 0x8c, 0xa9, 0x67, 0x12,
	0x82, 0xfa, 0xfb, 0x69, 0xda, 0x92, 0x72, 0x8b,
	0x1a, 0x71, 0xde, 0x0a, 0x9e, 0x06, 0x0b, 0x29,
	0x05, 0xd6, 0xa5, 0xb6, 0x7e, 0xcd, 0x3b, 0x36,
	0x92, 0xdd, 0xbd, 0x7f, 0x2d, 0x77, 0x8b, 0x8c,
	0x98, 0x03, 0xae, 0xe3, 0x28, 0x09, 0x1b, 0x58,
	0xfa, 0xb3, 0x24, 0xe4, 0xfa, 0xd6, 0x75, 0x94,
	0x55, 0x85, 0x80, 0x8b, 0x48, 0x31, 0xd7, 0xbc,
	0x3f, 0xf4, 0xde, 0xf0, 0x8e, 0x4b, 0x7a, 0x9d,
	0xe5, 0x76, 0xd2, 0x65, 0x86, 0xce, 0xc6, 0x4b,
	0x61, 0x16, 0x1a, 0xe1, 0x0b, 0x59, 0x4f, 0x09,
	0xe2, 0x6a, 0x7e, 0x90, 0x2e, 0xcb, 0xd0, 0x60,
	0x06, 0x91
};
static const u8 enc_assoc013[] __initconst = {
	0x50, 0x51, 0x52, 0x53, 0xc0, 0xc1, 0xc2, 0xc3,
	0xc4, 0xc5, 0xc6, 0xc7
};
static const u8 enc_nonce013[] __initconst = {
	0x07, 0x00, 0x00, 0x00, 0x40, 0x41, 0x42, 0x43,
	0x44, 0x45, 0x46, 0x47
};
static const u8 enc_key013[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input014[] __initconst = { };
static const u8 enc_output014[] __initconst = {
	0x76, 0xac, 0xb3, 0x42, 0xcf, 0x31, 0x66, 0xa5,
	0xb6, 0x3c, 0x0c, 0x0e, 0xa1, 0x38, 0x3c, 0x8d
};
static const u8 enc_assoc014[] __initconst = { };
static const u8 enc_nonce014[] __initconst = {
	0x4d, 0xa5, 0xbf, 0x8d, 0xfd, 0x58, 0x52, 0xc1,
	0xea, 0x12, 0x37, 0x9d
};
static const u8 enc_key014[] __initconst = {
	0x80, 0xba, 0x31, 0x92, 0xc8, 0x03, 0xce, 0x96,
	0x5e, 0xa3, 0x71, 0xd5, 0xff, 0x07, 0x3c, 0xf0,
	0xf4, 0x3b, 0x6a, 0x2a, 0xb5, 0x76, 0xb2, 0x08,
	0x42, 0x6e, 0x11, 0x40, 0x9c, 0x09, 0xb9, 0xb0
};

/* wycheproof - misc */
static const u8 enc_input015[] __initconst = { };
static const u8 enc_output015[] __initconst = {
	0x90, 0x6f, 0xa6, 0x28, 0x4b, 0x52, 0xf8, 0x7b,
	0x73, 0x59, 0xcb, 0xaa, 0x75, 0x63, 0xc7, 0x09
};
static const u8 enc_assoc015[] __initconst = {
	0xbd, 0x50, 0x67, 0x64, 0xf2, 0xd2, 0xc4, 0x10
};
static const u8 enc_nonce015[] __initconst = {
	0xa9, 0x2e, 0xf0, 0xac, 0x99, 0x1d, 0xd5, 0x16,
	0xa3, 0xc6, 0xf6, 0x89
};
static const u8 enc_key015[] __initconst = {
	0x7a, 0x4c, 0xd7, 0x59, 0x17, 0x2e, 0x02, 0xeb,
	0x20, 0x4d, 0xb2, 0xc3, 0xf5, 0xc7, 0x46, 0x22,
	0x7d, 0xf5, 0x84, 0xfc, 0x13, 0x45, 0x19, 0x63,
	0x91, 0xdb, 0xb9, 0x57, 0x7a, 0x25, 0x07, 0x42
};

/* wycheproof - misc */
static const u8 enc_input016[] __initconst = {
	0x2a
};
static const u8 enc_output016[] __initconst = {
	0x3a, 0xca, 0xc2, 0x7d, 0xec, 0x09, 0x68, 0x80,
	0x1e, 0x9f, 0x6e, 0xde, 0xd6, 0x9d, 0x80, 0x75,
	0x22
};
static const u8 enc_assoc016[] __initconst = { };
static const u8 enc_nonce016[] __initconst = {
	0x99, 0xe2, 0x3e, 0xc4, 0x89, 0x85, 0xbc, 0xcd,
	0xee, 0xab, 0x60, 0xf1
};
static const u8 enc_key016[] __initconst = {
	0xcc, 0x56, 0xb6, 0x80, 0x55, 0x2e, 0xb7, 0x50,
	0x08, 0xf5, 0x48, 0x4b, 0x4c, 0xb8, 0x03, 0xfa,
	0x50, 0x63, 0xeb, 0xd6, 0xea, 0xb9, 0x1f, 0x6a,
	0xb6, 0xae, 0xf4, 0x91, 0x6a, 0x76, 0x62, 0x73
};

/* wycheproof - misc */
static const u8 enc_input017[] __initconst = {
	0x51
};
static const u8 enc_output017[] __initconst = {
	0xc4, 0x16, 0x83, 0x10, 0xca, 0x45, 0xb1, 0xf7,
	0xc6, 0x6c, 0xad, 0x4e, 0x99, 0xe4, 0x3f, 0x72,
	0xb9
};
static const u8 enc_assoc017[] __initconst = {
	0x91, 0xca, 0x6c, 0x59, 0x2c, 0xbc, 0xca, 0x53
};
static const u8 enc_nonce017[] __initconst = {
	0xab, 0x0d, 0xca, 0x71, 0x6e, 0xe0, 0x51, 0xd2,
	0x78, 0x2f, 0x44, 0x03
};
static const u8 enc_key017[] __initconst = {
	0x46, 0xf0, 0x25, 0x49, 0x65, 0xf7, 0x69, 0xd5,
	0x2b, 0xdb, 0x4a, 0x70, 0xb4, 0x43, 0x19, 0x9f,
	0x8e, 0xf2, 0x07, 0x52, 0x0d, 0x12, 0x20, 0xc5,
	0x5e, 0x4b, 0x70, 0xf0, 0xfd, 0xa6, 0x20, 0xee
};

/* wycheproof - misc */
static const u8 enc_input018[] __initconst = {
	0x5c, 0x60
};
static const u8 enc_output018[] __initconst = {
	0x4d, 0x13, 0x91, 0xe8, 0xb6, 0x1e, 0xfb, 0x39,
	0xc1, 0x22, 0x19, 0x54, 0x53, 0x07, 0x7b, 0x22,
	0xe5, 0xe2
};
static const u8 enc_assoc018[] __initconst = { };
static const u8 enc_nonce018[] __initconst = {
	0x46, 0x1a, 0xf1, 0x22, 0xe9, 0xf2, 0xe0, 0x34,
	0x7e, 0x03, 0xf2, 0xdb
};
static const u8 enc_key018[] __initconst = {
	0x2f, 0x7f, 0x7e, 0x4f, 0x59, 0x2b, 0xb3, 0x89,
	0x19, 0x49, 0x89, 0x74, 0x35, 0x07, 0xbf, 0x3e,
	0xe9, 0xcb, 0xde, 0x17, 0x86, 0xb6, 0x69, 0x5f,
	0xe6, 0xc0, 0x25, 0xfd, 0x9b, 0xa4, 0xc1, 0x00
};

/* wycheproof - misc */
static const u8 enc_input019[] __initconst = {
	0xdd, 0xf2
};
static const u8 enc_output019[] __initconst = {
	0xb6, 0x0d, 0xea, 0xd0, 0xfd, 0x46, 0x97, 0xec,
	0x2e, 0x55, 0x58, 0x23, 0x77, 0x19, 0xd0, 0x24,
	0x37, 0xa2
};
static const u8 enc_assoc019[] __initconst = {
	0x88, 0x36, 0x4f, 0xc8, 0x06, 0x05, 0x18, 0xbf
};
static const u8 enc_nonce019[] __initconst = {
	0x61, 0x54, 0x6b, 0xa5, 0xf1, 0x72, 0x05, 0x90,
	0xb6, 0x04, 0x0a, 0xc6
};
static const u8 enc_key019[] __initconst = {
	0xc8, 0x83, 0x3d, 0xce, 0x5e, 0xa9, 0xf2, 0x48,
	0xaa, 0x20, 0x30, 0xea, 0xcf, 0xe7, 0x2b, 0xff,
	0xe6, 0x9a, 0x62, 0x0c, 0xaf, 0x79, 0x33, 0x44,
	0xe5, 0x71, 0x8f, 0xe0, 0xd7, 0xab, 0x1a, 0x58
};

/* wycheproof - misc */
static const u8 enc_input020[] __initconst = {
	0xab, 0x85, 0xe9, 0xc1, 0x57, 0x17, 0x31
};
static const u8 enc_output020[] __initconst = {
	0x5d, 0xfe, 0x34, 0x40, 0xdb, 0xb3, 0xc3, 0xed,
	0x7a, 0x43, 0x4e, 0x26, 0x02, 0xd3, 0x94, 0x28,
	0x1e, 0x0a, 0xfa, 0x9f, 0xb7, 0xaa, 0x42
};
static const u8 enc_assoc020[] __initconst = { };
static const u8 enc_nonce020[] __initconst = {
	0x3c, 0x4e, 0x65, 0x4d, 0x66, 0x3f, 0xa4, 0x59,
	0x6d, 0xc5, 0x5b, 0xb7
};
static const u8 enc_key020[] __initconst = {
	0x55, 0x56, 0x81, 0x58, 0xd3, 0xa6, 0x48, 0x3f,
	0x1f, 0x70, 0x21, 0xea, 0xb6, 0x9b, 0x70, 0x3f,
	0x61, 0x42, 0x51, 0xca, 0xdc, 0x1a, 0xf5, 0xd3,
	0x4a, 0x37, 0x4f, 0xdb, 0xfc, 0x5a, 0xda, 0xc7
};

/* wycheproof - misc */
static const u8 enc_input021[] __initconst = {
	0x4e, 0xe5, 0xcd, 0xa2, 0x0d, 0x42, 0x90
};
static const u8 enc_output021[] __initconst = {
	0x4b, 0xd4, 0x72, 0x12, 0x94, 0x1c, 0xe3, 0x18,
	0x5f, 0x14, 0x08, 0xee, 0x7f, 0xbf, 0x18, 0xf5,
	0xab, 0xad, 0x6e, 0x22, 0x53, 0xa1, 0xba
};
static const u8 enc_assoc021[] __initconst = {
	0x84, 0xe4, 0x6b, 0xe8, 0xc0, 0x91, 0x90, 0x53
};
static const u8 enc_nonce021[] __initconst = {
	0x58, 0x38, 0x93, 0x75, 0xc6, 0x9e, 0xe3, 0x98,
	0xde, 0x94, 0x83, 0x96
};
static const u8 enc_key021[] __initconst = {
	0xe3, 0xc0, 0x9e, 0x7f, 0xab, 0x1a, 0xef, 0xb5,
	0x16, 0xda, 0x6a, 0x33, 0x02, 0x2a, 0x1d, 0xd4,
	0xeb, 0x27, 0x2c, 0x80, 0xd5, 0x40, 0xc5, 0xda,
	0x52, 0xa7, 0x30, 0xf3, 0x4d, 0x84, 0x0d, 0x7f
};

/* wycheproof - misc */
static const u8 enc_input022[] __initconst = {
	0xbe, 0x33, 0x08, 0xf7, 0x2a, 0x2c, 0x6a, 0xed
};
static const u8 enc_output022[] __initconst = {
	0x8e, 0x94, 0x39, 0xa5, 0x6e, 0xee, 0xc8, 0x17,
	0xfb, 0xe8, 0xa6, 0xed, 0x8f, 0xab, 0xb1, 0x93,
	0x75, 0x39, 0xdd, 0x6c, 0x00, 0xe9, 0x00, 0x21
};
static const u8 enc_assoc022[] __initconst = { };
static const u8 enc_nonce022[] __initconst = {
	0x4f, 0x07, 0xaf, 0xed, 0xfd, 0xc3, 0xb6, 0xc2,
	0x36, 0x18, 0x23, 0xd3
};
static const u8 enc_key022[] __initconst = {
	0x51, 0xe4, 0xbf, 0x2b, 0xad, 0x92, 0xb7, 0xaf,
	0xf1, 0xa4, 0xbc, 0x05, 0x55, 0x0b, 0xa8, 0x1d,
	0xf4, 0xb9, 0x6f, 0xab, 0xf4, 0x1c, 0x12, 0xc7,
	0xb0, 0x0e, 0x60, 0xe4, 0x8d, 0xb7, 0xe1, 0x52
};

/* wycheproof - misc */
static const u8 enc_input023[] __initconst = {
	0xa4, 0xc9, 0xc2, 0x80, 0x1b, 0x71, 0xf7, 0xdf
};
static const u8 enc_output023[] __initconst = {
	0xb9, 0xb9, 0x10, 0x43, 0x3a, 0xf0, 0x52, 0xb0,
	0x45, 0x30, 0xf5, 0x1a, 0xee, 0xe0, 0x24, 0xe0,
	0xa4, 0x45, 0xa6, 0x32, 0x8f, 0xa6, 0x7a, 0x18
};
static const u8 enc_assoc023[] __initconst = {
	0x66, 0xc0, 0xae, 0x70, 0x07, 0x6c, 0xb1, 0x4d
};
static const u8 enc_nonce023[] __initconst = {
	0xb4, 0xea, 0x66, 0x6e, 0xe1, 0x19, 0x56, 0x33,
	0x66, 0x48, 0x4a, 0x78
};
static const u8 enc_key023[] __initconst = {
	0x11, 0x31, 0xc1, 0x41, 0x85, 0x77, 0xa0, 0x54,
	0xde, 0x7a, 0x4a, 0xc5, 0x51, 0x95, 0x0f, 0x1a,
	0x05, 0x3f, 0x9a, 0xe4, 0x6e, 0x5b, 0x75, 0xfe,
	0x4a, 0xbd, 0x56, 0x08, 0xd7, 0xcd, 0xda, 0xdd
};

/* wycheproof - misc */
static const u8 enc_input024[] __initconst = {
	0x42, 0xba, 0xae, 0x59, 0x78, 0xfe, 0xaf, 0x5c,
	0x36, 0x8d, 0x14, 0xe0
};
static const u8 enc_output024[] __initconst = {
	0xff, 0x7d, 0xc2, 0x03, 0xb2, 0x6c, 0x46, 0x7a,
	0x6b, 0x50, 0xdb, 0x33, 0x57, 0x8c, 0x0f, 0x27,
	0x58, 0xc2, 0xe1, 0x4e, 0x36, 0xd4, 0xfc, 0x10,
	0x6d, 0xcb, 0x29, 0xb4
};
static const u8 enc_assoc024[] __initconst = { };
static const u8 enc_nonce024[] __initconst = {
	0x9a, 0x59, 0xfc, 0xe2, 0x6d, 0xf0, 0x00, 0x5e,
	0x07, 0x53, 0x86, 0x56
};
static const u8 enc_key024[] __initconst = {
	0x99, 0xb6, 0x2b, 0xd5, 0xaf, 0xbe, 0x3f, 0xb0,
	0x15, 0xbd, 0xe9, 0x3f, 0x0a, 0xbf, 0x48, 0x39,
	0x57, 0xa1, 0xc3, 0xeb, 0x3c, 0xa5, 0x9c, 0xb5,
	0x0b, 0x39, 0xf7, 0xf8, 0xa9, 0xcc, 0x51, 0xbe
};

/* wycheproof - misc */
static const u8 enc_input025[] __initconst = {
	0xfd, 0xc8, 0x5b, 0x94, 0xa4, 0xb2, 0xa6, 0xb7,
	0x59, 0xb1, 0xa0, 0xda
};
static const u8 enc_output025[] __initconst = {
	0x9f, 0x88, 0x16, 0xde, 0x09, 0x94, 0xe9, 0x38,
	0xd9, 0xe5, 0x3f, 0x95, 0xd0, 0x86, 0xfc, 0x6c,
	0x9d, 0x8f, 0xa9, 0x15, 0xfd, 0x84, 0x23, 0xa7,
	0xcf, 0x05, 0x07, 0x2f
};
static const u8 enc_assoc025[] __initconst = {
	0xa5, 0x06, 0xe1, 0xa5, 0xc6, 0x90, 0x93, 0xf9
};
static const u8 enc_nonce025[] __initconst = {
	0x58, 0xdb, 0xd4, 0xad, 0x2c, 0x4a, 0xd3, 0x5d,
	0xd9, 0x06, 0xe9, 0xce
};
static const u8 enc_key025[] __initconst = {
	0x85, 0xf3, 0x5b, 0x62, 0x82, 0xcf, 0xf4, 0x40,
	0xbc, 0x10, 0x20, 0xc8, 0x13, 0x6f, 0xf2, 0x70,
	0x31, 0x11, 0x0f, 0xa6, 0x3e, 0xc1, 0x6f, 0x1e,
	0x82, 0x51, 0x18, 0xb0, 0x06, 0xb9, 0x12, 0x57
};

/* wycheproof - misc */
static const u8 enc_input026[] __initconst = {
	0x51, 0xf8, 0xc1, 0xf7, 0x31, 0xea, 0x14, 0xac,
	0xdb, 0x21, 0x0a, 0x6d, 0x97, 0x3e, 0x07
};
static const u8 enc_output026[] __initconst = {
	0x0b, 0x29, 0x63, 0x8e, 0x1f, 0xbd, 0xd6, 0xdf,
	0x53, 0x97, 0x0b, 0xe2, 0x21, 0x00, 0x42, 0x2a,
	0x91, 0x34, 0x08, 0x7d, 0x67, 0xa4, 0x6e, 0x79,
	0x17, 0x8d, 0x0a, 0x93, 0xf5, 0xe1, 0xd2
};
static const u8 enc_assoc026[] __initconst = { };
static const u8 enc_nonce026[] __initconst = {
	0x68, 0xab, 0x7f, 0xdb, 0xf6, 0x19, 0x01, 0xda,
	0xd4, 0x61, 0xd2, 0x3c
};
static const u8 enc_key026[] __initconst = {
	0x67, 0x11, 0x96, 0x27, 0xbd, 0x98, 0x8e, 0xda,
	0x90, 0x62, 0x19, 0xe0, 0x8c, 0x0d, 0x0d, 0x77,
	0x9a, 0x07, 0xd2, 0x08, 0xce, 0x8a, 0x4f, 0xe0,
	0x70, 0x9a, 0xf7, 0x55, 0xee, 0xec, 0x6d, 0xcb
};

/* wycheproof - misc */
static const u8 enc_input027[] __initconst = {
	0x97, 0x46, 0x9d, 0xa6, 0x67, 0xd6, 0x11, 0x0f,
	0x9c, 0xbd, 0xa1, 0xd1, 0xa2, 0x06, 0x73
};
static const u8 enc_output027[] __initconst = {
	0x32, 0xdb, 0x66, 0xc4, 0xa3, 0x81, 0x9d, 0x81,
	0x55, 0x74, 0x55, 0xe5, 0x98, 0x0f, 0xed, 0xfe,
	0xae, 0x30, 0xde, 0xc9, 0x4e, 0x6a, 0xd3, 0xa9,
	0xee, 0xa0, 0x6a, 0x0d, 0x70, 0x39, 0x17
};
static const u8 enc_assoc027[] __initconst = {
	0x64, 0x53, 0xa5, 0x33, 0x84, 0x63, 0x22, 0x12
};
static const u8 enc_nonce027[] __initconst = {
	0xd9, 0x5b, 0x32, 0x43, 0xaf, 0xae, 0xf7, 0x14,
	0xc5, 0x03, 0x5b, 0x6a
};
static const u8 enc_key027[] __initconst = {
	0xe6, 0xf1, 0x11, 0x8d, 0x41, 0xe4, 0xb4, 0x3f,
	0xb5, 0x82, 0x21, 0xb7, 0xed, 0x79, 0x67, 0x38,
	0x34, 0xe0, 0xd8, 0xac, 0x5c, 0x4f, 0xa6, 0x0b,
	0xbc, 0x8b, 0xc4, 0x89, 0x3a, 0x58, 0x89, 0x4d
};

/* wycheproof - misc */
static const u8 enc_input028[] __initconst = {
	0x54, 0x9b, 0x36, 0x5a, 0xf9, 0x13, 0xf3, 0xb0,
	0x81, 0x13, 0x1c, 0xcb, 0x6b, 0x82, 0x55, 0x88
};
static const u8 enc_output028[] __initconst = {
	0xe9, 0x11, 0x0e, 0x9f, 0x56, 0xab, 0x3c, 0xa4,
	0x83, 0x50, 0x0c, 0xea, 0xba, 0xb6, 0x7a, 0x13,
	0x83, 0x6c, 0xca, 0xbf, 0x15, 0xa6, 0xa2, 0x2a,
	0x51, 0xc1, 0x07, 0x1c, 0xfa, 0x68, 0xfa, 0x0c
};
static const u8 enc_assoc028[] __initconst = { };
static const u8 enc_nonce028[] __initconst = {
	0x2f, 0xcb, 0x1b, 0x38, 0xa9, 0x9e, 0x71, 0xb8,
	0x47, 0x40, 0xad, 0x9b
};
static const u8 enc_key028[] __initconst = {
	0x59, 0xd4, 0xea, 0xfb, 0x4d, 0xe0, 0xcf, 0xc7,
	0xd3, 0xdb, 0x99, 0xa8, 0xf5, 0x4b, 0x15, 0xd7,
	0xb3, 0x9f, 0x0a, 0xcc, 0x8d, 0xa6, 0x97, 0x63,
	0xb0, 0x19, 0xc1, 0x69, 0x9f, 0x87, 0x67, 0x4a
};

/* wycheproof - misc */
static const u8 enc_input029[] __initconst = {
	0x55, 0xa4, 0x65, 0x64, 0x4f, 0x5b, 0x65, 0x09,
	0x28, 0xcb, 0xee, 0x7c, 0x06, 0x32, 0x14, 0xd6
};
static const u8 enc_output029[] __initconst = {
	0xe4, 0xb1, 0x13, 0xcb, 0x77, 0x59, 0x45, 0xf3,
	0xd3, 0xa8, 0xae, 0x9e, 0xc1, 0x41, 0xc0, 0x0c,
	0x7c, 0x43, 0xf1, 0x6c, 0xe0, 0x96, 0xd0, 0xdc,
	0x27, 0xc9, 0x58, 0x49, 0xdc, 0x38, 0x3b, 0x7d
};
static const u8 enc_assoc029[] __initconst = {
	0x03, 0x45, 0x85, 0x62, 0x1a, 0xf8, 0xd7, 0xff
};
static const u8 enc_nonce029[] __initconst = {
	0x11, 0x8a, 0x69, 0x64, 0xc2, 0xd3, 0xe3, 0x80,
	0x07, 0x1f, 0x52, 0x66
};
static const u8 enc_key029[] __initconst = {
	0xb9, 0x07, 0xa4, 0x50, 0x75, 0x51, 0x3f, 0xe8,
	0xa8, 0x01, 0x9e, 0xde, 0xe3, 0xf2, 0x59, 0x14,
	0x87, 0xb2, 0xa0, 0x30, 0xb0, 0x3c, 0x6e, 0x1d,
	0x77, 0x1c, 0x86, 0x25, 0x71, 0xd2, 0xea, 0x1e
};

/* wycheproof - misc */
static const u8 enc_input030[] __initconst = {
	0x3f, 0xf1, 0x51, 0x4b, 0x1c, 0x50, 0x39, 0x15,
	0x91, 0x8f, 0x0c, 0x0c, 0x31, 0x09, 0x4a, 0x6e,
	0x1f
};
static const u8 enc_output030[] __initconst = {
	0x02, 0xcc, 0x3a, 0xcb, 0x5e, 0xe1, 0xfc, 0xdd,
	0x12, 0xa0, 0x3b, 0xb8, 0x57, 0x97, 0x64, 0x74,
	0xd3, 0xd8, 0x3b, 0x74, 0x63, 0xa2, 0xc3, 0x80,
	0x0f, 0xe9, 0x58, 0xc2, 0x8e, 0xaa, 0x29, 0x08,
	0x13
};
static const u8 enc_assoc030[] __initconst = { };
static const u8 enc_nonce030[] __initconst = {
	0x45, 0xaa, 0xa3, 0xe5, 0xd1, 0x6d, 0x2d, 0x42,
	0xdc, 0x03, 0x44, 0x5d
};
static const u8 enc_key030[] __initconst = {
	0x3b, 0x24, 0x58, 0xd8, 0x17, 0x6e, 0x16, 0x21,
	0xc0, 0xcc, 0x24, 0xc0, 0xc0, 0xe2, 0x4c, 0x1e,
	0x80, 0xd7, 0x2f, 0x7e, 0xe9, 0x14, 0x9a, 0x4b,
	0x16, 0x61, 0x76, 0x62, 0x96, 0x16, 0xd0, 0x11
};

/* wycheproof - misc */
static const u8 enc_input031[] __initconst = {
	0x63, 0x85, 0x8c, 0xa3, 0xe2, 0xce, 0x69, 0x88,
	0x7b, 0x57, 0x8a, 0x3c, 0x16, 0x7b, 0x42, 0x1c,
	0x9c
};
static const u8 enc_output031[] __initconst = {
	0x35, 0x76, 0x64, 0x88, 0xd2, 0xbc, 0x7c, 0x2b,
	0x8d, 0x17, 0xcb, 0xbb, 0x9a, 0xbf, 0xad, 0x9e,
	0x6d, 0x1f, 0x39, 0x1e, 0x65, 0x7b, 0x27, 0x38,
	0xdd, 0xa0, 0x84, 0x48, 0xcb, 0xa2, 0x81, 0x1c,
	0xeb
};
static const u8 enc_assoc031[] __initconst = {
	0x9a, 0xaf, 0x29, 0x9e, 0xee, 0xa7, 0x8f, 0x79
};
static const u8 enc_nonce031[] __initconst = {
	0xf0, 0x38, 0x4f, 0xb8, 0x76, 0x12, 0x14, 0x10,
	0x63, 0x3d, 0x99, 0x3d
};
static const u8 enc_key031[] __initconst = {
	0xf6, 0x0c, 0x6a, 0x1b, 0x62, 0x57, 0x25, 0xf7,
	0x6c, 0x70, 0x37, 0xb4, 0x8f, 0xe3, 0x57, 0x7f,
	0xa7, 0xf7, 0xb8, 0x7b, 0x1b, 0xd5, 0xa9, 0x82,
	0x17, 0x6d, 0x18, 0x23, 0x06, 0xff, 0xb8, 0x70
};

/* wycheproof - misc */
static const u8 enc_input032[] __initconst = {
	0x10, 0xf1, 0xec, 0xf9, 0xc6, 0x05, 0x84, 0x66,
	0x5d, 0x9a, 0xe5, 0xef, 0xe2, 0x79, 0xe7, 0xf7,
	0x37, 0x7e, 0xea, 0x69, 0x16, 0xd2, 0xb1, 0x11
};
static const u8 enc_output032[] __initconst = {
	0x42, 0xf2, 0x6c, 0x56, 0xcb, 0x4b, 0xe2, 0x1d,
	0x9d, 0x8d, 0x0c, 0x80, 0xfc, 0x99, 0xdd, 0xe0,
	0x0d, 0x75, 0xf3, 0x80, 0x74, 0xbf, 0xe7, 0x64,
	0x54, 0xaa, 0x7e, 0x13, 0xd4, 0x8f, 0xff, 0x7d,
	0x75, 0x57, 0x03, 0x94, 0x57, 0x04, 0x0a, 0x3a
};
static const u8 enc_assoc032[] __initconst = { };
static const u8 enc_nonce032[] __initconst = {
	0xe6, 0xb1, 0xad, 0xf2, 0xfd, 0x58, 0xa8, 0x76,
	0x2c, 0x65, 0xf3, 0x1b
};
static const u8 enc_key032[] __initconst = {
	0x02, 0x12, 0xa8, 0xde, 0x50, 0x07, 0xed, 0x87,
	0xb3, 0x3f, 0x1a, 0x70, 0x90, 0xb6, 0x11, 0x4f,
	0x9e, 0x08, 0xce, 0xfd, 0x96, 0x07, 0xf2, 0xc2,
	0x76, 0xbd, 0xcf, 0xdb, 0xc5, 0xce, 0x9c, 0xd7
};

/* wycheproof - misc */
static const u8 enc_input033[] __initconst = {
	0x92, 0x22, 0xf9, 0x01, 0x8e, 0x54, 0xfd, 0x6d,
	0xe1, 0x20, 0x08, 0x06, 0xa9, 0xee, 0x8e, 0x4c,
	0xc9, 0x04, 0xd2, 0x9f, 0x25, 0xcb, 0xa1, 0x93
};
static const u8 enc_output033[] __initconst = {
	0x12, 0x30, 0x32, 0x43, 0x7b, 0x4b, 0xfd, 0x69,
	0x20, 0xe8, 0xf7, 0xe7, 0xe0, 0x08, 0x7a, 0xe4,
	0x88, 0x9e, 0xbe, 0x7a, 0x0a, 0xd0, 0xe9, 0x00,
	0x3c, 0xf6, 0x8f, 0x17, 0x95, 0x50, 0xda, 0x63,
	0xd3, 0xb9, 0x6c, 0x2d, 0x55, 0x41, 0x18, 0x65
};
static const u8 enc_assoc033[] __initconst = {
	0x3e, 0x8b, 0xc5, 0xad, 0xe1, 0x82, 0xff, 0x08
};
static const u8 enc_nonce033[] __initconst = {
	0x6b, 0x28, 0x2e, 0xbe, 0xcc, 0x54, 0x1b, 0xcd,
	0x78, 0x34, 0xed, 0x55
};
static const u8 enc_key033[] __initconst = {
	0xc5, 0xbc, 0x09, 0x56, 0x56, 0x46, 0xe7, 0xed,
	0xda, 0x95, 0x4f, 0x1f, 0x73, 0x92, 0x23, 0xda,
	0xda, 0x20, 0xb9, 0x5c, 0x44, 0xab, 0x03, 0x3d,
	0x0f, 0xae, 0x4b, 0x02, 0x83, 0xd1, 0x8b, 0xe3
};

/* wycheproof - misc */
static const u8 enc_input034[] __initconst = {
	0xb0, 0x53, 0x99, 0x92, 0x86, 0xa2, 0x82, 0x4f,
	0x42, 0xcc, 0x8c, 0x20, 0x3a, 0xb2, 0x4e, 0x2c,
	0x97, 0xa6, 0x85, 0xad, 0xcc, 0x2a, 0xd3, 0x26,
	0x62, 0x55, 0x8e, 0x55, 0xa5, 0xc7, 0x29
};
static const u8 enc_output034[] __initconst = {
	0x45, 0xc7, 0xd6, 0xb5, 0x3a, 0xca, 0xd4, 0xab,
	0xb6, 0x88, 0x76, 0xa6, 0xe9, 0x6a, 0x48, 0xfb,
	0x59, 0x52, 0x4d, 0x2c, 0x92, 0xc9, 0xd8, 0xa1,
	0x89, 0xc9, 0xfd, 0x2d, 0xb9, 0x17, 0x46, 0x56,
	0x6d, 0x3c, 0xa1, 0x0e, 0x31, 0x1b, 0x69, 0x5f,
	0x3e, 0xae, 0x15, 0x51, 0x65, 0x24, 0x93
};
static const u8 enc_assoc034[] __initconst = { };
static const u8 enc_nonce034[] __initconst = {
	0x04, 0xa9, 0xbe, 0x03, 0x50, 0x8a, 0x5f, 0x31,
	0x37, 0x1a, 0x6f, 0xd2
};
static const u8 enc_key034[] __initconst = {
	0x2e, 0xb5, 0x1c, 0x46, 0x9a, 0xa8, 0xeb, 0x9e,
	0x6c, 0x54, 0xa8, 0x34, 0x9b, 0xae, 0x50, 0xa2,
	0x0f, 0x0e, 0x38, 0x27, 0x11, 0xbb, 0xa1, 0x15,
	0x2c, 0x42, 0x4f, 0x03, 0xb6, 0x67, 0x1d, 0x71
};

/* wycheproof - misc */
static const u8 enc_input035[] __initconst = {
	0xf4, 0x52, 0x06, 0xab, 0xc2, 0x55, 0x52, 0xb2,
	0xab, 0xc9, 0xab, 0x7f, 0xa2, 0x43, 0x03, 0x5f,
	0xed, 0xaa, 0xdd, 0xc3, 0xb2, 0x29, 0x39, 0x56,
	0xf1, 0xea, 0x6e, 0x71, 0x56, 0xe7, 0xeb
};
static const u8 enc_output035[] __initconst = {
	0x46, 0xa8, 0x0c, 0x41, 0x87, 0x02, 0x47, 0x20,
	0x08, 0x46, 0x27, 0x58, 0x00, 0x80, 0xdd, 0xe5,
	0xa3, 0xf4, 0xa1, 0x10, 0x93, 0xa7, 0x07, 0x6e,
	0xd6, 0xf3, 0xd3, 0x26, 0xbc, 0x7b, 0x70, 0x53,
	0x4d, 0x4a, 0xa2, 0x83, 0x5a, 0x52, 0xe7, 0x2d,
	0x14, 0xdf, 0x0e, 0x4f, 0x47, 0xf2, 0x5f
};
static const u8 enc_assoc035[] __initconst = {
	0x37, 0x46, 0x18, 0xa0, 0x6e, 0xa9, 0x8a, 0x48
};
static const u8 enc_nonce035[] __initconst = {
	0x47, 0x0a, 0x33, 0x9e, 0xcb, 0x32, 0x19, 0xb8,
	0xb8, 0x1a, 0x1f, 0x8b
};
static const u8 enc_key035[] __initconst = {
	0x7f, 0x5b, 0x74, 0xc0, 0x7e, 0xd1, 0xb4, 0x0f,
	0xd1, 0x43, 0x58, 0xfe, 0x2f, 0xf2, 0xa7, 0x40,
	0xc1, 0x16, 0xc7, 0x70, 0x65, 0x10, 0xe6, 0xa4,
	0x37, 0xf1, 0x9e, 0xa4, 0x99, 0x11, 0xce, 0xc4
};

/* wycheproof - misc */
static const u8 enc_input036[] __initconst = {
	0xb9, 0xc5, 0x54, 0xcb, 0xc3, 0x6a, 0xc1, 0x8a,
	0xe8, 0x97, 0xdf, 0x7b, 0xee, 0xca, 0xc1, 0xdb,
	0xeb, 0x4e, 0xaf, 0xa1, 0x56, 0xbb, 0x60, 0xce,
	0x2e, 0x5d, 0x48, 0xf0, 0x57, 0x15, 0xe6, 0x78
};
static const u8 enc_output036[] __initconst = {
	0xea, 0x29, 0xaf, 0xa4, 0x9d, 0x36, 0xe8, 0x76,
	0x0f, 0x5f, 0xe1, 0x97, 0x23, 0xb9, 0x81, 0x1e,
	0xd5, 0xd5, 0x19, 0x93, 0x4a, 0x44, 0x0f, 0x50,
	0x81, 0xac, 0x43, 0x0b, 0x95, 0x3b, 0x0e, 0x21,
	0x22, 0x25, 0x41, 0xaf, 0x46, 0xb8, 0x65, 0x33,
	0xc6, 0xb6, 0x8d, 0x2f, 0xf1, 0x08, 0xa7, 0xea
};
static const u8 enc_assoc036[] __initconst = { };
static const u8 enc_nonce036[] __initconst = {
	0x72, 0xcf, 0xd9, 0x0e, 0xf3, 0x02, 0x6c, 0xa2,
	0x2b, 0x7e, 0x6e, 0x6a
};
static const u8 enc_key036[] __initconst = {
	0xe1, 0x73, 0x1d, 0x58, 0x54, 0xe1, 0xb7, 0x0c,
	0xb3, 0xff, 0xe8, 0xb7, 0x86, 0xa2, 0xb3, 0xeb,
	0xf0, 0x99, 0x43, 0x70, 0x95, 0x47, 0x57, 0xb9,
	0xdc, 0x8c, 0x7b, 0xc5, 0x35, 0x46, 0x34, 0xa3
};

/* wycheproof - misc */
static const u8 enc_input037[] __initconst = {
	0x6b, 0x26, 0x04, 0x99, 0x6c, 0xd3, 0x0c, 0x14,
	0xa1, 0x3a, 0x52, 0x57, 0xed, 0x6c, 0xff, 0xd3,
	0xbc, 0x5e, 0x29, 0xd6, 0xb9, 0x7e, 0xb1, 0x79,
	0x9e, 0xb3, 0x35, 0xe2, 0x81, 0xea, 0x45, 0x1e
};
static const u8 enc_output037[] __initconst = {
	0x6d, 0xad, 0x63, 0x78, 0x97, 0x54, 0x4d, 0x8b,
	0xf6, 0xbe, 0x95, 0x07, 0xed, 0x4d, 0x1b, 0xb2,
	0xe9, 0x54, 0xbc, 0x42, 0x7e, 0x5d, 0xe7, 0x29,
	0xda, 0xf5, 0x07, 0x62, 0x84, 0x6f, 0xf2, 0xf4,
	0x7b, 0x99, 0x7d, 0x93, 0xc9, 0x82, 0x18, 0x9d,
	0x70, 0x95, 0xdc, 0x79, 0x4c, 0x74, 0x62, 0x32
};
static const u8 enc_assoc037[] __initconst = {
	0x23, 0x33, 0xe5, 0xce, 0x0f, 0x93, 0xb0, 0x59
};
static const u8 enc_nonce037[] __initconst = {
	0x26, 0x28, 0x80, 0xd4, 0x75, 0xf3, 0xda, 0xc5,
	0x34, 0x0d, 0xd1, 0xb8
};
static const u8 enc_key037[] __initconst = {
	0x27, 0xd8, 0x60, 0x63, 0x1b, 0x04, 0x85, 0xa4,
	0x10, 0x70, 0x2f, 0xea, 0x61, 0xbc, 0x87, 0x3f,
	0x34, 0x42, 0x26, 0x0c, 0xad, 0xed, 0x4a, 0xbd,
	0xe2, 0x5b, 0x78, 0x6a, 0x2d, 0x97, 0xf1, 0x45
};

/* wycheproof - misc */
static const u8 enc_input038[] __initconst = {
	0x97, 0x3d, 0x0c, 0x75, 0x38, 0x26, 0xba, 0xe4,
	0x66, 0xcf, 0x9a, 0xbb, 0x34, 0x93, 0x15, 0x2e,
	0x9d, 0xe7, 0x81, 0x9e, 0x2b, 0xd0, 0xc7, 0x11,
	0x71, 0x34, 0x6b, 0x4d, 0x2c, 0xeb, 0xf8, 0x04,
	0x1a, 0xa3, 0xce, 0xdc, 0x0d, 0xfd, 0x7b, 0x46,
	0x7e, 0x26, 0x22, 0x8b, 0xc8, 0x6c, 0x9a
};
static const u8 enc_output038[] __initconst = {
	0xfb, 0xa7, 0x8a, 0xe4, 0xf9, 0xd8, 0x08, 0xa6,
	0x2e, 0x3d, 0xa4, 0x0b, 0xe2, 0xcb, 0x77, 0x00,
	0xc3, 0x61, 0x3d, 0x9e, 0xb2, 0xc5, 0x29, 0xc6,
	0x52, 0xe7, 0x6a, 0x43, 0x2c, 0x65, 0x8d, 0x27,
	0x09, 0x5f, 0x0e, 0xb8, 0xf9, 0x40, 0xc3, 0x24,
	0x98, 0x1e, 0xa9, 0x35, 0xe5, 0x07, 0xf9, 0x8f,
	0x04, 0x69, 0x56, 0xdb, 0x3a, 0x51, 0x29, 0x08,
	0xbd, 0x7a, 0xfc, 0x8f, 0x2a, 0xb0, 0xa9
};
static const u8 enc_assoc038[] __initconst = { };
static const u8 enc_nonce038[] __initconst = {
	0xe7, 0x4a, 0x51, 0x5e, 0x7e, 0x21, 0x02, 0xb9,
	0x0b, 0xef, 0x55, 0xd2
};
static const u8 enc_key038[] __initconst = {
	0xcf, 0x0d, 0x40, 0xa4, 0x64, 0x4e, 0x5f, 0x51,
	0x81, 0x51, 0x65, 0xd5, 0x30, 0x1b, 0x22, 0x63,
	0x1f, 0x45, 0x44, 0xc4, 0x9a, 0x18, 0x78, 0xe3,
	0xa0, 0xa5, 0xe8, 0xe1, 0xaa, 0xe0, 0xf2, 0x64
};

/* wycheproof - misc */
static const u8 enc_input039[] __initconst = {
	0xa9, 0x89, 0x95, 0x50, 0x4d, 0xf1, 0x6f, 0x74,
	0x8b, 0xfb, 0x77, 0x85, 0xff, 0x91, 0xee, 0xb3,
	0xb6, 0x60, 0xea, 0x9e, 0xd3, 0x45, 0x0c, 0x3d,
	0x5e, 0x7b, 0x0e, 0x79, 0xef, 0x65, 0x36, 0x59,
	0xa9, 0x97, 0x8d, 0x75, 0x54, 0x2e, 0xf9, 0x1c,
	0x45, 0x67, 0x62, 0x21, 0x56, 0x40, 0xb9
};
static const u8 enc_output039[] __initconst = {
	0xa1, 0xff, 0xed, 0x80, 0x76, 0x18, 0x29, 0xec,
	0xce, 0x24, 0x2e, 0x0e, 0x88, 0xb1, 0x38, 0x04,
	0x90, 0x16, 0xbc, 0xa0, 0x18, 0xda, 0x2b, 0x6e,
	0x19, 0x98, 0x6b, 0x3e, 0x31, 0x8c, 0xae, 0x8d,
	0x80, 0x61, 0x98, 0xfb, 0x4c, 0x52, 0x7c, 0xc3,
	0x93, 0x50, 0xeb, 0xdd, 0xea, 0xc5, 0x73, 0xc4,
	0xcb, 0xf0, 0xbe, 0xfd, 0xa0, 0xb7, 0x02, 0x42,
	0xc6, 0x40, 0xd7, 0xcd, 0x02, 0xd7, 0xa3
};
static const u8 enc_assoc039[] __initconst = {
	0xb3, 0xe4, 0x06, 0x46, 0x83, 0xb0, 0x2d, 0x84
};
static const u8 enc_nonce039[] __initconst = {
	0xd4, 0xd8, 0x07, 0x34, 0x16, 0x83, 0x82, 0x5b,
	0x31, 0xcd, 0x4d, 0x95
};
static const u8 enc_key039[] __initconst = {
	0x6c, 0xbf, 0xd7, 0x1c, 0x64, 0x5d, 0x18, 0x4c,
	0xf5, 0xd2, 0x3c, 0x40, 0x2b, 0xdb, 0x0d, 0x25,
	0xec, 0x54, 0x89, 0x8c, 0x8a, 0x02, 0x73, 0xd4,
	0x2e, 0xb5, 0xbe, 0x10, 0x9f, 0xdc, 0xb2, 0xac
};

/* wycheproof - misc */
static const u8 enc_input040[] __initconst = {
	0xd0, 0x96, 0x80, 0x31, 0x81, 0xbe, 0xef, 0x9e,
	0x00, 0x8f, 0xf8, 0x5d, 0x5d, 0xdc, 0x38, 0xdd,
	0xac, 0xf0, 0xf0, 0x9e, 0xe5, 0xf7, 0xe0, 0x7f,
	0x1e, 0x40, 0x79, 0xcb, 0x64, 0xd0, 0xdc, 0x8f,
	0x5e, 0x67, 0x11, 0xcd, 0x49, 0x21, 0xa7, 0x88,
	0x7d, 0xe7, 0x6e, 0x26, 0x78, 0xfd, 0xc6, 0x76,
	0x18, 0xf1, 0x18, 0x55, 0x86, 0xbf, 0xea, 0x9d,
	0x4c, 0x68, 0x5d, 0x50, 0xe4, 0xbb, 0x9a, 0x82
};
static const u8 enc_output040[] __initconst = {
	0x9a, 0x4e, 0xf2, 0x2b, 0x18, 0x16, 0x77, 0xb5,
	0x75, 0x5c, 0x08, 0xf7, 0x47, 0xc0, 0xf8, 0xd8,
	0xe8, 0xd4, 0xc1, 0x8a, 0x9c, 0xc2, 0x40, 0x5c,
	0x12, 0xbb, 0x51, 0xbb, 0x18, 0x72, 0xc8, 0xe8,
	0xb8, 0x77, 0x67, 0x8b, 0xec, 0x44, 0x2c, 0xfc,
	0xbb, 0x0f, 0xf4, 0x64, 0xa6, 0x4b, 0x74, 0x33,
	0x2c, 0xf0, 0x72, 0x89, 0x8c, 0x7e, 0x0e, 0xdd,
	0xf6, 0x23, 0x2e, 0xa6, 0xe2, 0x7e, 0xfe, 0x50,
	0x9f, 0xf3, 0x42, 0x7a, 0x0f, 0x32, 0xfa, 0x56,
	0x6d, 0x9c, 0xa0, 0xa7, 0x8a, 0xef, 0xc0, 0x13
};
static const u8 enc_assoc040[] __initconst = { };
static const u8 enc_nonce040[] __initconst = {
	0xd6, 0x10, 0x40, 0xa3, 0x13, 0xed, 0x49, 0x28,
	0x23, 0xcc, 0x06, 0x5b
};
static const u8 enc_key040[] __initconst = {
	0x5b, 0x1d, 0x10, 0x35, 0xc0, 0xb1, 0x7e, 0xe0,
	0xb0, 0x44, 0x47, 0x67, 0xf8, 0x0a, 0x25, 0xb8,
	0xc1, 0xb7, 0x41, 0xf4, 0xb5, 0x0a, 0x4d, 0x30,
	0x52, 0x22, 0x6b, 0xaa, 0x1c, 0x6f, 0xb7, 0x01
};

/* wycheproof - misc */
static const u8 enc_input041[] __initconst = {
	0x94, 0xee, 0x16, 0x6d, 0x6d, 0x6e, 0xcf, 0x88,
	0x32, 0x43, 0x71, 0x36, 0xb4, 0xae, 0x80, 0x5d,
	0x42, 0x88, 0x64, 0x35, 0x95, 0x86, 0xd9, 0x19,
	0x3a, 0x25, 0x01, 0x62, 0x93, 0xed, 0xba, 0x44,
	0x3c, 0x58, 0xe0, 0x7e, 0x7b, 0x71, 0x95, 0xec,
	0x5b, 0xd8, 0x45, 0x82, 0xa9, 0xd5, 0x6c, 0x8d,
	0x4a, 0x10, 0x8c, 0x7d, 0x7c, 0xe3, 0x4e, 0x6c,
	0x6f, 0x8e, 0xa1, 0xbe, 0xc0, 0x56, 0x73, 0x17
};
static const u8 enc_output041[] __initconst = {
	0x5f, 0xbb, 0xde, 0xcc, 0x34, 0xbe, 0x20, 0x16,
	0x14, 0xf6, 0x36, 0x03, 0x1e, 0xeb, 0x42, 0xf1,
	0xca, 0xce, 0x3c, 0x79, 0xa1, 0x2c, 0xff, 0xd8,
	0x71, 0xee, 0x8e, 0x73, 0x82, 0x0c, 0x82, 0x97,
	0x49, 0xf1, 0xab, 0xb4, 0x29, 0x43, 0x67, 0x84,
	0x9f, 0xb6, 0xc2, 0xaa, 0x56, 0xbd, 0xa8, 0xa3,
	0x07, 0x8f, 0x72, 0x3d, 0x7c, 0x1c, 0x85, 0x20,
	0x24, 0xb0, 0x17, 0xb5, 0x89, 0x73, 0xfb, 0x1e,
	0x09, 0x26, 0x3d, 0xa7, 0xb4, 0xcb, 0x92, 0x14,
	0x52, 0xf9, 0x7d, 0xca, 0x40, 0xf5, 0x80, 0xec
};
static const u8 enc_assoc041[] __initconst = {
	0x71, 0x93, 0xf6, 0x23, 0x66, 0x33, 0x21, 0xa2
};
static const u8 enc_nonce041[] __initconst = {
	0xd3, 0x1c, 0x21, 0xab, 0xa1, 0x75, 0xb7, 0x0d,
	0xe4, 0xeb, 0xb1, 0x9c
};
static const u8 enc_key041[] __initconst = {
	0x97, 0xd6, 0x35, 0xc4, 0xf4, 0x75, 0x74, 0xd9,
	0x99, 0x8a, 0x90, 0x87, 0x5d, 0xa1, 0xd3, 0xa2,
	0x84, 0xb7, 0x55, 0xb2, 0xd3, 0x92, 0x97, 0xa5,
	0x72, 0x52, 0x35, 0x19, 0x0e, 0x10, 0xa9, 0x7e
};

/* wycheproof - misc */
static const u8 enc_input042[] __initconst = {
	0xb4, 0x29, 0xeb, 0x80, 0xfb, 0x8f, 0xe8, 0xba,
	0xed, 0xa0, 0xc8, 0x5b, 0x9c, 0x33, 0x34, 0x58,
	0xe7, 0xc2, 0x99, 0x2e, 0x55, 0x84, 0x75, 0x06,
	0x9d, 0x12, 0xd4, 0x5c, 0x22, 0x21, 0x75, 0x64,
	0x12, 0x15, 0x88, 0x03, 0x22, 0x97, 0xef, 0xf5,
	0x67, 0x83, 0x74, 0x2a, 0x5f, 0xc2, 0x2d, 0x74,
	0x10, 0xff, 0xb2, 0x9d, 0x66, 0x09, 0x86, 0x61,
	0xd7, 0x6f, 0x12, 0x6c, 0x3c, 0x27, 0x68, 0x9e,
	0x43, 0xb3, 0x72, 0x67, 0xca, 0xc5, 0xa3, 0xa6,
	0xd3, 0xab, 0x49, 0xe3, 0x91, 0xda, 0x29, 0xcd,
	0x30, 0x54, 0xa5, 0x69, 0x2e, 0x28, 0x07, 0xe4,
	0xc3, 0xea, 0x46, 0xc8, 0x76, 0x1d, 0x50, 0xf5,
	0x92
};
static const u8 enc_output042[] __initconst = {
	0xd0, 0x10, 0x2f, 0x6c, 0x25, 0x8b, 0xf4, 0x97,
	0x42, 0xce, 0xc3, 0x4c, 0xf2, 0xd0, 0xfe, 0xdf,
	0x23, 0xd1, 0x05, 0xfb, 0x4c, 0x84, 0xcf, 0x98,
	0x51, 0x5e, 0x1b, 0xc9, 0xa6, 0x4f, 0x8a, 0xd5,
	0xbe, 0x8f, 0x07, 0x21, 0xbd, 0xe5, 0x06, 0x45,
	0xd0, 0x00, 0x83, 0xc3, 0xa2, 0x63, 0xa3, 0x10,
	0x53, 0xb7, 0x60, 0x24, 0x5f, 0x52, 0xae, 0x28,
	0x66, 0xa5, 0xec, 0x83, 0xb1, 0x9f, 0x61, 0xbe,
	0x1d, 0x30, 0xd5, 0xc5, 0xd9, 0xfe, 0xcc, 0x4c,
	0xbb, 0xe0, 0x8f, 0xd3, 0x85, 0x81, 0x3a, 0x2a,
	0xa3, 0x9a, 0x00, 0xff, 0x9c, 0x10, 0xf7, 0xf2,
	0x37, 0x02, 0xad, 0xd1, 0xe4, 0xb2, 0xff, 0xa3,
	0x1c, 0x41, 0x86, 0x5f, 0xc7, 0x1d, 0xe1, 0x2b,
	0x19, 0x61, 0x21, 0x27, 0xce, 0x49, 0x99, 0x3b,
	0xb0
};
static const u8 enc_assoc042[] __initconst = { };
static const u8 enc_nonce042[] __initconst = {
	0x17, 0xc8, 0x6a, 0x8a, 0xbb, 0xb7, 0xe0, 0x03,
	0xac, 0xde, 0x27, 0x99
};
static const u8 enc_key042[] __initconst = {
	0xfe, 0x6e, 0x55, 0xbd, 0xae, 0xd1, 0xf7, 0x28,
	0x4c, 0xa5, 0xfc, 0x0f, 0x8c, 0x5f, 0x2b, 0x8d,
	0xf5, 0x6d, 0xc0, 0xf4, 0x9e, 0x8c, 0xa6, 0x6a,
	0x41, 0x99, 0x5e, 0x78, 0x33, 0x51, 0xf9, 0x01
};

/* wycheproof - misc */
static const u8 enc_input043[] __initconst = {
	0xce, 0xb5, 0x34, 0xce, 0x50, 0xdc, 0x23, 0xff,
	0x63, 0x8a, 0xce, 0x3e, 0xf6, 0x3a, 0xb2, 0xcc,
	0x29, 0x73, 0xee, 0xad, 0xa8, 0x07, 0x85, 0xfc,
	0x16, 0x5d, 0x06, 0xc2, 0xf5, 0x10, 0x0f, 0xf5,
	0xe8, 0xab, 0x28, 0x82, 0xc4, 0x75, 0xaf, 0xcd,
	0x05, 0xcc, 0xd4, 0x9f, 0x2e, 0x7d, 0x8f, 0x55,
	0xef, 0x3a, 0x72, 0xe3, 0xdc, 0x51, 0xd6, 0x85,
	0x2b, 0x8e, 0x6b, 0x9e, 0x7a, 0xec, 0xe5, 0x7b,
	0xe6, 0x55, 0x6b, 0x0b, 0x6d, 0x94, 0x13, 0xe3,
	0x3f, 0xc5, 0xfc, 0x24, 0xa9, 0xa2, 0x05, 0xad,
	0x59, 0x57, 0x4b, 0xb3, 0x9d, 0x94, 0x4a, 0x92,
	0xdc, 0x47, 0x97, 0x0d, 0x84, 0xa6, 0xad, 0x31,
	0x76
};
static const u8 enc_output043[] __initconst = {
	0x75, 0x45, 0x39, 0x1b, 0x51, 0xde, 0x01, 0xd5,
	0xc5, 0x3d, 0xfa, 0xca, 0x77, 0x79, 0x09, 0x06,
	0x3e, 0x58, 0xed, 0xee, 0x4b, 0xb1, 0x22, 0x7e,
	0x71, 0x10, 0xac, 0x4d, 0x26, 0x20, 0xc2, 0xae,
	0xc2, 0xf8, 0x48, 0xf5, 0x6d, 0xee, 0xb0, 0x37,
	0xa8, 0xdc, 0xed, 0x75, 0xaf, 0xa8, 0xa6, 0xc8,
	0x90, 0xe2, 0xde, 0xe4, 0x2f, 0x95, 0x0b, 0xb3,
	0x3d, 0x9e, 0x24, 0x24, 0xd0, 0x8a, 0x50, 0x5d,
	0x89, 0x95, 0x63, 0x97, 0x3e, 0xd3, 0x88, 0x70,
	0xf3, 0xde, 0x6e, 0xe2, 0xad, 0xc7, 0xfe, 0x07,
	0x2c, 0x36, 0x6c, 0x14, 0xe2, 0xcf, 0x7c, 0xa6,
	0x2f, 0xb3, 0xd3, 0x6b, 0xee, 0x11, 0x68, 0x54,
	0x61, 0xb7, 0x0d, 0x44, 0xef, 0x8c, 0x66, 0xc5,
	0xc7, 0xbb, 0xf1, 0x0d, 0xca, 0xdd, 0x7f, 0xac,
	0xf6
};
static const u8 enc_assoc043[] __initconst = {
	0xa1, 0x1c, 0x40, 0xb6, 0x03, 0x76, 0x73, 0x30
};
static const u8 enc_nonce043[] __initconst = {
	0x46, 0x36, 0x2f, 0x45, 0xd6, 0x37, 0x9e, 0x63,
	0xe5, 0x22, 0x94, 0x60
};
static const u8 enc_key043[] __initconst = {
	0xaa, 0xbc, 0x06, 0x34, 0x74, 0xe6, 0x5c, 0x4c,
	0x3e, 0x9b, 0xdc, 0x48, 0x0d, 0xea, 0x97, 0xb4,
	0x51, 0x10, 0xc8, 0x61, 0x88, 0x46, 0xff, 0x6b,
	0x15, 0xbd, 0xd2, 0xa4, 0xa5, 0x68, 0x2c, 0x4e
};

/* wycheproof - misc */
static const u8 enc_input044[] __initconst = {
	0xe5, 0xcc, 0xaa, 0x44, 0x1b, 0xc8, 0x14, 0x68,
	0x8f, 0x8f, 0x6e, 0x8f, 0x28, 0xb5, 0x00, 0xb2
};
static const u8 enc_output044[] __initconst = {
	0x7e, 0x72, 0xf5, 0xa1, 0x85, 0xaf, 0x16, 0xa6,
	0x11, 0x92, 0x1b, 0x43, 0x8f, 0x74, 0x9f, 0x0b,
	0x12, 0x42, 0xc6, 0x70, 0x73, 0x23, 0x34, 0x02,
	0x9a, 0xdf, 0xe1, 0xc5, 0x00, 0x16, 0x51, 0xe4
};
static const u8 enc_assoc044[] __initconst = {
	0x02
};
static const u8 enc_nonce044[] __initconst = {
	0x87, 0x34, 0x5f, 0x10, 0x55, 0xfd, 0x9e, 0x21,
	0x02, 0xd5, 0x06, 0x56
};
static const u8 enc_key044[] __initconst = {
	0x7d, 0x00, 0xb4, 0x80, 0x95, 0xad, 0xfa, 0x32,
	0x72, 0x05, 0x06, 0x07, 0xb2, 0x64, 0x18, 0x50,
	0x02, 0xba, 0x99, 0x95, 0x7c, 0x49, 0x8b, 0xe0,
	0x22, 0x77, 0x0f, 0x2c, 0xe2, 0xf3, 0x14, 0x3c
};

/* wycheproof - misc */
static const u8 enc_input045[] __initconst = {
	0x02, 0xcd, 0xe1, 0x68, 0xfb, 0xa3, 0xf5, 0x44,
	0xbb, 0xd0, 0x33, 0x2f, 0x7a, 0xde, 0xad, 0xa8
};
static const u8 enc_output045[] __initconst = {
	0x85, 0xf2, 0x9a, 0x71, 0x95, 0x57, 0xcd, 0xd1,
	0x4d, 0x1f, 0x8f, 0xff, 0xab, 0x6d, 0x9e, 0x60,
	0x73, 0x2c, 0xa3, 0x2b, 0xec, 0xd5, 0x15, 0xa1,
	0xed, 0x35, 0x3f, 0x54, 0x2e, 0x99, 0x98, 0x58
};
static const u8 enc_assoc045[] __initconst = {
	0xb6, 0x48
};
static const u8 enc_nonce045[] __initconst = {
	0x87, 0xa3, 0x16, 0x3e, 0xc0, 0x59, 0x8a, 0xd9,
	0x5b, 0x3a, 0xa7, 0x13
};
static const u8 enc_key045[] __initconst = {
	0x64, 0x32, 0x71, 0x7f, 0x1d, 0xb8, 0x5e, 0x41,
	0xac, 0x78, 0x36, 0xbc, 0xe2, 0x51, 0x85, 0xa0,
	0x80, 0xd5, 0x76, 0x2b, 0x9e, 0x2b, 0x18, 0x44,
	0x4b, 0x6e, 0xc7, 0x2c, 0x3b, 0xd8, 0xe4, 0xdc
};

/* wycheproof - misc */
static const u8 enc_input046[] __initconst = {
	0x16, 0xdd, 0xd2, 0x3f, 0xf5, 0x3f, 0x3d, 0x23,
	0xc0, 0x63, 0x34, 0x48, 0x70, 0x40, 0xeb, 0x47
};
static const u8 enc_output046[] __initconst = {
	0xc1, 0xb2, 0x95, 0x93, 0x6d, 0x56, 0xfa, 0xda,
	0xc0, 0x3e, 0x5f, 0x74, 0x2b, 0xff, 0x73, 0xa1,
	0x39, 0xc4, 0x57, 0xdb, 0xab, 0x66, 0x38, 0x2b,
	0xab, 0xb3, 0xb5, 0x58, 0x00, 0xcd, 0xa5, 0xb8
};
static const u8 enc_assoc046[] __initconst = {
	0xbd, 0x4c, 0xd0, 0x2f, 0xc7, 0x50, 0x2b, 0xbd,
	0xbd, 0xf6, 0xc9, 0xa3, 0xcb, 0xe8, 0xf0
};
static const u8 enc_nonce046[] __initconst = {
	0x6f, 0x57, 0x3a, 0xa8, 0x6b, 0xaa, 0x49, 0x2b,
	0xa4, 0x65, 0x96, 0xdf
};
static const u8 enc_key046[] __initconst = {
	0x8e, 0x34, 0xcf, 0x73, 0xd2, 0x45, 0xa1, 0x08,
	0x2a, 0x92, 0x0b, 0x86, 0x36, 0x4e, 0xb8, 0x96,
	0xc4, 0x94, 0x64, 0x67, 0xbc, 0xb3, 0xd5, 0x89,
	0x29, 0xfc, 0xb3, 0x66, 0x90, 0xe6, 0x39, 0x4f
};

/* wycheproof - misc */
static const u8 enc_input047[] __initconst = {
	0x62, 0x3b, 0x78, 0x50, 0xc3, 0x21, 0xe2, 0xcf,
	0x0c, 0x6f, 0xbc, 0xc8, 0xdf, 0xd1, 0xaf, 0xf2
};
static const u8 enc_output047[] __initconst = {
	0xc8, 0x4c, 0x9b, 0xb7, 0xc6, 0x1c, 0x1b, 0xcb,
	0x17, 0x77, 0x2a, 0x1c, 0x50, 0x0c, 0x50, 0x95,
	0xdb, 0xad, 0xf7, 0xa5, 0x13, 0x8c, 0xa0, 0x34,
	0x59, 0xa2, 0xcd, 0x65, 0x83, 0x1e, 0x09, 0x2f
};
static const u8 enc_assoc047[] __initconst = {
	0x89, 0xcc, 0xe9, 0xfb, 0x47, 0x44, 0x1d, 0x07,
	0xe0, 0x24, 0x5a, 0x66, 0xfe, 0x8b, 0x77, 0x8b
};
static const u8 enc_nonce047[] __initconst = {
	0x1a, 0x65, 0x18, 0xf0, 0x2e, 0xde, 0x1d, 0xa6,
	0x80, 0x92, 0x66, 0xd9
};
static const u8 enc_key047[] __initconst = {
	0xcb, 0x55, 0x75, 0xf5, 0xc7, 0xc4, 0x5c, 0x91,
	0xcf, 0x32, 0x0b, 0x13, 0x9f, 0xb5, 0x94, 0x23,
	0x75, 0x60, 0xd0, 0xa3, 0xe6, 0xf8, 0x65, 0xa6,
	0x7d, 0x4f, 0x63, 0x3f, 0x2c, 0x08, 0xf0, 0x16
};

/* wycheproof - misc */
static const u8 enc_input048[] __initconst = {
	0x87, 0xb3, 0xa4, 0xd7, 0xb2, 0x6d, 0x8d, 0x32,
	0x03, 0xa0, 0xde, 0x1d, 0x64, 0xef, 0x82, 0xe3
};
static const u8 enc_output048[] __initconst = {
	0x94, 0xbc, 0x80, 0x62, 0x1e, 0xd1, 0xe7, 0x1b,
	0x1f, 0xd2, 0xb5, 0xc3, 0xa1, 0x5e, 0x35, 0x68,
	0x33, 0x35, 0x11, 0x86, 0x17, 0x96, 0x97, 0x84,
	0x01, 0x59, 0x8b, 0x96, 0x37, 0x22, 0xf5, 0xb3
};
static const u8 enc_assoc048[] __initconst = {
	0xd1, 0x9f, 0x2d, 0x98, 0x90, 0x95, 0xf7, 0xab,
	0x03, 0xa5, 0xfd, 0xe8, 0x44, 0x16, 0xe0, 0x0c,
	0x0e
};
static const u8 enc_nonce048[] __initconst = {
	0x56, 0x4d, 0xee, 0x49, 0xab, 0x00, 0xd2, 0x40,
	0xfc, 0x10, 0x68, 0xc3
};
static const u8 enc_key048[] __initconst = {
	0xa5, 0x56, 0x9e, 0x72, 0x9a, 0x69, 0xb2, 0x4b,
	0xa6, 0xe0, 0xff, 0x15, 0xc4, 0x62, 0x78, 0x97,
	0x43, 0x68, 0x24, 0xc9, 0x41, 0xe9, 0xd0, 0x0b,
	0x2e, 0x93, 0xfd, 0xdc, 0x4b, 0xa7, 0x76, 0x57
};

/* wycheproof - misc */
static const u8 enc_input049[] __initconst = {
	0xe6, 0x01, 0xb3, 0x85, 0x57, 0x79, 0x7d, 0xa2,
	0xf8, 0xa4, 0x10, 0x6a, 0x08, 0x9d, 0x1d, 0xa6
};
static const u8 enc_output049[] __initconst = {
	0x29, 0x9b, 0x5d, 0x3f, 0x3d, 0x03, 0xc0, 0x87,
	0x20, 0x9a, 0x16, 0xe2, 0x85, 0x14, 0x31, 0x11,
	0x4b, 0x45, 0x4e, 0xd1, 0x98, 0xde, 0x11, 0x7e,
	0x83, 0xec, 0x49, 0xfa, 0x8d, 0x85, 0x08, 0xd6
};
static const u8 enc_assoc049[] __initconst = {
	0x5e, 0x64, 0x70, 0xfa, 0xcd, 0x99, 0xc1, 0xd8,
	0x1e, 0x37, 0xcd, 0x44, 0x01, 0x5f, 0xe1, 0x94,
	0x80, 0xa2, 0xa4, 0xd3, 0x35, 0x2a, 0x4f, 0xf5,
	0x60, 0xc0, 0x64, 0x0f, 0xdb, 0xda
};
static const u8 enc_nonce049[] __initconst = {
	0xdf, 0x87, 0x13, 0xe8, 0x7e, 0xc3, 0xdb, 0xcf,
	0xad, 0x14, 0xd5, 0x3e
};
static const u8 enc_key049[] __initconst = {
	0x56, 0x20, 0x74, 0x65, 0xb4, 0xe4, 0x8e, 0x6d,
	0x04, 0x63, 0x0f, 0x4a, 0x42, 0xf3, 0x5c, 0xfc,
	0x16, 0x3a, 0xb2, 0x89, 0xc2, 0x2a, 0x2b, 0x47,
	0x84, 0xf6, 0xf9, 0x29, 0x03, 0x30, 0xbe, 0xe0
};

/* wycheproof - misc */
static const u8 enc_input050[] __initconst = {
	0xdc, 0x9e, 0x9e, 0xaf, 0x11, 0xe3, 0x14, 0x18,
	0x2d, 0xf6, 0xa4, 0xeb, 0xa1, 0x7a, 0xec, 0x9c
};
static const u8 enc_output050[] __initconst = {
	0x60, 0x5b, 0xbf, 0x90, 0xae, 0xb9, 0x74, 0xf6,
	0x60, 0x2b, 0xc7, 0x78, 0x05, 0x6f, 0x0d, 0xca,
	0x38, 0xea, 0x23, 0xd9, 0x90, 0x54, 0xb4, 0x6b,
	0x42, 0xff, 0xe0, 0x04, 0x12, 0x9d, 0x22, 0x04
};
static const u8 enc_assoc050[] __initconst = {
	0xba, 0x44, 0x6f, 0x6f, 0x9a, 0x0c, 0xed, 0x22,
	0x45, 0x0f, 0xeb, 0x10, 0x73, 0x7d, 0x90, 0x07,
	0xfd, 0x69, 0xab, 0xc1, 0x9b, 0x1d, 0x4d, 0x90,
	0x49, 0xa5, 0x55, 0x1e, 0x86, 0xec, 0x2b, 0x37
};
static const u8 enc_nonce050[] __initconst = {
	0x8d, 0xf4, 0xb1, 0x5a, 0x88, 0x8c, 0x33, 0x28,
	0x6a, 0x7b, 0x76, 0x51
};
static const u8 enc_key050[] __initconst = {
	0x39, 0x37, 0x98, 0x6a, 0xf8, 0x6d, 0xaf, 0xc1,
	0xba, 0x0c, 0x46, 0x72, 0xd8, 0xab, 0xc4, 0x6c,
	0x20, 0x70, 0x62, 0x68, 0x2d, 0x9c, 0x26, 0x4a,
	0xb0, 0x6d, 0x6c, 0x58, 0x07, 0x20, 0x51, 0x30
};

/* wycheproof - misc */
static const u8 enc_input051[] __initconst = {
	0x81, 0xce, 0x84, 0xed, 0xe9, 0xb3, 0x58, 0x59,
	0xcc, 0x8c, 0x49, 0xa8, 0xf6, 0xbe, 0x7d, 0xc6
};
static const u8 enc_output051[] __initconst = {
	0x7b, 0x7c, 0xe0, 0xd8, 0x24, 0x80, 0x9a, 0x70,
	0xde, 0x32, 0x56, 0x2c, 0xcf, 0x2c, 0x2b, 0xbd,
	0x15, 0xd4, 0x4a, 0x00, 0xce, 0x0d, 0x19, 0xb4,
	0x23, 0x1f, 0x92, 0x1e, 0x22, 0xbc, 0x0a, 0x43
};
static const u8 enc_assoc051[] __initconst = {
	0xd4, 0x1a, 0x82, 0x8d, 0x5e, 0x71, 0x82, 0x92,
	0x47, 0x02, 0x19, 0x05, 0x40, 0x2e, 0xa2, 0x57,
	0xdc, 0xcb, 0xc3, 0xb8, 0x0f, 0xcd, 0x56, 0x75,
	0x05, 0x6b, 0x68, 0xbb, 0x59, 0xe6, 0x2e, 0x88,
	0x73
};
static const u8 enc_nonce051[] __initconst = {
	0xbe, 0x40, 0xe5, 0xf1, 0xa1, 0x18, 0x17, 0xa0,
	0xa8, 0xfa, 0x89, 0x49
};
static const u8 enc_key051[] __initconst = {
	0x36, 0x37, 0x2a, 0xbc, 0xdb, 0x78, 0xe0, 0x27,
	0x96, 0x46, 0xac, 0x3d, 0x17, 0x6b, 0x96, 0x74,
	0xe9, 0x15, 0x4e, 0xec, 0xf0, 0xd5, 0x46, 0x9c,
	0x65, 0x1e, 0xc7, 0xe1, 0x6b, 0x4c, 0x11, 0x99
};

/* wycheproof - misc */
static const u8 enc_input052[] __initconst = {
	0xa6, 0x67, 0x47, 0xc8, 0x9e, 0x85, 0x7a, 0xf3,
	0xa1, 0x8e, 0x2c, 0x79, 0x50, 0x00, 0x87, 0xed
};
static const u8 enc_output052[] __initconst = {
	0xca, 0x82, 0xbf, 0xf3, 0xe2, 0xf3, 0x10, 0xcc,
	0xc9, 0x76, 0x67, 0x2c, 0x44, 0x15, 0xe6, 0x9b,
	0x57, 0x63, 0x8c, 0x62, 0xa5, 0xd8, 0x5d, 0xed,
	0x77, 0x4f, 0x91, 0x3c, 0x81, 0x3e, 0xa0, 0x32
};
static const u8 enc_assoc052[] __initconst = {
	0x3f, 0x2d, 0xd4, 0x9b, 0xbf, 0x09, 0xd6, 0x9a,
	0x78, 0xa3, 0xd8, 0x0e, 0xa2, 0x56, 0x66, 0x14,
	0xfc, 0x37, 0x94, 0x74, 0x19, 0x6c, 0x1a, 0xae,
	0x84, 0x58, 0x3d, 0xa7, 0x3d, 0x7f, 0xf8, 0x5c,
	0x6f, 0x42, 0xca, 0x42, 0x05, 0x6a, 0x97, 0x92,
	0xcc, 0x1b, 0x9f, 0xb3, 0xc7, 0xd2, 0x61
};
static const u8 enc_nonce052[] __initconst = {
	0x84, 0xc8, 0x7d, 0xae, 0x4e, 0xee, 0x27, 0x73,
	0x0e, 0xc3, 0x5d, 0x12
};
static const u8 enc_key052[] __initconst = {
	0x9f, 0x14, 0x79, 0xed, 0x09, 0x7d, 0x7f, 0xe5,
	0x29, 0xc1, 0x1f, 0x2f, 0x5a, 0xdd, 0x9a, 0xaf,
	0xf4, 0xa1, 0xca, 0x0b, 0x68, 0x99, 0x7a, 0x2c,
	0xb7, 0xf7, 0x97, 0x49, 0xbd, 0x90, 0xaa, 0xf4
};

/* wycheproof - misc */
static const u8 enc_input053[] __initconst = {
	0x25, 0x6d, 0x40, 0x88, 0x80, 0x94, 0x17, 0x83,
	0x55, 0xd3, 0x04, 0x84, 0x64, 0x43, 0xfe, 0xe8,
	0xdf, 0x99, 0x47, 0x03, 0x03, 0xfb, 0x3b, 0x7b,
	0x80, 0xe0, 0x30, 0xbe, 0xeb, 0xd3, 0x29, 0xbe
};
static const u8 enc_output053[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xe6, 0xd3, 0xd7, 0x32, 0x4a, 0x1c, 0xbb, 0xa7,
	0x77, 0xbb, 0xb0, 0xec, 0xdd, 0xa3, 0x78, 0x07
};
static const u8 enc_assoc053[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_nonce053[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key053[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input054[] __initconst = {
	0x25, 0x6d, 0x40, 0x88, 0x80, 0x94, 0x17, 0x83,
	0x55, 0xd3, 0x04, 0x84, 0x64, 0x43, 0xfe, 0xe8,
	0xdf, 0x99, 0x47, 0x03, 0x03, 0xfb, 0x3b, 0x7b,
	0x80, 0xe0, 0x30, 0xbe, 0xeb, 0xd3, 0x29, 0xbe,
	0xe3, 0xbc, 0xdb, 0x5b, 0x1e, 0xde, 0xfc, 0xfe,
	0x8b, 0xcd, 0xa1, 0xb6, 0xa1, 0x5c, 0x8c, 0x2b,
	0x08, 0x69, 0xff, 0xd2, 0xec, 0x5e, 0x26, 0xe5,
	0x53, 0xb7, 0xb2, 0x27, 0xfe, 0x87, 0xfd, 0xbd
};
static const u8 enc_output054[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x06, 0x2d, 0xe6, 0x79, 0x5f, 0x27, 0x4f, 0xd2,
	0xa3, 0x05, 0xd7, 0x69, 0x80, 0xbc, 0x9c, 0xce
};
static const u8 enc_assoc054[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_nonce054[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key054[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input055[] __initconst = {
	0x25, 0x6d, 0x40, 0x88, 0x80, 0x94, 0x17, 0x83,
	0x55, 0xd3, 0x04, 0x84, 0x64, 0x43, 0xfe, 0xe8,
	0xdf, 0x99, 0x47, 0x03, 0x03, 0xfb, 0x3b, 0x7b,
	0x80, 0xe0, 0x30, 0xbe, 0xeb, 0xd3, 0x29, 0xbe,
	0xe3, 0xbc, 0xdb, 0x5b, 0x1e, 0xde, 0xfc, 0xfe,
	0x8b, 0xcd, 0xa1, 0xb6, 0xa1, 0x5c, 0x8c, 0x2b,
	0x08, 0x69, 0xff, 0xd2, 0xec, 0x5e, 0x26, 0xe5,
	0x53, 0xb7, 0xb2, 0x27, 0xfe, 0x87, 0xfd, 0xbd,
	0x7a, 0xda, 0x44, 0x42, 0x42, 0x69, 0xbf, 0xfa,
	0x55, 0x27, 0xf2, 0x70, 0xac, 0xf6, 0x85, 0x02,
	0xb7, 0x4c, 0x5a, 0xe2, 0xe6, 0x0c, 0x05, 0x80,
	0x98, 0x1a, 0x49, 0x38, 0x45, 0x93, 0x92, 0xc4,
	0x9b, 0xb2, 0xf2, 0x84, 0xb6, 0x46, 0xef, 0xc7,
	0xf3, 0xf0, 0xb1, 0x36, 0x1d, 0xc3, 0x48, 0xed,
	0x77, 0xd3, 0x0b, 0xc5, 0x76, 0x92, 0xed, 0x38,
	0xfb, 0xac, 0x01, 0x88, 0x38, 0x04, 0x88, 0xc7
};
static const u8 enc_output055[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xd8, 0xb4, 0x79, 0x02, 0xba, 0xae, 0xaf, 0xb3,
	0x42, 0x03, 0x05, 0x15, 0x29, 0xaf, 0x28, 0x2e
};
static const u8 enc_assoc055[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_nonce055[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key055[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input056[] __initconst = {
	0xda, 0x92, 0xbf, 0x77, 0x7f, 0x6b, 0xe8, 0x7c,
	0xaa, 0x2c, 0xfb, 0x7b, 0x9b, 0xbc, 0x01, 0x17,
	0x20, 0x66, 0xb8, 0xfc, 0xfc, 0x04, 0xc4, 0x84,
	0x7f, 0x1f, 0xcf, 0x41, 0x14, 0x2c, 0xd6, 0x41
};
static const u8 enc_output056[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xb3, 0x89, 0x1c, 0x84, 0x9c, 0xb5, 0x2c, 0x27,
	0x74, 0x7e, 0xdf, 0xcf, 0x31, 0x21, 0x3b, 0xb6
};
static const u8 enc_assoc056[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce056[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key056[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input057[] __initconst = {
	0xda, 0x92, 0xbf, 0x77, 0x7f, 0x6b, 0xe8, 0x7c,
	0xaa, 0x2c, 0xfb, 0x7b, 0x9b, 0xbc, 0x01, 0x17,
	0x20, 0x66, 0xb8, 0xfc, 0xfc, 0x04, 0xc4, 0x84,
	0x7f, 0x1f, 0xcf, 0x41, 0x14, 0x2c, 0xd6, 0x41,
	0x1c, 0x43, 0x24, 0xa4, 0xe1, 0x21, 0x03, 0x01,
	0x74, 0x32, 0x5e, 0x49, 0x5e, 0xa3, 0x73, 0xd4,
	0xf7, 0x96, 0x00, 0x2d, 0x13, 0xa1, 0xd9, 0x1a,
	0xac, 0x48, 0x4d, 0xd8, 0x01, 0x78, 0x02, 0x42
};
static const u8 enc_output057[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xf0, 0xc1, 0x2d, 0x26, 0xef, 0x03, 0x02, 0x9b,
	0x62, 0xc0, 0x08, 0xda, 0x27, 0xc5, 0xdc, 0x68
};
static const u8 enc_assoc057[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce057[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key057[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input058[] __initconst = {
	0xda, 0x92, 0xbf, 0x77, 0x7f, 0x6b, 0xe8, 0x7c,
	0xaa, 0x2c, 0xfb, 0x7b, 0x9b, 0xbc, 0x01, 0x17,
	0x20, 0x66, 0xb8, 0xfc, 0xfc, 0x04, 0xc4, 0x84,
	0x7f, 0x1f, 0xcf, 0x41, 0x14, 0x2c, 0xd6, 0x41,
	0x1c, 0x43, 0x24, 0xa4, 0xe1, 0x21, 0x03, 0x01,
	0x74, 0x32, 0x5e, 0x49, 0x5e, 0xa3, 0x73, 0xd4,
	0xf7, 0x96, 0x00, 0x2d, 0x13, 0xa1, 0xd9, 0x1a,
	0xac, 0x48, 0x4d, 0xd8, 0x01, 0x78, 0x02, 0x42,
	0x85, 0x25, 0xbb, 0xbd, 0xbd, 0x96, 0x40, 0x05,
	0xaa, 0xd8, 0x0d, 0x8f, 0x53, 0x09, 0x7a, 0xfd,
	0x48, 0xb3, 0xa5, 0x1d, 0x19, 0xf3, 0xfa, 0x7f,
	0x67, 0xe5, 0xb6, 0xc7, 0xba, 0x6c, 0x6d, 0x3b,
	0x64, 0x4d, 0x0d, 0x7b, 0x49, 0xb9, 0x10, 0x38,
	0x0c, 0x0f, 0x4e, 0xc9, 0xe2, 0x3c, 0xb7, 0x12,
	0x88, 0x2c, 0xf4, 0x3a, 0x89, 0x6d, 0x12, 0xc7,
	0x04, 0x53, 0xfe, 0x77, 0xc7, 0xfb, 0x77, 0x38
};
static const u8 enc_output058[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xee, 0x65, 0x78, 0x30, 0x01, 0xc2, 0x56, 0x91,
	0xfa, 0x28, 0xd0, 0xf5, 0xf1, 0xc1, 0xd7, 0x62
};
static const u8 enc_assoc058[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce058[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key058[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input059[] __initconst = {
	0x25, 0x6d, 0x40, 0x08, 0x80, 0x94, 0x17, 0x03,
	0x55, 0xd3, 0x04, 0x04, 0x64, 0x43, 0xfe, 0x68,
	0xdf, 0x99, 0x47, 0x83, 0x03, 0xfb, 0x3b, 0xfb,
	0x80, 0xe0, 0x30, 0x3e, 0xeb, 0xd3, 0x29, 0x3e
};
static const u8 enc_output059[] __initconst = {
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x79, 0xba, 0x7a, 0x29, 0xf5, 0xa7, 0xbb, 0x75,
	0x79, 0x7a, 0xf8, 0x7a, 0x61, 0x01, 0x29, 0xa4
};
static const u8 enc_assoc059[] __initconst = {
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80
};
static const u8 enc_nonce059[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key059[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input060[] __initconst = {
	0x25, 0x6d, 0x40, 0x08, 0x80, 0x94, 0x17, 0x03,
	0x55, 0xd3, 0x04, 0x04, 0x64, 0x43, 0xfe, 0x68,
	0xdf, 0x99, 0x47, 0x83, 0x03, 0xfb, 0x3b, 0xfb,
	0x80, 0xe0, 0x30, 0x3e, 0xeb, 0xd3, 0x29, 0x3e,
	0xe3, 0xbc, 0xdb, 0xdb, 0x1e, 0xde, 0xfc, 0x7e,
	0x8b, 0xcd, 0xa1, 0x36, 0xa1, 0x5c, 0x8c, 0xab,
	0x08, 0x69, 0xff, 0x52, 0xec, 0x5e, 0x26, 0x65,
	0x53, 0xb7, 0xb2, 0xa7, 0xfe, 0x87, 0xfd, 0x3d
};
static const u8 enc_output060[] __initconst = {
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x36, 0xb1, 0x74, 0x38, 0x19, 0xe1, 0xb9, 0xba,
	0x15, 0x51, 0xe8, 0xed, 0x92, 0x2a, 0x95, 0x9a
};
static const u8 enc_assoc060[] __initconst = {
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80
};
static const u8 enc_nonce060[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key060[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input061[] __initconst = {
	0x25, 0x6d, 0x40, 0x08, 0x80, 0x94, 0x17, 0x03,
	0x55, 0xd3, 0x04, 0x04, 0x64, 0x43, 0xfe, 0x68,
	0xdf, 0x99, 0x47, 0x83, 0x03, 0xfb, 0x3b, 0xfb,
	0x80, 0xe0, 0x30, 0x3e, 0xeb, 0xd3, 0x29, 0x3e,
	0xe3, 0xbc, 0xdb, 0xdb, 0x1e, 0xde, 0xfc, 0x7e,
	0x8b, 0xcd, 0xa1, 0x36, 0xa1, 0x5c, 0x8c, 0xab,
	0x08, 0x69, 0xff, 0x52, 0xec, 0x5e, 0x26, 0x65,
	0x53, 0xb7, 0xb2, 0xa7, 0xfe, 0x87, 0xfd, 0x3d,
	0x7a, 0xda, 0x44, 0xc2, 0x42, 0x69, 0xbf, 0x7a,
	0x55, 0x27, 0xf2, 0xf0, 0xac, 0xf6, 0x85, 0x82,
	0xb7, 0x4c, 0x5a, 0x62, 0xe6, 0x0c, 0x05, 0x00,
	0x98, 0x1a, 0x49, 0xb8, 0x45, 0x93, 0x92, 0x44,
	0x9b, 0xb2, 0xf2, 0x04, 0xb6, 0x46, 0xef, 0x47,
	0xf3, 0xf0, 0xb1, 0xb6, 0x1d, 0xc3, 0x48, 0x6d,
	0x77, 0xd3, 0x0b, 0x45, 0x76, 0x92, 0xed, 0xb8,
	0xfb, 0xac, 0x01, 0x08, 0x38, 0x04, 0x88, 0x47
};
static const u8 enc_output061[] __initconst = {
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0xfe, 0xac, 0x49, 0x55, 0x55, 0x4e, 0x80, 0x6f,
	0x3a, 0x19, 0x02, 0xe2, 0x44, 0x32, 0xc0, 0x8a
};
static const u8 enc_assoc061[] __initconst = {
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80
};
static const u8 enc_nonce061[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key061[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input062[] __initconst = {
	0xda, 0x92, 0xbf, 0xf7, 0x7f, 0x6b, 0xe8, 0xfc,
	0xaa, 0x2c, 0xfb, 0xfb, 0x9b, 0xbc, 0x01, 0x97,
	0x20, 0x66, 0xb8, 0x7c, 0xfc, 0x04, 0xc4, 0x04,
	0x7f, 0x1f, 0xcf, 0xc1, 0x14, 0x2c, 0xd6, 0xc1
};
static const u8 enc_output062[] __initconst = {
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0x20, 0xa3, 0x79, 0x8d, 0xf1, 0x29, 0x2c, 0x59,
	0x72, 0xbf, 0x97, 0x41, 0xae, 0xc3, 0x8a, 0x19
};
static const u8 enc_assoc062[] __initconst = {
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f
};
static const u8 enc_nonce062[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key062[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input063[] __initconst = {
	0xda, 0x92, 0xbf, 0xf7, 0x7f, 0x6b, 0xe8, 0xfc,
	0xaa, 0x2c, 0xfb, 0xfb, 0x9b, 0xbc, 0x01, 0x97,
	0x20, 0x66, 0xb8, 0x7c, 0xfc, 0x04, 0xc4, 0x04,
	0x7f, 0x1f, 0xcf, 0xc1, 0x14, 0x2c, 0xd6, 0xc1,
	0x1c, 0x43, 0x24, 0x24, 0xe1, 0x21, 0x03, 0x81,
	0x74, 0x32, 0x5e, 0xc9, 0x5e, 0xa3, 0x73, 0x54,
	0xf7, 0x96, 0x00, 0xad, 0x13, 0xa1, 0xd9, 0x9a,
	0xac, 0x48, 0x4d, 0x58, 0x01, 0x78, 0x02, 0xc2
};
static const u8 enc_output063[] __initconst = {
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xc0, 0x3d, 0x9f, 0x67, 0x35, 0x4a, 0x97, 0xb2,
	0xf0, 0x74, 0xf7, 0x55, 0x15, 0x57, 0xe4, 0x9c
};
static const u8 enc_assoc063[] __initconst = {
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f
};
static const u8 enc_nonce063[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key063[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input064[] __initconst = {
	0xda, 0x92, 0xbf, 0xf7, 0x7f, 0x6b, 0xe8, 0xfc,
	0xaa, 0x2c, 0xfb, 0xfb, 0x9b, 0xbc, 0x01, 0x97,
	0x20, 0x66, 0xb8, 0x7c, 0xfc, 0x04, 0xc4, 0x04,
	0x7f, 0x1f, 0xcf, 0xc1, 0x14, 0x2c, 0xd6, 0xc1,
	0x1c, 0x43, 0x24, 0x24, 0xe1, 0x21, 0x03, 0x81,
	0x74, 0x32, 0x5e, 0xc9, 0x5e, 0xa3, 0x73, 0x54,
	0xf7, 0x96, 0x00, 0xad, 0x13, 0xa1, 0xd9, 0x9a,
	0xac, 0x48, 0x4d, 0x58, 0x01, 0x78, 0x02, 0xc2,
	0x85, 0x25, 0xbb, 0x3d, 0xbd, 0x96, 0x40, 0x85,
	0xaa, 0xd8, 0x0d, 0x0f, 0x53, 0x09, 0x7a, 0x7d,
	0x48, 0xb3, 0xa5, 0x9d, 0x19, 0xf3, 0xfa, 0xff,
	0x67, 0xe5, 0xb6, 0x47, 0xba, 0x6c, 0x6d, 0xbb,
	0x64, 0x4d, 0x0d, 0xfb, 0x49, 0xb9, 0x10, 0xb8,
	0x0c, 0x0f, 0x4e, 0x49, 0xe2, 0x3c, 0xb7, 0x92,
	0x88, 0x2c, 0xf4, 0xba, 0x89, 0x6d, 0x12, 0x47,
	0x04, 0x53, 0xfe, 0xf7, 0xc7, 0xfb, 0x77, 0xb8
};
static const u8 enc_output064[] __initconst = {
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xc8, 0x6d, 0xa8, 0xdd, 0x65, 0x22, 0x86, 0xd5,
	0x02, 0x13, 0xd3, 0x28, 0xd6, 0x3e, 0x40, 0x06
};
static const u8 enc_assoc064[] __initconst = {
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f
};
static const u8 enc_nonce064[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key064[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input065[] __initconst = {
	0x5a, 0x92, 0xbf, 0x77, 0xff, 0x6b, 0xe8, 0x7c,
	0x2a, 0x2c, 0xfb, 0x7b, 0x1b, 0xbc, 0x01, 0x17,
	0xa0, 0x66, 0xb8, 0xfc, 0x7c, 0x04, 0xc4, 0x84,
	0xff, 0x1f, 0xcf, 0x41, 0x94, 0x2c, 0xd6, 0x41
};
static const u8 enc_output065[] __initconst = {
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0xbe, 0xde, 0x90, 0x83, 0xce, 0xb3, 0x6d, 0xdf,
	0xe5, 0xfa, 0x81, 0x1f, 0x95, 0x47, 0x1c, 0x67
};
static const u8 enc_assoc065[] __initconst = {
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff
};
static const u8 enc_nonce065[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key065[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input066[] __initconst = {
	0x5a, 0x92, 0xbf, 0x77, 0xff, 0x6b, 0xe8, 0x7c,
	0x2a, 0x2c, 0xfb, 0x7b, 0x1b, 0xbc, 0x01, 0x17,
	0xa0, 0x66, 0xb8, 0xfc, 0x7c, 0x04, 0xc4, 0x84,
	0xff, 0x1f, 0xcf, 0x41, 0x94, 0x2c, 0xd6, 0x41,
	0x9c, 0x43, 0x24, 0xa4, 0x61, 0x21, 0x03, 0x01,
	0xf4, 0x32, 0x5e, 0x49, 0xde, 0xa3, 0x73, 0xd4,
	0x77, 0x96, 0x00, 0x2d, 0x93, 0xa1, 0xd9, 0x1a,
	0x2c, 0x48, 0x4d, 0xd8, 0x81, 0x78, 0x02, 0x42
};
static const u8 enc_output066[] __initconst = {
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x30, 0x08, 0x74, 0xbb, 0x06, 0x92, 0xb6, 0x89,
	0xde, 0xad, 0x9a, 0xe1, 0x5b, 0x06, 0x73, 0x90
};
static const u8 enc_assoc066[] __initconst = {
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff
};
static const u8 enc_nonce066[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key066[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input067[] __initconst = {
	0x5a, 0x92, 0xbf, 0x77, 0xff, 0x6b, 0xe8, 0x7c,
	0x2a, 0x2c, 0xfb, 0x7b, 0x1b, 0xbc, 0x01, 0x17,
	0xa0, 0x66, 0xb8, 0xfc, 0x7c, 0x04, 0xc4, 0x84,
	0xff, 0x1f, 0xcf, 0x41, 0x94, 0x2c, 0xd6, 0x41,
	0x9c, 0x43, 0x24, 0xa4, 0x61, 0x21, 0x03, 0x01,
	0xf4, 0x32, 0x5e, 0x49, 0xde, 0xa3, 0x73, 0xd4,
	0x77, 0x96, 0x00, 0x2d, 0x93, 0xa1, 0xd9, 0x1a,
	0x2c, 0x48, 0x4d, 0xd8, 0x81, 0x78, 0x02, 0x42,
	0x05, 0x25, 0xbb, 0xbd, 0x3d, 0x96, 0x40, 0x05,
	0x2a, 0xd8, 0x0d, 0x8f, 0xd3, 0x09, 0x7a, 0xfd,
	0xc8, 0xb3, 0xa5, 0x1d, 0x99, 0xf3, 0xfa, 0x7f,
	0xe7, 0xe5, 0xb6, 0xc7, 0x3a, 0x6c, 0x6d, 0x3b,
	0xe4, 0x4d, 0x0d, 0x7b, 0xc9, 0xb9, 0x10, 0x38,
	0x8c, 0x0f, 0x4e, 0xc9, 0x62, 0x3c, 0xb7, 0x12,
	0x08, 0x2c, 0xf4, 0x3a, 0x09, 0x6d, 0x12, 0xc7,
	0x84, 0x53, 0xfe, 0x77, 0x47, 0xfb, 0x77, 0x38
};
static const u8 enc_output067[] __initconst = {
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x99, 0xca, 0xd8, 0x5f, 0x45, 0xca, 0x40, 0x94,
	0x2d, 0x0d, 0x4d, 0x5e, 0x95, 0x0a, 0xde, 0x22
};
static const u8 enc_assoc067[] __initconst = {
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
	0x7f, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff
};
static const u8 enc_nonce067[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key067[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input068[] __initconst = {
	0x25, 0x6d, 0x40, 0x88, 0x7f, 0x6b, 0xe8, 0x7c,
	0x55, 0xd3, 0x04, 0x84, 0x9b, 0xbc, 0x01, 0x17,
	0xdf, 0x99, 0x47, 0x03, 0xfc, 0x04, 0xc4, 0x84,
	0x80, 0xe0, 0x30, 0xbe, 0x14, 0x2c, 0xd6, 0x41
};
static const u8 enc_output068[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x8b, 0xbe, 0x14, 0x52, 0x72, 0xe7, 0xc2, 0xd9,
	0xa1, 0x89, 0x1a, 0x3a, 0xb0, 0x98, 0x3d, 0x9d
};
static const u8 enc_assoc068[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce068[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key068[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input069[] __initconst = {
	0x25, 0x6d, 0x40, 0x88, 0x7f, 0x6b, 0xe8, 0x7c,
	0x55, 0xd3, 0x04, 0x84, 0x9b, 0xbc, 0x01, 0x17,
	0xdf, 0x99, 0x47, 0x03, 0xfc, 0x04, 0xc4, 0x84,
	0x80, 0xe0, 0x30, 0xbe, 0x14, 0x2c, 0xd6, 0x41,
	0xe3, 0xbc, 0xdb, 0x5b, 0xe1, 0x21, 0x03, 0x01,
	0x8b, 0xcd, 0xa1, 0xb6, 0x5e, 0xa3, 0x73, 0xd4,
	0x08, 0x69, 0xff, 0xd2, 0x13, 0xa1, 0xd9, 0x1a,
	0x53, 0xb7, 0xb2, 0x27, 0x01, 0x78, 0x02, 0x42
};
static const u8 enc_output069[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x3b, 0x41, 0x86, 0x19, 0x13, 0xa8, 0xf6, 0xde,
	0x7f, 0x61, 0xe2, 0x25, 0x63, 0x1b, 0xc3, 0x82
};
static const u8 enc_assoc069[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce069[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key069[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input070[] __initconst = {
	0x25, 0x6d, 0x40, 0x88, 0x7f, 0x6b, 0xe8, 0x7c,
	0x55, 0xd3, 0x04, 0x84, 0x9b, 0xbc, 0x01, 0x17,
	0xdf, 0x99, 0x47, 0x03, 0xfc, 0x04, 0xc4, 0x84,
	0x80, 0xe0, 0x30, 0xbe, 0x14, 0x2c, 0xd6, 0x41,
	0xe3, 0xbc, 0xdb, 0x5b, 0xe1, 0x21, 0x03, 0x01,
	0x8b, 0xcd, 0xa1, 0xb6, 0x5e, 0xa3, 0x73, 0xd4,
	0x08, 0x69, 0xff, 0xd2, 0x13, 0xa1, 0xd9, 0x1a,
	0x53, 0xb7, 0xb2, 0x27, 0x01, 0x78, 0x02, 0x42,
	0x7a, 0xda, 0x44, 0x42, 0xbd, 0x96, 0x40, 0x05,
	0x55, 0x27, 0xf2, 0x70, 0x53, 0x09, 0x7a, 0xfd,
	0xb7, 0x4c, 0x5a, 0xe2, 0x19, 0xf3, 0xfa, 0x7f,
	0x98, 0x1a, 0x49, 0x38, 0xba, 0x6c, 0x6d, 0x3b,
	0x9b, 0xb2, 0xf2, 0x84, 0x49, 0xb9, 0x10, 0x38,
	0xf3, 0xf0, 0xb1, 0x36, 0xe2, 0x3c, 0xb7, 0x12,
	0x77, 0xd3, 0x0b, 0xc5, 0x89, 0x6d, 0x12, 0xc7,
	0xfb, 0xac, 0x01, 0x88, 0xc7, 0xfb, 0x77, 0x38
};
static const u8 enc_output070[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x84, 0x28, 0xbc, 0xf0, 0x23, 0xec, 0x6b, 0xf3,
	0x1f, 0xd9, 0xef, 0xb2, 0x03, 0xff, 0x08, 0x71
};
static const u8 enc_assoc070[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce070[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key070[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input071[] __initconst = {
	0xda, 0x92, 0xbf, 0x77, 0x80, 0x94, 0x17, 0x83,
	0xaa, 0x2c, 0xfb, 0x7b, 0x64, 0x43, 0xfe, 0xe8,
	0x20, 0x66, 0xb8, 0xfc, 0x03, 0xfb, 0x3b, 0x7b,
	0x7f, 0x1f, 0xcf, 0x41, 0xeb, 0xd3, 0x29, 0xbe
};
static const u8 enc_output071[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0x13, 0x9f, 0xdf, 0x64, 0x74, 0xea, 0x24, 0xf5,
	0x49, 0xb0, 0x75, 0x82, 0x5f, 0x2c, 0x76, 0x20
};
static const u8 enc_assoc071[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_nonce071[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key071[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input072[] __initconst = {
	0xda, 0x92, 0xbf, 0x77, 0x80, 0x94, 0x17, 0x83,
	0xaa, 0x2c, 0xfb, 0x7b, 0x64, 0x43, 0xfe, 0xe8,
	0x20, 0x66, 0xb8, 0xfc, 0x03, 0xfb, 0x3b, 0x7b,
	0x7f, 0x1f, 0xcf, 0x41, 0xeb, 0xd3, 0x29, 0xbe,
	0x1c, 0x43, 0x24, 0xa4, 0x1e, 0xde, 0xfc, 0xfe,
	0x74, 0x32, 0x5e, 0x49, 0xa1, 0x5c, 0x8c, 0x2b,
	0xf7, 0x96, 0x00, 0x2d, 0xec, 0x5e, 0x26, 0xe5,
	0xac, 0x48, 0x4d, 0xd8, 0xfe, 0x87, 0xfd, 0xbd
};
static const u8 enc_output072[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xbb, 0xad, 0x8d, 0x86, 0x3b, 0x83, 0x5a, 0x8e,
	0x86, 0x64, 0xfd, 0x1d, 0x45, 0x66, 0xb6, 0xb4
};
static const u8 enc_assoc072[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_nonce072[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key072[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - misc */
static const u8 enc_input073[] __initconst = {
	0xda, 0x92, 0xbf, 0x77, 0x80, 0x94, 0x17, 0x83,
	0xaa, 0x2c, 0xfb, 0x7b, 0x64, 0x43, 0xfe, 0xe8,
	0x20, 0x66, 0xb8, 0xfc, 0x03, 0xfb, 0x3b, 0x7b,
	0x7f, 0x1f, 0xcf, 0x41, 0xeb, 0xd3, 0x29, 0xbe,
	0x1c, 0x43, 0x24, 0xa4, 0x1e, 0xde, 0xfc, 0xfe,
	0x74, 0x32, 0x5e, 0x49, 0xa1, 0x5c, 0x8c, 0x2b,
	0xf7, 0x96, 0x00, 0x2d, 0xec, 0x5e, 0x26, 0xe5,
	0xac, 0x48, 0x4d, 0xd8, 0xfe, 0x87, 0xfd, 0xbd,
	0x85, 0x25, 0xbb, 0xbd, 0x42, 0x69, 0xbf, 0xfa,
	0xaa, 0xd8, 0x0d, 0x8f, 0xac, 0xf6, 0x85, 0x02,
	0x48, 0xb3, 0xa5, 0x1d, 0xe6, 0x0c, 0x05, 0x80,
	0x67, 0xe5, 0xb6, 0xc7, 0x45, 0x93, 0x92, 0xc4,
	0x64, 0x4d, 0x0d, 0x7b, 0xb6, 0x46, 0xef, 0xc7,
	0x0c, 0x0f, 0x4e, 0xc9, 0x1d, 0xc3, 0x48, 0xed,
	0x88, 0x2c, 0xf4, 0x3a, 0x76, 0x92, 0xed, 0x38,
	0x04, 0x53, 0xfe, 0x77, 0x38, 0x04, 0x88, 0xc7
};
static const u8 enc_output073[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0x42, 0xf2, 0x35, 0x42, 0x97, 0x84, 0x9a, 0x51,
	0x1d, 0x53, 0xe5, 0x57, 0x17, 0x72, 0xf7, 0x1f
};
static const u8 enc_assoc073[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_nonce073[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0xee, 0x32, 0x00
};
static const u8 enc_key073[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - checking for int overflows */
static const u8 enc_input074[] __initconst = {
	0xd4, 0x50, 0x0b, 0xf0, 0x09, 0x49, 0x35, 0x51,
	0xc3, 0x80, 0xad, 0xf5, 0x2c, 0x57, 0x3a, 0x69,
	0xdf, 0x7e, 0x8b, 0x76, 0x24, 0x63, 0x33, 0x0f,
	0xac, 0xc1, 0x6a, 0x57, 0x26, 0xbe, 0x71, 0x90,
	0xc6, 0x3c, 0x5a, 0x1c, 0x92, 0x65, 0x84, 0xa0,
	0x96, 0x75, 0x68, 0x28, 0xdc, 0xdc, 0x64, 0xac,
	0xdf, 0x96, 0x3d, 0x93, 0x1b, 0xf1, 0xda, 0xe2,
	0x38, 0xf3, 0xf1, 0x57, 0x22, 0x4a, 0xc4, 0xb5,
	0x42, 0xd7, 0x85, 0xb0, 0xdd, 0x84, 0xdb, 0x6b,
	0xe3, 0xbc, 0x5a, 0x36, 0x63, 0xe8, 0x41, 0x49,
	0xff, 0xbe, 0xd0, 0x9e, 0x54, 0xf7, 0x8f, 0x16,
	0xa8, 0x22, 0x3b, 0x24, 0xcb, 0x01, 0x9f, 0x58,
	0xb2, 0x1b, 0x0e, 0x55, 0x1e, 0x7a, 0xa0, 0x73,
	0x27, 0x62, 0x95, 0x51, 0x37, 0x6c, 0xcb, 0xc3,
	0x93, 0x76, 0x71, 0xa0, 0x62, 0x9b, 0xd9, 0x5c,
	0x99, 0x15, 0xc7, 0x85, 0x55, 0x77, 0x1e, 0x7a
};
static const u8 enc_output074[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x0b, 0x30, 0x0d, 0x8d, 0xa5, 0x6c, 0x21, 0x85,
	0x75, 0x52, 0x79, 0x55, 0x3c, 0x4c, 0x82, 0xca
};
static const u8 enc_assoc074[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce074[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x00, 0x02, 0x50, 0x6e
};
static const u8 enc_key074[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input075[] __initconst = {
	0x7d, 0xe8, 0x7f, 0x67, 0x29, 0x94, 0x52, 0x75,
	0xd0, 0x65, 0x5d, 0xa4, 0xc7, 0xfd, 0xe4, 0x56,
	0x9e, 0x16, 0xf1, 0x11, 0xb5, 0xeb, 0x26, 0xc2,
	0x2d, 0x85, 0x9e, 0x3f, 0xf8, 0x22, 0xec, 0xed,
	0x3a, 0x6d, 0xd9, 0xa6, 0x0f, 0x22, 0x95, 0x7f,
	0x7b, 0x7c, 0x85, 0x7e, 0x88, 0x22, 0xeb, 0x9f,
	0xe0, 0xb8, 0xd7, 0x02, 0x21, 0x41, 0xf2, 0xd0,
	0xb4, 0x8f, 0x4b, 0x56, 0x12, 0xd3, 0x22, 0xa8,
	0x8d, 0xd0, 0xfe, 0x0b, 0x4d, 0x91, 0x79, 0x32,
	0x4f, 0x7c, 0x6c, 0x9e, 0x99, 0x0e, 0xfb, 0xd8,
	0x0e, 0x5e, 0xd6, 0x77, 0x58, 0x26, 0x49, 0x8b,
	0x1e, 0xfe, 0x0f, 0x71, 0xa0, 0xf3, 0xec, 0x5b,
	0x29, 0xcb, 0x28, 0xc2, 0x54, 0x0a, 0x7d, 0xcd,
	0x51, 0xb7, 0xda, 0xae, 0xe0, 0xff, 0x4a, 0x7f,
	0x3a, 0xc1, 0xee, 0x54, 0xc2, 0x9e, 0xe4, 0xc1,
	0x70, 0xde, 0x40, 0x8f, 0x66, 0x69, 0x21, 0x94
};
static const u8 enc_output075[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xc5, 0x78, 0xe2, 0xaa, 0x44, 0xd3, 0x09, 0xb7,
	0xb6, 0xa5, 0x19, 0x3b, 0xdc, 0x61, 0x18, 0xf5
};
static const u8 enc_assoc075[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce075[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x00, 0x03, 0x18, 0xa5
};
static const u8 enc_key075[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input076[] __initconst = {
	0x1b, 0x99, 0x6f, 0x9a, 0x3c, 0xcc, 0x67, 0x85,
	0xde, 0x22, 0xff, 0x5b, 0x8a, 0xdd, 0x95, 0x02,
	0xce, 0x03, 0xa0, 0xfa, 0xf5, 0x99, 0x2a, 0x09,
	0x52, 0x2c, 0xdd, 0x12, 0x06, 0xd2, 0x20, 0xb8,
	0xf8, 0xbd, 0x07, 0xd1, 0xf1, 0xf5, 0xa1, 0xbd,
	0x9a, 0x71, 0xd1, 0x1c, 0x7f, 0x57, 0x9b, 0x85,
	0x58, 0x18, 0xc0, 0x8d, 0x4d, 0xe0, 0x36, 0x39,
	0x31, 0x83, 0xb7, 0xf5, 0x90, 0xb3, 0x35, 0xae,
	0xd8, 0xde, 0x5b, 0x57, 0xb1, 0x3c, 0x5f, 0xed,
	0xe2, 0x44, 0x1c, 0x3e, 0x18, 0x4a, 0xa9, 0xd4,
	0x6e, 0x61, 0x59, 0x85, 0x06, 0xb3, 0xe1, 0x1c,
	0x43, 0xc6, 0x2c, 0xbc, 0xac, 0xec, 0xed, 0x33,
	0x19, 0x08, 0x75, 0xb0, 0x12, 0x21, 0x8b, 0x19,
	0x30, 0xfb, 0x7c, 0x38, 0xec, 0x45, 0xac, 0x11,
	0xc3, 0x53, 0xd0, 0xcf, 0x93, 0x8d, 0xcc, 0xb9,
	0xef, 0xad, 0x8f, 0xed, 0xbe, 0x46, 0xda, 0xa5
};
static const u8 enc_output076[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x4b, 0x0b, 0xda, 0x8a, 0xd0, 0x43, 0x83, 0x0d,
	0x83, 0x19, 0xab, 0x82, 0xc5, 0x0c, 0x76, 0x63
};
static const u8 enc_assoc076[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce076[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xb4, 0xf0
};
static const u8 enc_key076[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input077[] __initconst = {
	0x86, 0xcb, 0xac, 0xae, 0x4d, 0x3f, 0x74, 0xae,
	0x01, 0x21, 0x3e, 0x05, 0x51, 0xcc, 0x15, 0x16,
	0x0e, 0xa1, 0xbe, 0x84, 0x08, 0xe3, 0xd5, 0xd7,
	0x4f, 0x01, 0x46, 0x49, 0x95, 0xa6, 0x9e, 0x61,
	0x76, 0xcb, 0x9e, 0x02, 0xb2, 0x24, 0x7e, 0xd2,
	0x99, 0x89, 0x2f, 0x91, 0x82, 0xa4, 0x5c, 0xaf,
	0x4c, 0x69, 0x40, 0x56, 0x11, 0x76, 0x6e, 0xdf,
	0xaf, 0xdc, 0x28, 0x55, 0x19, 0xea, 0x30, 0x48,
	0x0c, 0x44, 0xf0, 0x5e, 0x78, 0x1e, 0xac, 0xf8,
	0xfc, 0xec, 0xc7, 0x09, 0x0a, 0xbb, 0x28, 0xfa,
	0x5f, 0xd5, 0x85, 0xac, 0x8c, 0xda, 0x7e, 0x87,
	0x72, 0xe5, 0x94, 0xe4, 0xce, 0x6c, 0x88, 0x32,
	0x81, 0x93, 0x2e, 0x0f, 0x89, 0xf8, 0x77, 0xa1,
	0xf0, 0x4d, 0x9c, 0x32, 0xb0, 0x6c, 0xf9, 0x0b,
	0x0e, 0x76, 0x2b, 0x43, 0x0c, 0x4d, 0x51, 0x7c,
	0x97, 0x10, 0x70, 0x68, 0xf4, 0x98, 0xef, 0x7f
};
static const u8 enc_output077[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x4b, 0xc9, 0x8f, 0x72, 0xc4, 0x94, 0xc2, 0xa4,
	0x3c, 0x2b, 0x15, 0xa1, 0x04, 0x3f, 0x1c, 0xfa
};
static const u8 enc_assoc077[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce077[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xfb, 0x66
};
static const u8 enc_key077[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input078[] __initconst = {
	0xfa, 0xb1, 0xcd, 0xdf, 0x4f, 0xe1, 0x98, 0xef,
	0x63, 0xad, 0xd8, 0x81, 0xd6, 0xea, 0xd6, 0xc5,
	0x76, 0x37, 0xbb, 0xe9, 0x20, 0x18, 0xca, 0x7c,
	0x0b, 0x96, 0xfb, 0xa0, 0x87, 0x1e, 0x93, 0x2d,
	0xb1, 0xfb, 0xf9, 0x07, 0x61, 0xbe, 0x25, 0xdf,
	0x8d, 0xfa, 0xf9, 0x31, 0xce, 0x57, 0x57, 0xe6,
	0x17, 0xb3, 0xd7, 0xa9, 0xf0, 0xbf, 0x0f, 0xfe,
	0x5d, 0x59, 0x1a, 0x33, 0xc1, 0x43, 0xb8, 0xf5,
	0x3f, 0xd0, 0xb5, 0xa1, 0x96, 0x09, 0xfd, 0x62,
	0xe5, 0xc2, 0x51, 0xa4, 0x28, 0x1a, 0x20, 0x0c,
	0xfd, 0xc3, 0x4f, 0x28, 0x17, 0x10, 0x40, 0x6f,
	0x4e, 0x37, 0x62, 0x54, 0x46, 0xff, 0x6e, 0xf2,
	0x24, 0x91, 0x3d, 0xeb, 0x0d, 0x89, 0xaf, 0x33,
	0x71, 0x28, 0xe3, 0xd1, 0x55, 0xd1, 0x6d, 0x3e,
	0xc3, 0x24, 0x60, 0x41, 0x43, 0x21, 0x43, 0xe9,
	0xab, 0x3a, 0x6d, 0x2c, 0xcc, 0x2f, 0x4d, 0x62
};
static const u8 enc_output078[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xf7, 0xe9, 0xe1, 0x51, 0xb0, 0x25, 0x33, 0xc7,
	0x46, 0x58, 0xbf, 0xc7, 0x73, 0x7c, 0x68, 0x0d
};
static const u8 enc_assoc078[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce078[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0xbb, 0x90
};
static const u8 enc_key078[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input079[] __initconst = {
	0x22, 0x72, 0x02, 0xbe, 0x7f, 0x35, 0x15, 0xe9,
	0xd1, 0xc0, 0x2e, 0xea, 0x2f, 0x19, 0x50, 0xb6,
	0x48, 0x1b, 0x04, 0x8a, 0x4c, 0x91, 0x50, 0x6c,
	0xb4, 0x0d, 0x50, 0x4e, 0x6c, 0x94, 0x9f, 0x82,
	0xd1, 0x97, 0xc2, 0x5a, 0xd1, 0x7d, 0xc7, 0x21,
	0x65, 0x11, 0x25, 0x78, 0x2a, 0xc7, 0xa7, 0x12,
	0x47, 0xfe, 0xae, 0xf3, 0x2f, 0x1f, 0x25, 0x0c,
	0xe4, 0xbb, 0x8f, 0x79, 0xac, 0xaa, 0x17, 0x9d,
	0x45, 0xa7, 0xb0, 0x54, 0x5f, 0x09, 0x24, 0x32,
	0x5e, 0xfa, 0x87, 0xd5, 0xe4, 0x41, 0xd2, 0x84,
	0x78, 0xc6, 0x1f, 0x22, 0x23, 0xee, 0x67, 0xc3,
	0xb4, 0x1f, 0x43, 0x94, 0x53, 0x5e, 0x2a, 0x24,
	0x36, 0x9a, 0x2e, 0x16, 0x61, 0x3c, 0x45, 0x94,
	0x90, 0xc1, 0x4f, 0xb1, 0xd7, 0x55, 0xfe, 0x53,
	0xfb, 0xe1, 0xee, 0x45, 0xb1, 0xb2, 0x1f, 0x71,
	0x62, 0xe2, 0xfc, 0xaa, 0x74, 0x2a, 0xbe, 0xfd
};
static const u8 enc_output079[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x79, 0x5b, 0xcf, 0xf6, 0x47, 0xc5, 0x53, 0xc2,
	0xe4, 0xeb, 0x6e, 0x0e, 0xaf, 0xd9, 0xe0, 0x4e
};
static const u8 enc_assoc079[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce079[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x48, 0x4a
};
static const u8 enc_key079[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input080[] __initconst = {
	0xfa, 0xe5, 0x83, 0x45, 0xc1, 0x6c, 0xb0, 0xf5,
	0xcc, 0x53, 0x7f, 0x2b, 0x1b, 0x34, 0x69, 0xc9,
	0x69, 0x46, 0x3b, 0x3e, 0xa7, 0x1b, 0xcf, 0x6b,
	0x98, 0xd6, 0x69, 0xa8, 0xe6, 0x0e, 0x04, 0xfc,
	0x08, 0xd5, 0xfd, 0x06, 0x9c, 0x36, 0x26, 0x38,
	0xe3, 0x40, 0x0e, 0xf4, 0xcb, 0x24, 0x2e, 0x27,
	0xe2, 0x24, 0x5e, 0x68, 0xcb, 0x9e, 0xc5, 0x83,
	0xda, 0x53, 0x40, 0xb1, 0x2e, 0xdf, 0x42, 0x3b,
	0x73, 0x26, 0xad, 0x20, 0xfe, 0xeb, 0x57, 0xda,
	0xca, 0x2e, 0x04, 0x67, 0xa3, 0x28, 0x99, 0xb4,
	0x2d, 0xf8, 0xe5, 0x6d, 0x84, 0xe0, 0x06, 0xbc,
	0x8a, 0x7a, 0xcc, 0x73, 0x1e, 0x7c, 0x1f, 0x6b,
	0xec, 0xb5, 0x71, 0x9f, 0x70, 0x77, 0xf0, 0xd4,
	0xf4, 0xc6, 0x1a, 0xb1, 0x1e, 0xba, 0xc1, 0x00,
	0x18, 0x01, 0xce, 0x33, 0xc4, 0xe4, 0xa7, 0x7d,
	0x83, 0x1d, 0x3c, 0xe3, 0x4e, 0x84, 0x10, 0xe1
};
static const u8 enc_output080[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x19, 0x46, 0xd6, 0x53, 0x96, 0x0f, 0x94, 0x7a,
	0x74, 0xd3, 0xe8, 0x09, 0x3c, 0xf4, 0x85, 0x02
};
static const u8 enc_assoc080[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce080[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x93, 0x2f, 0x40
};
static const u8 enc_key080[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input081[] __initconst = {
	0xeb, 0xb2, 0x16, 0xdd, 0xd7, 0xca, 0x70, 0x92,
	0x15, 0xf5, 0x03, 0xdf, 0x9c, 0xe6, 0x3c, 0x5c,
	0xd2, 0x19, 0x4e, 0x7d, 0x90, 0x99, 0xe8, 0xa9,
	0x0b, 0x2a, 0xfa, 0xad, 0x5e, 0xba, 0x35, 0x06,
	0x99, 0x25, 0xa6, 0x03, 0xfd, 0xbc, 0x34, 0x1a,
	0xae, 0xd4, 0x15, 0x05, 0xb1, 0x09, 0x41, 0xfa,
	0x38, 0x56, 0xa7, 0xe2, 0x47, 0xb1, 0x04, 0x07,
	0x09, 0x74, 0x6c, 0xfc, 0x20, 0x96, 0xca, 0xa6,
	0x31, 0xb2, 0xff, 0xf4, 0x1c, 0x25, 0x05, 0x06,
	0xd8, 0x89, 0xc1, 0xc9, 0x06, 0x71, 0xad, 0xe8,
	0x53, 0xee, 0x63, 0x94, 0xc1, 0x91, 0x92, 0xa5,
	0xcf, 0x37, 0x10, 0xd1, 0x07, 0x30, 0x99, 0xe5,
	0xbc, 0x94, 0x65, 0x82, 0xfc, 0x0f, 0xab, 0x9f,
	0x54, 0x3c, 0x71, 0x6a, 0xe2, 0x48, 0x6a, 0x86,
	0x83, 0xfd, 0xca, 0x39, 0xd2, 0xe1, 0x4f, 0x23,
	0xd0, 0x0a, 0x58, 0x26, 0x64, 0xf4, 0xec, 0xb1
};
static const u8 enc_output081[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x36, 0xc3, 0x00, 0x29, 0x85, 0xdd, 0x21, 0xba,
	0xf8, 0x95, 0xd6, 0x33, 0x57, 0x3f, 0x12, 0xc0
};
static const u8 enc_assoc081[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce081[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0xe2, 0x93, 0x35
};
static const u8 enc_key081[] __initconst = {
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30
};

/* wycheproof - checking for int overflows */
static const u8 enc_input082[] __initconst = {
	0x40, 0x8a, 0xe6, 0xef, 0x1c, 0x7e, 0xf0, 0xfb,
	0x2c, 0x2d, 0x61, 0x08, 0x16, 0xfc, 0x78, 0x49,
	0xef, 0xa5, 0x8f, 0x78, 0x27, 0x3f, 0x5f, 0x16,
	0x6e, 0xa6, 0x5f, 0x81, 0xb5, 0x75, 0x74, 0x7d,
	0x03, 0x5b, 0x30, 0x40, 0xfe, 0xde, 0x1e, 0xb9,
	0x45, 0x97, 0x88, 0x66, 0x97, 0x88, 0x40, 0x8e,
	0x00, 0x41, 0x3b, 0x3e, 0x37, 0x6d, 0x15, 0x2d,
	0x20, 0x4a, 0xa2, 0xb7, 0xa8, 0x35, 0x58, 0xfc,
	0xd4, 0x8a, 0x0e, 0xf7, 0xa2, 0x6b, 0x1c, 0xd6,
	0xd3, 0x5d, 0x23, 0xb3, 0xf5, 0xdf, 0xe0, 0xca,
	0x77, 0xa4, 0xce, 0x32, 0xb9, 0x4a, 0xbf, 0x83,
	0xda, 0x2a, 0xef, 0xca, 0xf0, 0x68, 0x38, 0x08,
	0x79, 0xe8, 0x9f, 0xb0, 0xa3, 0x82, 0x95, 0x95,
	0xcf, 0x44, 0xc3, 0x85, 0x2a, 0xe2, 0xcc, 0x66,
	0x2b, 0x68, 0x9f, 0x93, 0x55, 0xd9, 0xc1, 0x83,
	0x80, 0x1f, 0x6a, 0xcc, 0x31, 0x3f, 0x89, 0x07
};
static const u8 enc_output082[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x65, 0x14, 0x51, 0x8e, 0x0a, 0x26, 0x41, 0x42,
	0xe0, 0xb7, 0x35, 0x1f, 0x96, 0x7f, 0xc2, 0xae
};
static const u8 enc_assoc082[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce082[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0xf7, 0xd5
};
static const u8 enc_key082[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - checking for int overflows */
static const u8 enc_input083[] __initconst = {
	0x0a, 0x0a, 0x24, 0x49, 0x9b, 0xca, 0xde, 0x58,
	0xcf, 0x15, 0x76, 0xc3, 0x12, 0xac, 0xa9, 0x84,
	0x71, 0x8c, 0xb4, 0xcc, 0x7e, 0x01, 0x53, 0xf5,
	0xa9, 0x01, 0x58, 0x10, 0x85, 0x96, 0x44, 0xdf,
	0xc0, 0x21, 0x17, 0x4e, 0x0b, 0x06, 0x0a, 0x39,
	0x74, 0x48, 0xde, 0x8b, 0x48, 0x4a, 0x86, 0x03,
	0xbe, 0x68, 0x0a, 0x69, 0x34, 0xc0, 0x90, 0x6f,
	0x30, 0xdd, 0x17, 0xea, 0xe2, 0xd4, 0xc5, 0xfa,
	0xa7, 0x77, 0xf8, 0xca, 0x53, 0x37, 0x0e, 0x08,
	0x33, 0x1b, 0x88, 0xc3, 0x42, 0xba, 0xc9, 0x59,
	0x78, 0x7b, 0xbb, 0x33, 0x93, 0x0e, 0x3b, 0x56,
	0xbe, 0x86, 0xda, 0x7f, 0x2a, 0x6e, 0xb1, 0xf9,
	0x40, 0x89, 0xd1, 0xd1, 0x81, 0x07, 0x4d, 0x43,
	0x02, 0xf8, 0xe0, 0x55, 0x2d, 0x0d, 0xe1, 0xfa,
	0xb3, 0x06, 0xa2, 0x1b, 0x42, 0xd4, 0xc3, 0xba,
	0x6e, 0x6f, 0x0c, 0xbc, 0xc8, 0x1e, 0x87, 0x7a
};
static const u8 enc_output083[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x4c, 0x19, 0x4d, 0xa6, 0xa9, 0x9f, 0xd6, 0x5b,
	0x40, 0xe9, 0xca, 0xd7, 0x98, 0xf4, 0x4b, 0x19
};
static const u8 enc_assoc083[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce083[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x3d, 0xfc, 0xe4
};
static const u8 enc_key083[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - checking for int overflows */
static const u8 enc_input084[] __initconst = {
	0x4a, 0x0a, 0xaf, 0xf8, 0x49, 0x47, 0x29, 0x18,
	0x86, 0x91, 0x70, 0x13, 0x40, 0xf3, 0xce, 0x2b,
	0x8a, 0x78, 0xee, 0xd3, 0xa0, 0xf0, 0x65, 0x99,
	0x4b, 0x72, 0x48, 0x4e, 0x79, 0x91, 0xd2, 0x5c,
	0x29, 0xaa, 0x07, 0x5e, 0xb1, 0xfc, 0x16, 0xde,
	0x93, 0xfe, 0x06, 0x90, 0x58, 0x11, 0x2a, 0xb2,
	0x84, 0xa3, 0xed, 0x18, 0x78, 0x03, 0x26, 0xd1,
	0x25, 0x8a, 0x47, 0x22, 0x2f, 0xa6, 0x33, 0xd8,
	0xb2, 0x9f, 0x3b, 0xd9, 0x15, 0x0b, 0x23, 0x9b,
	0x15, 0x46, 0xc2, 0xbb, 0x9b, 0x9f, 0x41, 0x0f,
	0xeb, 0xea, 0xd3, 0x96, 0x00, 0x0e, 0xe4, 0x77,
	0x70, 0x15, 0x32, 0xc3, 0xd0, 0xf5, 0xfb, 0xf8,
	0x95, 0xd2, 0x80, 0x19, 0x6d, 0x2f, 0x73, 0x7c,
	0x5e, 0x9f, 0xec, 0x50, 0xd9, 0x2b, 0xb0, 0xdf,
	0x5d, 0x7e, 0x51, 0x3b, 0xe5, 0xb8, 0xea, 0x97,
	0x13, 0x10, 0xd5, 0xbf, 0x16, 0xba, 0x7a, 0xee
};
static const u8 enc_output084[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xc8, 0xae, 0x77, 0x88, 0xcd, 0x28, 0x74, 0xab,
	0xc1, 0x38, 0x54, 0x1e, 0x11, 0xfd, 0x05, 0x87
};
static const u8 enc_assoc084[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce084[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x01, 0x84, 0x86, 0xa8
};
static const u8 enc_key084[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - checking for int overflows */
static const u8 enc_input085[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x78, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x9f, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x9c, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0x47, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0xd4, 0xd2, 0x06, 0x61, 0x6f, 0x92, 0x93, 0xf6,
	0x5b, 0x45, 0xdb, 0xbc, 0x74, 0xe7, 0xc2, 0xed,
	0xfb, 0xcb, 0xbf, 0x1c, 0xfb, 0x67, 0x9b, 0xb7,
	0x39, 0xa5, 0x86, 0x2d, 0xe2, 0xbc, 0xb9, 0x37,
	0xf7, 0x4d, 0x5b, 0xf8, 0x67, 0x1c, 0x5a, 0x8a,
	0x50, 0x92, 0xf6, 0x1d, 0x54, 0xc9, 0xaa, 0x5b
};
static const u8 enc_output085[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x93, 0x3a, 0x51, 0x63, 0xc7, 0xf6, 0x23, 0x68,
	0x32, 0x7b, 0x3f, 0xbc, 0x10, 0x36, 0xc9, 0x43
};
static const u8 enc_assoc085[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce085[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key085[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - special case tag */
static const u8 enc_input086[] __initconst = {
	0x9a, 0x49, 0xc4, 0x0f, 0x8b, 0x48, 0xd7, 0xc6,
	0x6d, 0x1d, 0xb4, 0xe5, 0x3f, 0x20, 0xf2, 0xdd,
	0x4a, 0xaa, 0x24, 0x1d, 0xda, 0xb2, 0x6b, 0x5b,
	0xc0, 0xe2, 0x18, 0xb7, 0x2c, 0x33, 0x90, 0xf2,
	0xdf, 0x3e, 0xbd, 0x01, 0x76, 0x70, 0x44, 0x19,
	0x97, 0x2b, 0xcd, 0xbc, 0x6b, 0xbc, 0xb3, 0xe4,
	0xe7, 0x4a, 0x71, 0x52, 0x8e, 0xf5, 0x12, 0x63,
	0xce, 0x24, 0xe0, 0xd5, 0x75, 0xe0, 0xe4, 0x4d
};
static const u8 enc_output086[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
};
static const u8 enc_assoc086[] __initconst = {
	0x85, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xa6, 0x90, 0x2f, 0xcb, 0xc8, 0x83, 0xbb, 0xc1,
	0x80, 0xb2, 0x56, 0xae, 0x34, 0xad, 0x7f, 0x00
};
static const u8 enc_nonce086[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b
};
static const u8 enc_key086[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - special case tag */
static const u8 enc_input087[] __initconst = {
	0x9a, 0x49, 0xc4, 0x0f, 0x8b, 0x48, 0xd7, 0xc6,
	0x6d, 0x1d, 0xb4, 0xe5, 0x3f, 0x20, 0xf2, 0xdd,
	0x4a, 0xaa, 0x24, 0x1d, 0xda, 0xb2, 0x6b, 0x5b,
	0xc0, 0xe2, 0x18, 0xb7, 0x2c, 0x33, 0x90, 0xf2,
	0xdf, 0x3e, 0xbd, 0x01, 0x76, 0x70, 0x44, 0x19,
	0x97, 0x2b, 0xcd, 0xbc, 0x6b, 0xbc, 0xb3, 0xe4,
	0xe7, 0x4a, 0x71, 0x52, 0x8e, 0xf5, 0x12, 0x63,
	0xce, 0x24, 0xe0, 0xd5, 0x75, 0xe0, 0xe4, 0x4d
};
static const u8 enc_output087[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_assoc087[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x24, 0x7e, 0x50, 0x64, 0x2a, 0x1c, 0x0a, 0x2f,
	0x8f, 0x77, 0x21, 0x96, 0x09, 0xdb, 0xa9, 0x58
};
static const u8 enc_nonce087[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b
};
static const u8 enc_key087[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - special case tag */
static const u8 enc_input088[] __initconst = {
	0x9a, 0x49, 0xc4, 0x0f, 0x8b, 0x48, 0xd7, 0xc6,
	0x6d, 0x1d, 0xb4, 0xe5, 0x3f, 0x20, 0xf2, 0xdd,
	0x4a, 0xaa, 0x24, 0x1d, 0xda, 0xb2, 0x6b, 0x5b,
	0xc0, 0xe2, 0x18, 0xb7, 0x2c, 0x33, 0x90, 0xf2,
	0xdf, 0x3e, 0xbd, 0x01, 0x76, 0x70, 0x44, 0x19,
	0x97, 0x2b, 0xcd, 0xbc, 0x6b, 0xbc, 0xb3, 0xe4,
	0xe7, 0x4a, 0x71, 0x52, 0x8e, 0xf5, 0x12, 0x63,
	0xce, 0x24, 0xe0, 0xd5, 0x75, 0xe0, 0xe4, 0x4d
};
static const u8 enc_output088[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
static const u8 enc_assoc088[] __initconst = {
	0x7c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xd9, 0xe7, 0x2c, 0x06, 0x4a, 0xc8, 0x96, 0x1f,
	0x3f, 0xa5, 0x85, 0xe0, 0xe2, 0xab, 0xd6, 0x00
};
static const u8 enc_nonce088[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b
};
static const u8 enc_key088[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - special case tag */
static const u8 enc_input089[] __initconst = {
	0x9a, 0x49, 0xc4, 0x0f, 0x8b, 0x48, 0xd7, 0xc6,
	0x6d, 0x1d, 0xb4, 0xe5, 0x3f, 0x20, 0xf2, 0xdd,
	0x4a, 0xaa, 0x24, 0x1d, 0xda, 0xb2, 0x6b, 0x5b,
	0xc0, 0xe2, 0x18, 0xb7, 0x2c, 0x33, 0x90, 0xf2,
	0xdf, 0x3e, 0xbd, 0x01, 0x76, 0x70, 0x44, 0x19,
	0x97, 0x2b, 0xcd, 0xbc, 0x6b, 0xbc, 0xb3, 0xe4,
	0xe7, 0x4a, 0x71, 0x52, 0x8e, 0xf5, 0x12, 0x63,
	0xce, 0x24, 0xe0, 0xd5, 0x75, 0xe0, 0xe4, 0x4d
};
static const u8 enc_output089[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80
};
static const u8 enc_assoc089[] __initconst = {
	0x65, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x95, 0xaf, 0x0f, 0x4d, 0x0b, 0x68, 0x6e, 0xae,
	0xcc, 0xca, 0x43, 0x07, 0xd5, 0x96, 0xf5, 0x02
};
static const u8 enc_nonce089[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b
};
static const u8 enc_key089[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - special case tag */
static const u8 enc_input090[] __initconst = {
	0x9a, 0x49, 0xc4, 0x0f, 0x8b, 0x48, 0xd7, 0xc6,
	0x6d, 0x1d, 0xb4, 0xe5, 0x3f, 0x20, 0xf2, 0xdd,
	0x4a, 0xaa, 0x24, 0x1d, 0xda, 0xb2, 0x6b, 0x5b,
	0xc0, 0xe2, 0x18, 0xb7, 0x2c, 0x33, 0x90, 0xf2,
	0xdf, 0x3e, 0xbd, 0x01, 0x76, 0x70, 0x44, 0x19,
	0x97, 0x2b, 0xcd, 0xbc, 0x6b, 0xbc, 0xb3, 0xe4,
	0xe7, 0x4a, 0x71, 0x52, 0x8e, 0xf5, 0x12, 0x63,
	0xce, 0x24, 0xe0, 0xd5, 0x75, 0xe0, 0xe4, 0x4d
};
static const u8 enc_output090[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f,
	0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x7f
};
static const u8 enc_assoc090[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x85, 0x40, 0xb4, 0x64, 0x35, 0x77, 0x07, 0xbe,
	0x3a, 0x39, 0xd5, 0x5c, 0x34, 0xf8, 0xbc, 0xb3
};
static const u8 enc_nonce090[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b
};
static const u8 enc_key090[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - special case tag */
static const u8 enc_input091[] __initconst = {
	0x9a, 0x49, 0xc4, 0x0f, 0x8b, 0x48, 0xd7, 0xc6,
	0x6d, 0x1d, 0xb4, 0xe5, 0x3f, 0x20, 0xf2, 0xdd,
	0x4a, 0xaa, 0x24, 0x1d, 0xda, 0xb2, 0x6b, 0x5b,
	0xc0, 0xe2, 0x18, 0xb7, 0x2c, 0x33, 0x90, 0xf2,
	0xdf, 0x3e, 0xbd, 0x01, 0x76, 0x70, 0x44, 0x19,
	0x97, 0x2b, 0xcd, 0xbc, 0x6b, 0xbc, 0xb3, 0xe4,
	0xe7, 0x4a, 0x71, 0x52, 0x8e, 0xf5, 0x12, 0x63,
	0xce, 0x24, 0xe0, 0xd5, 0x75, 0xe0, 0xe4, 0x4d
};
static const u8 enc_output091[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
	0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00
};
static const u8 enc_assoc091[] __initconst = {
	0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x66, 0x23, 0xd9, 0x90, 0xb8, 0x98, 0xd8, 0x30,
	0xd2, 0x12, 0xaf, 0x23, 0x83, 0x33, 0x07, 0x01
};
static const u8 enc_nonce091[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b
};
static const u8 enc_key091[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - special case tag */
static const u8 enc_input092[] __initconst = {
	0x9a, 0x49, 0xc4, 0x0f, 0x8b, 0x48, 0xd7, 0xc6,
	0x6d, 0x1d, 0xb4, 0xe5, 0x3f, 0x20, 0xf2, 0xdd,
	0x4a, 0xaa, 0x24, 0x1d, 0xda, 0xb2, 0x6b, 0x5b,
	0xc0, 0xe2, 0x18, 0xb7, 0x2c, 0x33, 0x90, 0xf2,
	0xdf, 0x3e, 0xbd, 0x01, 0x76, 0x70, 0x44, 0x19,
	0x97, 0x2b, 0xcd, 0xbc, 0x6b, 0xbc, 0xb3, 0xe4,
	0xe7, 0x4a, 0x71, 0x52, 0x8e, 0xf5, 0x12, 0x63,
	0xce, 0x24, 0xe0, 0xd5, 0x75, 0xe0, 0xe4, 0x4d
};
static const u8 enc_output092[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
static const u8 enc_assoc092[] __initconst = {
	0x83, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x5f, 0x16, 0xd0, 0x9f, 0x17, 0x78, 0x72, 0x11,
	0xb7, 0xd4, 0x84, 0xe0, 0x24, 0xf8, 0x97, 0x01
};
static const u8 enc_nonce092[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b
};
static const u8 enc_key092[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input093[] __initconst = {
	0x00, 0x52, 0x35, 0xd2, 0xa9, 0x19, 0xf2, 0x8d,
	0x3d, 0xb7, 0x66, 0x4a, 0x34, 0xae, 0x6b, 0x44,
	0x4d, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x5b, 0x8b, 0x94, 0x50, 0x9e, 0x2b, 0x74, 0xa3,
	0x6d, 0x34, 0x6e, 0x33, 0xd5, 0x72, 0x65, 0x9b,
	0xa9, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0x83, 0xdc, 0xe9, 0xf3, 0x07, 0x3e, 0xfa, 0xdb,
	0x7d, 0x23, 0xb8, 0x7a, 0xce, 0x35, 0x16, 0x8c
};
static const u8 enc_output093[] __initconst = {
	0x00, 0x39, 0xe2, 0xfd, 0x2f, 0xd3, 0x12, 0x14,
	0x9e, 0x98, 0x98, 0x80, 0x88, 0x48, 0x13, 0xe7,
	0xca, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x3b, 0x0e, 0x86, 0x9a, 0xaa, 0x8e, 0xa4, 0x96,
	0x32, 0xff, 0xff, 0x37, 0xb9, 0xe8, 0xce, 0x00,
	0xca, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x3b, 0x0e, 0x86, 0x9a, 0xaa, 0x8e, 0xa4, 0x96,
	0x32, 0xff, 0xff, 0x37, 0xb9, 0xe8, 0xce, 0x00,
	0xa5, 0x19, 0xac, 0x1a, 0x35, 0xb4, 0xa5, 0x77,
	0x87, 0x51, 0x0a, 0xf7, 0x8d, 0x8d, 0x20, 0x0a
};
static const u8 enc_assoc093[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce093[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key093[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input094[] __initconst = {
	0xd3, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xe5, 0xda, 0x78, 0x76, 0x6f, 0xa1, 0x92, 0x90,
	0xc0, 0x31, 0xf7, 0x52, 0x08, 0x50, 0x67, 0x45,
	0xae, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x49, 0x6d, 0xde, 0xb0, 0x55, 0x09, 0xc6, 0xef,
	0xff, 0xab, 0x75, 0xeb, 0x2d, 0xf4, 0xab, 0x09,
	0x76, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x01, 0x49, 0xef, 0x50, 0x4b, 0x71, 0xb1, 0x20,
	0xca, 0x4f, 0xf3, 0x95, 0x19, 0xc2, 0xc2, 0x10
};
static const u8 enc_output094[] __initconst = {
	0xd3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x62, 0x18, 0xb2, 0x7f, 0x83, 0xb8, 0xb4, 0x66,
	0x02, 0xf6, 0xe1, 0xd8, 0x34, 0x20, 0x7b, 0x02,
	0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x2a, 0x64, 0x16, 0xce, 0xdb, 0x1c, 0xdd, 0x29,
	0x6e, 0xf5, 0xd7, 0xd6, 0x92, 0xda, 0xff, 0x02,
	0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x2a, 0x64, 0x16, 0xce, 0xdb, 0x1c, 0xdd, 0x29,
	0x6e, 0xf5, 0xd7, 0xd6, 0x92, 0xda, 0xff, 0x02,
	0x30, 0x2f, 0xe8, 0x2a, 0xb0, 0xa0, 0x9a, 0xf6,
	0x44, 0x00, 0xd0, 0x15, 0xae, 0x83, 0xd9, 0xcc
};
static const u8 enc_assoc094[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce094[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key094[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input095[] __initconst = {
	0xe9, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x6d, 0xf1, 0x39, 0x4e, 0xdc, 0x53, 0x9b, 0x5b,
	0x3a, 0x09, 0x57, 0xbe, 0x0f, 0xb8, 0x59, 0x46,
	0x80, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0xd1, 0x76, 0x9f, 0xe8, 0x06, 0xbb, 0xfe, 0xb6,
	0xf5, 0x90, 0x95, 0x0f, 0x2e, 0xac, 0x9e, 0x0a,
	0x58, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x99, 0x52, 0xae, 0x08, 0x18, 0xc3, 0x89, 0x79,
	0xc0, 0x74, 0x13, 0x71, 0x1a, 0x9a, 0xf7, 0x13
};
static const u8 enc_output095[] __initconst = {
	0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xea, 0x33, 0xf3, 0x47, 0x30, 0x4a, 0xbd, 0xad,
	0xf8, 0xce, 0x41, 0x34, 0x33, 0xc8, 0x45, 0x01,
	0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xb2, 0x7f, 0x57, 0x96, 0x88, 0xae, 0xe5, 0x70,
	0x64, 0xce, 0x37, 0x32, 0x91, 0x82, 0xca, 0x01,
	0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xb2, 0x7f, 0x57, 0x96, 0x88, 0xae, 0xe5, 0x70,
	0x64, 0xce, 0x37, 0x32, 0x91, 0x82, 0xca, 0x01,
	0x98, 0xa7, 0xe8, 0x36, 0xe0, 0xee, 0x4d, 0x02,
	0x35, 0x00, 0xd0, 0x55, 0x7e, 0xc2, 0xcb, 0xe0
};
static const u8 enc_assoc095[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce095[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key095[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input096[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x64, 0xf9, 0x0f, 0x5b, 0x26, 0x92, 0xb8, 0x60,
	0xd4, 0x59, 0x6f, 0xf4, 0xb3, 0x40, 0x2c, 0x5c,
	0x00, 0xb9, 0xbb, 0x53, 0x70, 0x7a, 0xa6, 0x67,
	0xd3, 0x56, 0xfe, 0x50, 0xc7, 0x19, 0x96, 0x94,
	0x03, 0x35, 0x61, 0xe7, 0xca, 0xca, 0x6d, 0x94,
	0x1d, 0xc3, 0xcd, 0x69, 0x14, 0xad, 0x69, 0x04
};
static const u8 enc_output096[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xe3, 0x3b, 0xc5, 0x52, 0xca, 0x8b, 0x9e, 0x96,
	0x16, 0x9e, 0x79, 0x7e, 0x8f, 0x30, 0x30, 0x1b,
	0x60, 0x3c, 0xa9, 0x99, 0x44, 0xdf, 0x76, 0x52,
	0x8c, 0x9d, 0x6f, 0x54, 0xab, 0x83, 0x3d, 0x0f,
	0x60, 0x3c, 0xa9, 0x99, 0x44, 0xdf, 0x76, 0x52,
	0x8c, 0x9d, 0x6f, 0x54, 0xab, 0x83, 0x3d, 0x0f,
	0x6a, 0xb8, 0xdc, 0xe2, 0xc5, 0x9d, 0xa4, 0x73,
	0x71, 0x30, 0xb0, 0x25, 0x2f, 0x68, 0xa8, 0xd8
};
static const u8 enc_assoc096[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce096[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key096[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input097[] __initconst = {
	0x68, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xb0, 0x8f, 0x25, 0x67, 0x5b, 0x9b, 0xcb, 0xf6,
	0xe3, 0x84, 0x07, 0xde, 0x2e, 0xc7, 0x5a, 0x47,
	0x9f, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x2d, 0x2a, 0xf7, 0xcd, 0x6b, 0x08, 0x05, 0x01,
	0xd3, 0x1b, 0xa5, 0x4f, 0xb2, 0xeb, 0x75, 0x96,
	0x47, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x65, 0x0e, 0xc6, 0x2d, 0x75, 0x70, 0x72, 0xce,
	0xe6, 0xff, 0x23, 0x31, 0x86, 0xdd, 0x1c, 0x8f
};
static const u8 enc_output097[] __initconst = {
	0x68, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x37, 0x4d, 0xef, 0x6e, 0xb7, 0x82, 0xed, 0x00,
	0x21, 0x43, 0x11, 0x54, 0x12, 0xb7, 0x46, 0x00,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x4e, 0x23, 0x3f, 0xb3, 0xe5, 0x1d, 0x1e, 0xc7,
	0x42, 0x45, 0x07, 0x72, 0x0d, 0xc5, 0x21, 0x9d,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x4e, 0x23, 0x3f, 0xb3, 0xe5, 0x1d, 0x1e, 0xc7,
	0x42, 0x45, 0x07, 0x72, 0x0d, 0xc5, 0x21, 0x9d,
	0x04, 0x4d, 0xea, 0x60, 0x88, 0x80, 0x41, 0x2b,
	0xfd, 0xff, 0xcf, 0x35, 0x57, 0x9e, 0x9b, 0x26
};
static const u8 enc_assoc097[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce097[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key097[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input098[] __initconst = {
	0x6d, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xa1, 0x61, 0xb5, 0xab, 0x04, 0x09, 0x00, 0x62,
	0x9e, 0xfe, 0xff, 0x78, 0xd7, 0xd8, 0x6b, 0x45,
	0x9f, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0xc6, 0xf8, 0x07, 0x8c, 0xc8, 0xef, 0x12, 0xa0,
	0xff, 0x65, 0x7d, 0x6d, 0x08, 0xdb, 0x10, 0xb8,
	0x47, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x8e, 0xdc, 0x36, 0x6c, 0xd6, 0x97, 0x65, 0x6f,
	0xca, 0x81, 0xfb, 0x13, 0x3c, 0xed, 0x79, 0xa1
};
static const u8 enc_output098[] __initconst = {
	0x6d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x26, 0xa3, 0x7f, 0xa2, 0xe8, 0x10, 0x26, 0x94,
	0x5c, 0x39, 0xe9, 0xf2, 0xeb, 0xa8, 0x77, 0x02,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xa5, 0xf1, 0xcf, 0xf2, 0x46, 0xfa, 0x09, 0x66,
	0x6e, 0x3b, 0xdf, 0x50, 0xb7, 0xf5, 0x44, 0xb3,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xa5, 0xf1, 0xcf, 0xf2, 0x46, 0xfa, 0x09, 0x66,
	0x6e, 0x3b, 0xdf, 0x50, 0xb7, 0xf5, 0x44, 0xb3,
	0x1e, 0x6b, 0xea, 0x63, 0x14, 0x54, 0x2e, 0x2e,
	0xf9, 0xff, 0xcf, 0x45, 0x0b, 0x2e, 0x98, 0x2b
};
static const u8 enc_assoc098[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce098[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key098[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input099[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xfc, 0x01, 0xb8, 0x91, 0xe5, 0xf0, 0xf9, 0x12,
	0x8d, 0x7d, 0x1c, 0x57, 0x91, 0x92, 0xb6, 0x98,
	0x63, 0x41, 0x44, 0x15, 0xb6, 0x99, 0x68, 0x95,
	0x9a, 0x72, 0x91, 0xb7, 0xa5, 0xaf, 0x13, 0x48,
	0x60, 0xcd, 0x9e, 0xa1, 0x0c, 0x29, 0xa3, 0x66,
	0x54, 0xe7, 0xa2, 0x8e, 0x76, 0x1b, 0xec, 0xd8
};
static const u8 enc_output099[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x7b, 0xc3, 0x72, 0x98, 0x09, 0xe9, 0xdf, 0xe4,
	0x4f, 0xba, 0x0a, 0xdd, 0xad, 0xe2, 0xaa, 0xdf,
	0x03, 0xc4, 0x56, 0xdf, 0x82, 0x3c, 0xb8, 0xa0,
	0xc5, 0xb9, 0x00, 0xb3, 0xc9, 0x35, 0xb8, 0xd3,
	0x03, 0xc4, 0x56, 0xdf, 0x82, 0x3c, 0xb8, 0xa0,
	0xc5, 0xb9, 0x00, 0xb3, 0xc9, 0x35, 0xb8, 0xd3,
	0xed, 0x20, 0x17, 0xc8, 0xdb, 0xa4, 0x77, 0x56,
	0x29, 0x04, 0x9d, 0x78, 0x6e, 0x3b, 0xce, 0xb1
};
static const u8 enc_assoc099[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce099[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key099[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input100[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x6b, 0x6d, 0xc9, 0xd2, 0x1a, 0x81, 0x9e, 0x70,
	0xb5, 0x77, 0xf4, 0x41, 0x37, 0xd3, 0xd6, 0xbd,
	0x13, 0x35, 0xf5, 0xeb, 0x44, 0x49, 0x40, 0x77,
	0xb2, 0x64, 0x49, 0xa5, 0x4b, 0x6c, 0x7c, 0x75,
	0x10, 0xb9, 0x2f, 0x5f, 0xfe, 0xf9, 0x8b, 0x84,
	0x7c, 0xf1, 0x7a, 0x9c, 0x98, 0xd8, 0x83, 0xe5
};
static const u8 enc_output100[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xec, 0xaf, 0x03, 0xdb, 0xf6, 0x98, 0xb8, 0x86,
	0x77, 0xb0, 0xe2, 0xcb, 0x0b, 0xa3, 0xca, 0xfa,
	0x73, 0xb0, 0xe7, 0x21, 0x70, 0xec, 0x90, 0x42,
	0xed, 0xaf, 0xd8, 0xa1, 0x27, 0xf6, 0xd7, 0xee,
	0x73, 0xb0, 0xe7, 0x21, 0x70, 0xec, 0x90, 0x42,
	0xed, 0xaf, 0xd8, 0xa1, 0x27, 0xf6, 0xd7, 0xee,
	0x07, 0x3f, 0x17, 0xcb, 0x67, 0x78, 0x64, 0x59,
	0x25, 0x04, 0x9d, 0x88, 0x22, 0xcb, 0xca, 0xb6
};
static const u8 enc_assoc100[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce100[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key100[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input101[] __initconst = {
	0xff, 0xcb, 0x2b, 0x11, 0x06, 0xf8, 0x23, 0x4c,
	0x5e, 0x99, 0xd4, 0xdb, 0x4c, 0x70, 0x48, 0xde,
	0x32, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x16, 0xe9, 0x88, 0x4a, 0x11, 0x4f, 0x0e, 0x92,
	0x66, 0xce, 0xa3, 0x88, 0x5f, 0xe3, 0x6b, 0x9f,
	0xd6, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0xce, 0xbe, 0xf5, 0xe9, 0x88, 0x5a, 0x80, 0xea,
	0x76, 0xd9, 0x75, 0xc1, 0x44, 0xa4, 0x18, 0x88
};
static const u8 enc_output101[] __initconst = {
	0xff, 0xa0, 0xfc, 0x3e, 0x80, 0x32, 0xc3, 0xd5,
	0xfd, 0xb6, 0x2a, 0x11, 0xf0, 0x96, 0x30, 0x7d,
	0xb5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x76, 0x6c, 0x9a, 0x80, 0x25, 0xea, 0xde, 0xa7,
	0x39, 0x05, 0x32, 0x8c, 0x33, 0x79, 0xc0, 0x04,
	0xb5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x76, 0x6c, 0x9a, 0x80, 0x25, 0xea, 0xde, 0xa7,
	0x39, 0x05, 0x32, 0x8c, 0x33, 0x79, 0xc0, 0x04,
	0x8b, 0x9b, 0xb4, 0xb4, 0x86, 0x12, 0x89, 0x65,
	0x8c, 0x69, 0x6a, 0x83, 0x40, 0x15, 0x04, 0x05
};
static const u8 enc_assoc101[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce101[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key101[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input102[] __initconst = {
	0x6f, 0x9e, 0x70, 0xed, 0x3b, 0x8b, 0xac, 0xa0,
	0x26, 0xe4, 0x6a, 0x5a, 0x09, 0x43, 0x15, 0x8d,
	0x21, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x0c, 0x61, 0x2c, 0x5e, 0x8d, 0x89, 0xa8, 0x73,
	0xdb, 0xca, 0xad, 0x5b, 0x73, 0x46, 0x42, 0x9b,
	0xc5, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0xd4, 0x36, 0x51, 0xfd, 0x14, 0x9c, 0x26, 0x0b,
	0xcb, 0xdd, 0x7b, 0x12, 0x68, 0x01, 0x31, 0x8c
};
static const u8 enc_output102[] __initconst = {
	0x6f, 0xf5, 0xa7, 0xc2, 0xbd, 0x41, 0x4c, 0x39,
	0x85, 0xcb, 0x94, 0x90, 0xb5, 0xa5, 0x6d, 0x2e,
	0xa6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x6c, 0xe4, 0x3e, 0x94, 0xb9, 0x2c, 0x78, 0x46,
	0x84, 0x01, 0x3c, 0x5f, 0x1f, 0xdc, 0xe9, 0x00,
	0xa6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x6c, 0xe4, 0x3e, 0x94, 0xb9, 0x2c, 0x78, 0x46,
	0x84, 0x01, 0x3c, 0x5f, 0x1f, 0xdc, 0xe9, 0x00,
	0x8b, 0x3b, 0xbd, 0x51, 0x64, 0x44, 0x59, 0x56,
	0x8d, 0x81, 0xca, 0x1f, 0xa7, 0x2c, 0xe4, 0x04
};
static const u8 enc_assoc102[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce102[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key102[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input103[] __initconst = {
	0x41, 0x2b, 0x08, 0x0a, 0x3e, 0x19, 0xc1, 0x0d,
	0x44, 0xa1, 0xaf, 0x1e, 0xab, 0xde, 0xb4, 0xce,
	0x35, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x6b, 0x83, 0x94, 0x33, 0x09, 0x21, 0x48, 0x6c,
	0xa1, 0x1d, 0x29, 0x1c, 0x3e, 0x97, 0xee, 0x9a,
	0xd1, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0xb3, 0xd4, 0xe9, 0x90, 0x90, 0x34, 0xc6, 0x14,
	0xb1, 0x0a, 0xff, 0x55, 0x25, 0xd0, 0x9d, 0x8d
};
static const u8 enc_output103[] __initconst = {
	0x41, 0x40, 0xdf, 0x25, 0xb8, 0xd3, 0x21, 0x94,
	0xe7, 0x8e, 0x51, 0xd4, 0x17, 0x38, 0xcc, 0x6d,
	0xb2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x0b, 0x06, 0x86, 0xf9, 0x3d, 0x84, 0x98, 0x59,
	0xfe, 0xd6, 0xb8, 0x18, 0x52, 0x0d, 0x45, 0x01,
	0xb2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x0b, 0x06, 0x86, 0xf9, 0x3d, 0x84, 0x98, 0x59,
	0xfe, 0xd6, 0xb8, 0x18, 0x52, 0x0d, 0x45, 0x01,
	0x86, 0xfb, 0xab, 0x2b, 0x4a, 0x94, 0xf4, 0x7a,
	0xa5, 0x6f, 0x0a, 0xea, 0x65, 0xd1, 0x10, 0x08
};
static const u8 enc_assoc103[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce103[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key103[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input104[] __initconst = {
	0xb2, 0x47, 0xa7, 0x47, 0x23, 0x49, 0x1a, 0xac,
	0xac, 0xaa, 0xd7, 0x09, 0xc9, 0x1e, 0x93, 0x2b,
	0x31, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x9a, 0xde, 0x04, 0xe7, 0x5b, 0xb7, 0x01, 0xd9,
	0x66, 0x06, 0x01, 0xb3, 0x47, 0x65, 0xde, 0x98,
	0xd5, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0x42, 0x89, 0x79, 0x44, 0xc2, 0xa2, 0x8f, 0xa1,
	0x76, 0x11, 0xd7, 0xfa, 0x5c, 0x22, 0xad, 0x8f
};
static const u8 enc_output104[] __initconst = {
	0xb2, 0x2c, 0x70, 0x68, 0xa5, 0x83, 0xfa, 0x35,
	0x0f, 0x85, 0x29, 0xc3, 0x75, 0xf8, 0xeb, 0x88,
	0xb6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xfa, 0x5b, 0x16, 0x2d, 0x6f, 0x12, 0xd1, 0xec,
	0x39, 0xcd, 0x90, 0xb7, 0x2b, 0xff, 0x75, 0x03,
	0xb6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xfa, 0x5b, 0x16, 0x2d, 0x6f, 0x12, 0xd1, 0xec,
	0x39, 0xcd, 0x90, 0xb7, 0x2b, 0xff, 0x75, 0x03,
	0xa0, 0x19, 0xac, 0x2e, 0xd6, 0x67, 0xe1, 0x7d,
	0xa1, 0x6f, 0x0a, 0xfa, 0x19, 0x61, 0x0d, 0x0d
};
static const u8 enc_assoc104[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce104[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key104[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input105[] __initconst = {
	0x74, 0x0f, 0x9e, 0x49, 0xf6, 0x10, 0xef, 0xa5,
	0x85, 0xb6, 0x59, 0xca, 0x6e, 0xd8, 0xb4, 0x99,
	0x2d, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x41, 0x2d, 0x96, 0xaf, 0xbe, 0x80, 0xec, 0x3e,
	0x79, 0xd4, 0x51, 0xb0, 0x0a, 0x2d, 0xb2, 0x9a,
	0xc9, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0x99, 0x7a, 0xeb, 0x0c, 0x27, 0x95, 0x62, 0x46,
	0x69, 0xc3, 0x87, 0xf9, 0x11, 0x6a, 0xc1, 0x8d
};
static const u8 enc_output105[] __initconst = {
	0x74, 0x64, 0x49, 0x66, 0x70, 0xda, 0x0f, 0x3c,
	0x26, 0x99, 0xa7, 0x00, 0xd2, 0x3e, 0xcc, 0x3a,
	0xaa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x21, 0xa8, 0x84, 0x65, 0x8a, 0x25, 0x3c, 0x0b,
	0x26, 0x1f, 0xc0, 0xb4, 0x66, 0xb7, 0x19, 0x01,
	0xaa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x21, 0xa8, 0x84, 0x65, 0x8a, 0x25, 0x3c, 0x0b,
	0x26, 0x1f, 0xc0, 0xb4, 0x66, 0xb7, 0x19, 0x01,
	0x73, 0x6e, 0x18, 0x18, 0x16, 0x96, 0xa5, 0x88,
	0x9c, 0x31, 0x59, 0xfa, 0xab, 0xab, 0x20, 0xfd
};
static const u8 enc_assoc105[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce105[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key105[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input106[] __initconst = {
	0xad, 0xba, 0x5d, 0x10, 0x5b, 0xc8, 0xaa, 0x06,
	0x2c, 0x23, 0x36, 0xcb, 0x88, 0x9d, 0xdb, 0xd5,
	0x37, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x17, 0x7c, 0x5f, 0xfe, 0x28, 0x75, 0xf4, 0x68,
	0xf6, 0xc2, 0x96, 0x57, 0x48, 0xf3, 0x59, 0x9a,
	0xd3, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0xcf, 0x2b, 0x22, 0x5d, 0xb1, 0x60, 0x7a, 0x10,
	0xe6, 0xd5, 0x40, 0x1e, 0x53, 0xb4, 0x2a, 0x8d
};
static const u8 enc_output106[] __initconst = {
	0xad, 0xd1, 0x8a, 0x3f, 0xdd, 0x02, 0x4a, 0x9f,
	0x8f, 0x0c, 0xc8, 0x01, 0x34, 0x7b, 0xa3, 0x76,
	0xb0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x77, 0xf9, 0x4d, 0x34, 0x1c, 0xd0, 0x24, 0x5d,
	0xa9, 0x09, 0x07, 0x53, 0x24, 0x69, 0xf2, 0x01,
	0xb0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x77, 0xf9, 0x4d, 0x34, 0x1c, 0xd0, 0x24, 0x5d,
	0xa9, 0x09, 0x07, 0x53, 0x24, 0x69, 0xf2, 0x01,
	0xba, 0xd5, 0x8f, 0x10, 0xa9, 0x1e, 0x6a, 0x88,
	0x9a, 0xba, 0x32, 0xfd, 0x17, 0xd8, 0x33, 0x1a
};
static const u8 enc_assoc106[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce106[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key106[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input107[] __initconst = {
	0xfe, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xc0, 0x01, 0xed, 0xc5, 0xda, 0x44, 0x2e, 0x71,
	0x9b, 0xce, 0x9a, 0xbe, 0x27, 0x3a, 0xf1, 0x44,
	0xb4, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x48, 0x02, 0x5f, 0x41, 0xfa, 0x4e, 0x33, 0x6c,
	0x78, 0x69, 0x57, 0xa2, 0xa7, 0xc4, 0x93, 0x0a,
	0x6c, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x00, 0x26, 0x6e, 0xa1, 0xe4, 0x36, 0x44, 0xa3,
	0x4d, 0x8d, 0xd1, 0xdc, 0x93, 0xf2, 0xfa, 0x13
};
static const u8 enc_output107[] __initconst = {
	0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x47, 0xc3, 0x27, 0xcc, 0x36, 0x5d, 0x08, 0x87,
	0x59, 0x09, 0x8c, 0x34, 0x1b, 0x4a, 0xed, 0x03,
	0xd4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x2b, 0x0b, 0x97, 0x3f, 0x74, 0x5b, 0x28, 0xaa,
	0xe9, 0x37, 0xf5, 0x9f, 0x18, 0xea, 0xc7, 0x01,
	0xd4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x2b, 0x0b, 0x97, 0x3f, 0x74, 0x5b, 0x28, 0xaa,
	0xe9, 0x37, 0xf5, 0x9f, 0x18, 0xea, 0xc7, 0x01,
	0xd6, 0x8c, 0xe1, 0x74, 0x07, 0x9a, 0xdd, 0x02,
	0x8d, 0xd0, 0x5c, 0xf8, 0x14, 0x63, 0x04, 0x88
};
static const u8 enc_assoc107[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce107[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key107[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input108[] __initconst = {
	0xb5, 0x13, 0xb0, 0x6a, 0xb9, 0xac, 0x14, 0x43,
	0x5a, 0xcb, 0x8a, 0xa3, 0xa3, 0x7a, 0xfd, 0xb6,
	0x54, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x61, 0x95, 0x01, 0x93, 0xb1, 0xbf, 0x03, 0x11,
	0xff, 0x11, 0x79, 0x89, 0xae, 0xd9, 0xa9, 0x99,
	0xb0, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0xb9, 0xc2, 0x7c, 0x30, 0x28, 0xaa, 0x8d, 0x69,
	0xef, 0x06, 0xaf, 0xc0, 0xb5, 0x9e, 0xda, 0x8e
};
static const u8 enc_output108[] __initconst = {
	0xb5, 0x78, 0x67, 0x45, 0x3f, 0x66, 0xf4, 0xda,
	0xf9, 0xe4, 0x74, 0x69, 0x1f, 0x9c, 0x85, 0x15,
	0xd3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x10, 0x13, 0x59, 0x85, 0x1a, 0xd3, 0x24,
	0xa0, 0xda, 0xe8, 0x8d, 0xc2, 0x43, 0x02, 0x02,
	0xd3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x10, 0x13, 0x59, 0x85, 0x1a, 0xd3, 0x24,
	0xa0, 0xda, 0xe8, 0x8d, 0xc2, 0x43, 0x02, 0x02,
	0xaa, 0x48, 0xa3, 0x88, 0x7d, 0x4b, 0x05, 0x96,
	0x99, 0xc2, 0xfd, 0xf9, 0xc6, 0x78, 0x7e, 0x0a
};
static const u8 enc_assoc108[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce108[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key108[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input109[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xd4, 0xf1, 0x09, 0xe8, 0x14, 0xce, 0xa8, 0x5a,
	0x08, 0xc0, 0x11, 0xd8, 0x50, 0xdd, 0x1d, 0xcb,
	0xcf, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x53, 0x40, 0xb8, 0x5a, 0x9a, 0xa0, 0x82, 0x96,
	0xb7, 0x7a, 0x5f, 0xc3, 0x96, 0x1f, 0x66, 0x0f,
	0x17, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x1b, 0x64, 0x89, 0xba, 0x84, 0xd8, 0xf5, 0x59,
	0x82, 0x9e, 0xd9, 0xbd, 0xa2, 0x29, 0x0f, 0x16
};
static const u8 enc_output109[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x53, 0x33, 0xc3, 0xe1, 0xf8, 0xd7, 0x8e, 0xac,
	0xca, 0x07, 0x07, 0x52, 0x6c, 0xad, 0x01, 0x8c,
	0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x30, 0x49, 0x70, 0x24, 0x14, 0xb5, 0x99, 0x50,
	0x26, 0x24, 0xfd, 0xfe, 0x29, 0x31, 0x32, 0x04,
	0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x30, 0x49, 0x70, 0x24, 0x14, 0xb5, 0x99, 0x50,
	0x26, 0x24, 0xfd, 0xfe, 0x29, 0x31, 0x32, 0x04,
	0xb9, 0x36, 0xa8, 0x17, 0xf2, 0x21, 0x1a, 0xf1,
	0x29, 0xe2, 0xcf, 0x16, 0x0f, 0xd4, 0x2b, 0xcb
};
static const u8 enc_assoc109[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce109[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key109[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input110[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xdf, 0x4c, 0x62, 0x03, 0x2d, 0x41, 0x19, 0xb5,
	0x88, 0x47, 0x7e, 0x99, 0x92, 0x5a, 0x56, 0xd9,
	0xd6, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0xfa, 0x84, 0xf0, 0x64, 0x55, 0x36, 0x42, 0x1b,
	0x2b, 0xb9, 0x24, 0x6e, 0xc2, 0x19, 0xed, 0x0b,
	0x0e, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0xb2, 0xa0, 0xc1, 0x84, 0x4b, 0x4e, 0x35, 0xd4,
	0x1e, 0x5d, 0xa2, 0x10, 0xf6, 0x2f, 0x84, 0x12
};
static const u8 enc_output110[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x58, 0x8e, 0xa8, 0x0a, 0xc1, 0x58, 0x3f, 0x43,
	0x4a, 0x80, 0x68, 0x13, 0xae, 0x2a, 0x4a, 0x9e,
	0xb6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x99, 0x8d, 0x38, 0x1a, 0xdb, 0x23, 0x59, 0xdd,
	0xba, 0xe7, 0x86, 0x53, 0x7d, 0x37, 0xb9, 0x00,
	0xb6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x99, 0x8d, 0x38, 0x1a, 0xdb, 0x23, 0x59, 0xdd,
	0xba, 0xe7, 0x86, 0x53, 0x7d, 0x37, 0xb9, 0x00,
	0x9f, 0x7a, 0xc4, 0x35, 0x1f, 0x6b, 0x91, 0xe6,
	0x30, 0x97, 0xa7, 0x13, 0x11, 0x5d, 0x05, 0xbe
};
static const u8 enc_assoc110[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce110[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key110[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input111[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x13, 0xf8, 0x0a, 0x00, 0x6d, 0xc1, 0xbb, 0xda,
	0xd6, 0x39, 0xa9, 0x2f, 0xc7, 0xec, 0xa6, 0x55,
	0xf7, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x63, 0x48, 0xb8, 0xfd, 0x29, 0xbf, 0x96, 0xd5,
	0x63, 0xa5, 0x17, 0xe2, 0x7d, 0x7b, 0xfc, 0x0f,
	0x2f, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x2b, 0x6c, 0x89, 0x1d, 0x37, 0xc7, 0xe1, 0x1a,
	0x56, 0x41, 0x91, 0x9c, 0x49, 0x4d, 0x95, 0x16
};
static const u8 enc_output111[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x94, 0x3a, 0xc0, 0x09, 0x81, 0xd8, 0x9d, 0x2c,
	0x14, 0xfe, 0xbf, 0xa5, 0xfb, 0x9c, 0xba, 0x12,
	0x97, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x41, 0x70, 0x83, 0xa7, 0xaa, 0x8d, 0x13,
	0xf2, 0xfb, 0xb5, 0xdf, 0xc2, 0x55, 0xa8, 0x04,
	0x97, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x00, 0x41, 0x70, 0x83, 0xa7, 0xaa, 0x8d, 0x13,
	0xf2, 0xfb, 0xb5, 0xdf, 0xc2, 0x55, 0xa8, 0x04,
	0x9a, 0x18, 0xa8, 0x28, 0x07, 0x02, 0x69, 0xf4,
	0x47, 0x00, 0xd0, 0x09, 0xe7, 0x17, 0x1c, 0xc9
};
static const u8 enc_assoc111[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce111[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key111[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input112[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x82, 0xe5, 0x9b, 0x45, 0x82, 0x91, 0x50, 0x38,
	0xf9, 0x33, 0x81, 0x1e, 0x65, 0x2d, 0xc6, 0x6a,
	0xfc, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0xb6, 0x71, 0xc8, 0xca, 0xc2, 0x70, 0xc2, 0x65,
	0xa0, 0xac, 0x2f, 0x53, 0x57, 0x99, 0x88, 0x0a,
	0x24, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0xfe, 0x55, 0xf9, 0x2a, 0xdc, 0x08, 0xb5, 0xaa,
	0x95, 0x48, 0xa9, 0x2d, 0x63, 0xaf, 0xe1, 0x13
};
static const u8 enc_output112[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x05, 0x27, 0x51, 0x4c, 0x6e, 0x88, 0x76, 0xce,
	0x3b, 0xf4, 0x97, 0x94, 0x59, 0x5d, 0xda, 0x2d,
	0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xd5, 0x78, 0x00, 0xb4, 0x4c, 0x65, 0xd9, 0xa3,
	0x31, 0xf2, 0x8d, 0x6e, 0xe8, 0xb7, 0xdc, 0x01,
	0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xd5, 0x78, 0x00, 0xb4, 0x4c, 0x65, 0xd9, 0xa3,
	0x31, 0xf2, 0x8d, 0x6e, 0xe8, 0xb7, 0xdc, 0x01,
	0xb4, 0x36, 0xa8, 0x2b, 0x93, 0xd5, 0x55, 0xf7,
	0x43, 0x00, 0xd0, 0x19, 0x9b, 0xa7, 0x18, 0xce
};
static const u8 enc_assoc112[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce112[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key112[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input113[] __initconst = {
	0xff, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0xf1, 0xd1, 0x28, 0x87, 0xb7, 0x21, 0x69, 0x86,
	0xa1, 0x2d, 0x79, 0x09, 0x8b, 0x6d, 0xe6, 0x0f,
	0xc0, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0xa7, 0xc7, 0x58, 0x99, 0xf3, 0xe6, 0x0a, 0xf1,
	0xfc, 0xb6, 0xc7, 0x30, 0x7d, 0x87, 0x59, 0x0f,
	0x18, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0xef, 0xe3, 0x69, 0x79, 0xed, 0x9e, 0x7d, 0x3e,
	0xc9, 0x52, 0x41, 0x4e, 0x49, 0xb1, 0x30, 0x16
};
static const u8 enc_output113[] __initconst = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x76, 0x13, 0xe2, 0x8e, 0x5b, 0x38, 0x4f, 0x70,
	0x63, 0xea, 0x6f, 0x83, 0xb7, 0x1d, 0xfa, 0x48,
	0xa0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xc4, 0xce, 0x90, 0xe7, 0x7d, 0xf3, 0x11, 0x37,
	0x6d, 0xe8, 0x65, 0x0d, 0xc2, 0xa9, 0x0d, 0x04,
	0xa0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xc4, 0xce, 0x90, 0xe7, 0x7d, 0xf3, 0x11, 0x37,
	0x6d, 0xe8, 0x65, 0x0d, 0xc2, 0xa9, 0x0d, 0x04,
	0xce, 0x54, 0xa8, 0x2e, 0x1f, 0xa9, 0x42, 0xfa,
	0x3f, 0x00, 0xd0, 0x29, 0x4f, 0x37, 0x15, 0xd3
};
static const u8 enc_assoc113[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce113[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key113[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input114[] __initconst = {
	0xcb, 0xf1, 0xda, 0x9e, 0x0b, 0xa9, 0x37, 0x73,
	0x74, 0xe6, 0x9e, 0x1c, 0x0e, 0x60, 0x0c, 0xfc,
	0x34, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0xbe, 0x3f, 0xa6, 0x6b, 0x6c, 0xe7, 0x80, 0x8a,
	0xa3, 0xe4, 0x59, 0x49, 0xf9, 0x44, 0x64, 0x9f,
	0xd0, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0x66, 0x68, 0xdb, 0xc8, 0xf5, 0xf2, 0x0e, 0xf2,
	0xb3, 0xf3, 0x8f, 0x00, 0xe2, 0x03, 0x17, 0x88
};
static const u8 enc_output114[] __initconst = {
	0xcb, 0x9a, 0x0d, 0xb1, 0x8d, 0x63, 0xd7, 0xea,
	0xd7, 0xc9, 0x60, 0xd6, 0xb2, 0x86, 0x74, 0x5f,
	0xb3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xde, 0xba, 0xb4, 0xa1, 0x58, 0x42, 0x50, 0xbf,
	0xfc, 0x2f, 0xc8, 0x4d, 0x95, 0xde, 0xcf, 0x04,
	0xb3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xde, 0xba, 0xb4, 0xa1, 0x58, 0x42, 0x50, 0xbf,
	0xfc, 0x2f, 0xc8, 0x4d, 0x95, 0xde, 0xcf, 0x04,
	0x23, 0x83, 0xab, 0x0b, 0x79, 0x92, 0x05, 0x69,
	0x9b, 0x51, 0x0a, 0xa7, 0x09, 0xbf, 0x31, 0xf1
};
static const u8 enc_assoc114[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce114[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key114[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input115[] __initconst = {
	0x8f, 0x27, 0x86, 0x94, 0xc4, 0xe9, 0xda, 0xeb,
	0xd5, 0x8d, 0x3e, 0x5b, 0x96, 0x6e, 0x8b, 0x68,
	0x42, 0x3d, 0x35, 0xf6, 0x13, 0xe6, 0xd9, 0x09,
	0x3d, 0x38, 0xe9, 0x75, 0xc3, 0x8f, 0xe3, 0xb8,
	0x06, 0x53, 0xe7, 0xa3, 0x31, 0x71, 0x88, 0x33,
	0xac, 0xc3, 0xb9, 0xad, 0xff, 0x1c, 0x31, 0x98,
	0xa6, 0xf6, 0x37, 0x81, 0x71, 0xea, 0xe4, 0x39,
	0x6e, 0xa1, 0x5d, 0xc2, 0x40, 0xd1, 0xab, 0xf4,
	0xde, 0x04, 0x9a, 0x00, 0xa8, 0x64, 0x06, 0x4b,
	0xbc, 0xd4, 0x6f, 0xe4, 0xe4, 0x5b, 0x42, 0x8f
};
static const u8 enc_output115[] __initconst = {
	0x8f, 0x4c, 0x51, 0xbb, 0x42, 0x23, 0x3a, 0x72,
	0x76, 0xa2, 0xc0, 0x91, 0x2a, 0x88, 0xf3, 0xcb,
	0xc5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x66, 0xd6, 0xf5, 0x69, 0x05, 0xd4, 0x58, 0x06,
	0xf3, 0x08, 0x28, 0xa9, 0x93, 0x86, 0x9a, 0x03,
	0xc5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x66, 0xd6, 0xf5, 0x69, 0x05, 0xd4, 0x58, 0x06,
	0xf3, 0x08, 0x28, 0xa9, 0x93, 0x86, 0x9a, 0x03,
	0x8b, 0xfb, 0xab, 0x17, 0xa9, 0xe0, 0xb8, 0x74,
	0x8b, 0x51, 0x0a, 0xe7, 0xd9, 0xfd, 0x23, 0x05
};
static const u8 enc_assoc115[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce115[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key115[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input116[] __initconst = {
	0xd5, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x9a, 0x22, 0xd7, 0x0a, 0x48, 0xe2, 0x4f, 0xdd,
	0xcd, 0xd4, 0x41, 0x9d, 0xe6, 0x4c, 0x8f, 0x44,
	0xfc, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x77, 0xb5, 0xc9, 0x07, 0xd9, 0xc9, 0xe1, 0xea,
	0x51, 0x85, 0x1a, 0x20, 0x4a, 0xad, 0x9f, 0x0a,
	0x24, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x3f, 0x91, 0xf8, 0xe7, 0xc7, 0xb1, 0x96, 0x25,
	0x64, 0x61, 0x9c, 0x5e, 0x7e, 0x9b, 0xf6, 0x13
};
static const u8 enc_output116[] __initconst = {
	0xd5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x1d, 0xe0, 0x1d, 0x03, 0xa4, 0xfb, 0x69, 0x2b,
	0x0f, 0x13, 0x57, 0x17, 0xda, 0x3c, 0x93, 0x03,
	0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x14, 0xbc, 0x01, 0x79, 0x57, 0xdc, 0xfa, 0x2c,
	0xc0, 0xdb, 0xb8, 0x1d, 0xf5, 0x83, 0xcb, 0x01,
	0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x14, 0xbc, 0x01, 0x79, 0x57, 0xdc, 0xfa, 0x2c,
	0xc0, 0xdb, 0xb8, 0x1d, 0xf5, 0x83, 0xcb, 0x01,
	0x49, 0xbc, 0x6e, 0x9f, 0xc5, 0x1c, 0x4d, 0x50,
	0x30, 0x36, 0x64, 0x4d, 0x84, 0x27, 0x73, 0xd2
};
static const u8 enc_assoc116[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce116[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key116[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input117[] __initconst = {
	0xdb, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x75, 0xd5, 0x64, 0x3a, 0xa5, 0xaf, 0x93, 0x4d,
	0x8c, 0xce, 0x39, 0x2c, 0xc3, 0xee, 0xdb, 0x47,
	0xc0, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0x60, 0x1b, 0x5a, 0xd2, 0x06, 0x7f, 0x28, 0x06,
	0x6a, 0x8f, 0x32, 0x81, 0x71, 0x5b, 0xa8, 0x08,
	0x18, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x28, 0x3f, 0x6b, 0x32, 0x18, 0x07, 0x5f, 0xc9,
	0x5f, 0x6b, 0xb4, 0xff, 0x45, 0x6d, 0xc1, 0x11
};
static const u8 enc_output117[] __initconst = {
	0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xf2, 0x17, 0xae, 0x33, 0x49, 0xb6, 0xb5, 0xbb,
	0x4e, 0x09, 0x2f, 0xa6, 0xff, 0x9e, 0xc7, 0x00,
	0xa0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x03, 0x12, 0x92, 0xac, 0x88, 0x6a, 0x33, 0xc0,
	0xfb, 0xd1, 0x90, 0xbc, 0xce, 0x75, 0xfc, 0x03,
	0xa0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x03, 0x12, 0x92, 0xac, 0x88, 0x6a, 0x33, 0xc0,
	0xfb, 0xd1, 0x90, 0xbc, 0xce, 0x75, 0xfc, 0x03,
	0x63, 0xda, 0x6e, 0xa2, 0x51, 0xf0, 0x39, 0x53,
	0x2c, 0x36, 0x64, 0x5d, 0x38, 0xb7, 0x6f, 0xd7
};
static const u8 enc_assoc117[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce117[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key117[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

/* wycheproof - edge case intermediate sums in poly1305 */
static const u8 enc_input118[] __initconst = {
	0x93, 0x94, 0x28, 0xd0, 0x79, 0x35, 0x1f, 0x66,
	0x5c, 0xd0, 0x01, 0x35, 0x43, 0x19, 0x87, 0x5c,
	0x62, 0x48, 0x39, 0x60, 0x42, 0x16, 0xe4, 0x03,
	0xeb, 0xcc, 0x6a, 0xf5, 0x59, 0xec, 0x8b, 0x43,
	0x97, 0x7a, 0xed, 0x35, 0xcb, 0x5a, 0x2f, 0xca,
	0xa0, 0x34, 0x6e, 0xfb, 0x93, 0x65, 0x54, 0x64,
	0xd8, 0xc8, 0xc3, 0xfa, 0x1a, 0x9e, 0x47, 0x4a,
	0xbe, 0x52, 0xd0, 0x2c, 0x81, 0x87, 0xe9, 0x0f,
	0x4f, 0x2d, 0x90, 0x96, 0x52, 0x4f, 0xa1, 0xb2,
	0xb0, 0x23, 0xb8, 0xb2, 0x88, 0x22, 0x27, 0x73,
	0x90, 0xec, 0xf2, 0x1a, 0x04, 0xe6, 0x30, 0x85,
	0x8b, 0xb6, 0x56, 0x52, 0xb5, 0xb1, 0x80, 0x16
};
static const u8 enc_output118[] __initconst = {
	0x93, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xe5, 0x8a, 0xf3, 0x69, 0xae, 0x0f, 0xc2, 0xf5,
	0x29, 0x0b, 0x7c, 0x7f, 0x65, 0x9c, 0x97, 0x04,
	0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xbb, 0xc1, 0x0b, 0x84, 0x94, 0x8b, 0x5c, 0x8c,
	0x2f, 0x0c, 0x72, 0x11, 0x3e, 0xa9, 0xbd, 0x04,
	0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xbb, 0xc1, 0x0b, 0x84, 0x94, 0x8b, 0x5c, 0x8c,
	0x2f, 0x0c, 0x72, 0x11, 0x3e, 0xa9, 0xbd, 0x04,
	0x73, 0xeb, 0x27, 0x24, 0xb5, 0xc4, 0x05, 0xf0,
	0x4d, 0x00, 0xd0, 0xf1, 0x58, 0x40, 0xa1, 0xc1
};
static const u8 enc_assoc118[] __initconst = {
	0xff, 0xff, 0xff, 0xff
};
static const u8 enc_nonce118[] __initconst = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x2d, 0x52
};
static const u8 enc_key118[] __initconst = {
	0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
	0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
	0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
	0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
};

static const struct chacha20poly1305_testvec
chacha20poly1305_enc_vectors[] __initconst = {
	{ enc_input001, enc_output001, enc_assoc001, enc_nonce001, enc_key001,
	  sizeof(enc_input001), sizeof(enc_assoc001), sizeof(enc_nonce001) },
	{ enc_input002, enc_output002, enc_assoc002, enc_nonce002, enc_key002,
	  sizeof(enc_input002), sizeof(enc_assoc002), sizeof(enc_nonce002) },
	{ enc_input003, enc_output003, enc_assoc003, enc_nonce003, enc_key003,
	  sizeof(enc_input003), sizeof(enc_assoc003), sizeof(enc_nonce003) },
	{ enc_input004, enc_output004, enc_assoc004, enc_nonce004, enc_key004,
	  sizeof(enc_input004), sizeof(enc_assoc004), sizeof(enc_nonce004) },
	{ enc_input005, enc_output005, enc_assoc005, enc_nonce005, enc_key005,
	  sizeof(enc_input005), sizeof(enc_assoc005), sizeof(enc_nonce005) },
	{ enc_input006, enc_output006, enc_assoc006, enc_nonce006, enc_key006,
	  sizeof(enc_input006), sizeof(enc_assoc006), sizeof(enc_nonce006) },
	{ enc_input007, enc_output007, enc_assoc007, enc_nonce007, enc_key007,
	  sizeof(enc_input007), sizeof(enc_assoc007), sizeof(enc_nonce007) },
	{ enc_input008, enc_output008, enc_assoc008, enc_nonce008, enc_key008,
	  sizeof(enc_input008), sizeof(enc_assoc008), sizeof(enc_nonce008) },
	{ enc_input009, enc_output009, enc_assoc009, enc_nonce009, enc_key009,
	  sizeof(enc_input009), sizeof(enc_assoc009), sizeof(enc_nonce009) },
	{ enc_input010, enc_output010, enc_assoc010, enc_nonce010, enc_key010,
	  sizeof(enc_input010), sizeof(enc_assoc010), sizeof(enc_nonce010) },
	{ enc_input011, enc_output011, enc_assoc011, enc_nonce011, enc_key011,
	  sizeof(enc_input011), sizeof(enc_assoc011), sizeof(enc_nonce011) },
	{ enc_input012, enc_output012, enc_assoc012, enc_nonce012, enc_key012,
	  sizeof(enc_input012), sizeof(enc_assoc012), sizeof(enc_nonce012) },
	{ enc_input013, enc_output013, enc_assoc013, enc_nonce013, enc_key013,
	  sizeof(enc_input013), sizeof(enc_assoc013), sizeof(enc_nonce013) },
	{ enc_input014, enc_output014, enc_assoc014, enc_nonce014, enc_key014,
	  sizeof(enc_input014), sizeof(enc_assoc014), sizeof(enc_nonce014) },
	{ enc_input015, enc_output015, enc_assoc015, enc_nonce015, enc_key015,
	  sizeof(enc_input015), sizeof(enc_assoc015), sizeof(enc_nonce015) },
	{ enc_input016, enc_output016, enc_assoc016, enc_nonce016, enc_key016,
	  sizeof(enc_input016), sizeof(enc_assoc016), sizeof(enc_nonce016) },
	{ enc_input017, enc_output017, enc_assoc017, enc_nonce017, enc_key017,
	  sizeof(enc_input017), sizeof(enc_assoc017), sizeof(enc_nonce017) },
	{ enc_input018, enc_output018, enc_assoc018, enc_nonce018, enc_key018,
	  sizeof(enc_input018), sizeof(enc_assoc018), sizeof(enc_nonce018) },
	{ enc_input019, enc_output019, enc_assoc019, enc_nonce019, enc_key019,
	  sizeof(enc_input019), sizeof(enc_assoc019), sizeof(enc_nonce019) },
	{ enc_input020, enc_output020, enc_assoc020, enc_nonce020, enc_key020,
	  sizeof(enc_input020), sizeof(enc_assoc020), sizeof(enc_nonce020) },
	{ enc_input021, enc_output021, enc_assoc021, enc_nonce021, enc_key021,
	  sizeof(enc_input021), sizeof(enc_assoc021), sizeof(enc_nonce021) },
	{ enc_input022, enc_output022, enc_assoc022, enc_nonce022, enc_key022,
	  sizeof(enc_input022), sizeof(enc_assoc022), sizeof(enc_nonce022) },
	{ enc_input023, enc_output023, enc_assoc023, enc_nonce023, enc_key023,
	  sizeof(enc_input023), sizeof(enc_assoc023), sizeof(enc_nonce023) },
	{ enc_input024, enc_output024, enc_assoc024, enc_nonce024, enc_key024,
	  sizeof(enc_input024), sizeof(enc_assoc024), sizeof(enc_nonce024) },
	{ enc_input025, enc_output025, enc_assoc025, enc_nonce025, enc_key025,
	  sizeof(enc_input025), sizeof(enc_assoc025), sizeof(enc_nonce025) },
	{ enc_input026, enc_output026, enc_assoc026, enc_nonce026, enc_key026,
	  sizeof(enc_input026), sizeof(enc_assoc026), sizeof(enc_nonce026) },
	{ enc_input027, enc_output027, enc_assoc027, enc_nonce027, enc_key027,
	  sizeof(enc_input027), sizeof(enc_assoc027), sizeof(enc_nonce027) },
	{ enc_input028, enc_output028, enc_assoc028, enc_nonce028, enc_key028,
	  sizeof(enc_input028), sizeof(enc_assoc028), sizeof(enc_nonce028) },
	{ enc_input029, enc_output029, enc_assoc029, enc_nonce029, enc_key029,
	  sizeof(enc_input029), sizeof(enc_assoc029), sizeof(enc_nonce029) },
	{ enc_input030, enc_output030, enc_assoc030, enc_nonce030, enc_key030,
	  sizeof(enc_input030), sizeof(enc_assoc030), sizeof(enc_nonce030) },
	{ enc_input031, enc_output031, enc_assoc031, enc_nonce031, enc_key031,
	  sizeof(enc_input031), sizeof(enc_assoc031), sizeof(enc_nonce031) },
	{ enc_input032, enc_output032, enc_assoc032, enc_nonce032, enc_key032,
	  sizeof(enc_input032), sizeof(enc_assoc032), sizeof(enc_nonce032) },
	{ enc_input033, enc_output033, enc_assoc033, enc_nonce033, enc_key033,
	  sizeof(enc_input033), sizeof(enc_assoc033), sizeof(enc_nonce033) },
	{ enc_input034, enc_output034, enc_assoc034, enc_nonce034, enc_key034,
	  sizeof(enc_input034), sizeof(enc_assoc034), sizeof(enc_nonce034) },
	{ enc_input035, enc_output035, enc_assoc035, enc_nonce035, enc_key035,
	  sizeof(enc_input035), sizeof(enc_assoc035), sizeof(enc_nonce035) },
	{ enc_input036, enc_output036, enc_assoc036, enc_nonce036, enc_key036,
	  sizeof(enc_input036), sizeof(enc_assoc036), sizeof(enc_nonce036) },
	{ enc_input037, enc_output037, enc_assoc037, enc_nonce037, enc_key037,
	  sizeof(enc_input037), sizeof(enc_assoc037), sizeof(enc_nonce037) },
	{ enc_input038, enc_output038, enc_assoc038, enc_nonce038, enc_key038,
	  sizeof(enc_input038), sizeof(enc_assoc038), sizeof(enc_nonce038) },
	{ enc_input039, enc_output039, enc_assoc039, enc_nonce039, enc_key039,
	  sizeof(enc_input039), sizeof(enc_assoc039), sizeof(enc_nonce039) },
	{ enc_input040, enc_output040, enc_assoc040, enc_nonce040, enc_key040,
	  sizeof(enc_input040), sizeof(enc_assoc040), sizeof(enc_nonce040) },
	{ enc_input041, enc_output041, enc_assoc041, enc_nonce041, enc_key041,
	  sizeof(enc_input041), sizeof(enc_assoc041), sizeof(enc_nonce041) },
	{ enc_input042, enc_output042, enc_assoc042, enc_nonce042, enc_key042,
	  sizeof(enc_input042), sizeof(enc_assoc042), sizeof(enc_nonce042) },
	{ enc_input043, enc_output043, enc_assoc043, enc_nonce043, enc_key043,
	  sizeof(enc_input043), sizeof(enc_assoc043), sizeof(enc_nonce043) },
	{ enc_input044, enc_output044, enc_assoc044, enc_nonce044, enc_key044,
	  sizeof(enc_input044), sizeof(enc_assoc044), sizeof(enc_nonce044) },
	{ enc_input045, enc_output045, enc_assoc045, enc_nonce045, enc_key045,
	  sizeof(enc_input045), sizeof(enc_assoc045), sizeof(enc_nonce045) },
	{ enc_input046, enc_output046, enc_assoc046, enc_nonce046, enc_key046,
	  sizeof(enc_input046), sizeof(enc_assoc046), sizeof(enc_nonce046) },
	{ enc_input047, enc_output047, enc_assoc047, enc_nonce047, enc_key047,
	  sizeof(enc_input047), sizeof(enc_assoc047), sizeof(enc_nonce047) },
	{ enc_input048, enc_output048, enc_assoc048, enc_nonce048, enc_key048,
	  sizeof(enc_input048), sizeof(enc_assoc048), sizeof(enc_nonce048) },
	{ enc_input049, enc_output049, enc_assoc049, enc_nonce049, enc_key049,
	  sizeof(enc_input049), sizeof(enc_assoc049), sizeof(enc_nonce049) },
	{ enc_input050, enc_output050, enc_assoc050, enc_nonce050, enc_key050,
	  sizeof(enc_input050), sizeof(enc_assoc050), sizeof(enc_nonce050) },
	{ enc_input051, enc_output051, enc_assoc051, enc_nonce051, enc_key051,
	  sizeof(enc_input051), sizeof(enc_assoc051), sizeof(enc_nonce051) },
	{ enc_input052, enc_output052, enc_assoc052, enc_nonce052, enc_key052,
	  sizeof(enc_input052), sizeof(enc_assoc052), sizeof(enc_nonce052) },
	{ enc_input053, enc_output053, enc_assoc053, enc_nonce053, enc_key053,
	  sizeof(enc_input053), sizeof(enc_assoc053), sizeof(enc_nonce053) },
	{ enc_input054, enc_output054, enc_assoc054, enc_nonce054, enc_key054,
	  sizeof(enc_input054), sizeof(enc_assoc054), sizeof(enc_nonce054) },
	{ enc_input055, enc_output055, enc_assoc055, enc_nonce055, enc_key055,
	  sizeof(enc_input055), sizeof(enc_assoc055), sizeof(enc_nonce055) },
	{ enc_input056, enc_output056, enc_assoc056, enc_nonce056, enc_key056,
	  sizeof(enc_input056), sizeof(enc_assoc056), sizeof(enc_nonce056) },
	{ enc_input057, enc_output057, enc_assoc057, enc_nonce057, enc_key057,
	  sizeof(enc_input057), sizeof(enc_assoc057), sizeof(enc_nonce057) },
	{ enc_input058, enc_output058, enc_assoc058, enc_nonce058, enc_key058,
	  sizeof(enc_input058), sizeof(enc_assoc058), sizeof(enc_nonce058) },
	{ enc_input059, enc_output059, enc_assoc059, enc_nonce059, enc_key059,
	  sizeof(enc_input059), sizeof(enc_assoc059), sizeof(enc_nonce059) },
	{ enc_input060, enc_output060, enc_assoc060, enc_nonce060, enc_key060,
	  sizeof(enc_input060), sizeof(enc_assoc060), sizeof(enc_nonce060) },
	{ enc_input061, enc_output061, enc_assoc061, enc_nonce061, enc_key061,
	  sizeof(enc_input061), sizeof(enc_assoc061), sizeof(enc_nonce061) },
	{ enc_input062, enc_output062, enc_assoc062, enc_nonce062, enc_key062,
	  sizeof(enc_input062), sizeof(enc_assoc062), sizeof(enc_nonce062) },
	{ enc_input063, enc_output063, enc_assoc063, enc_nonce063, enc_key063,
	  sizeof(enc_input063), sizeof(enc_assoc063), sizeof(enc_nonce063) },
	{ enc_input064, enc_output064, enc_assoc064, enc_nonce064, enc_key064,
	  sizeof(enc_input064), sizeof(enc_assoc064), sizeof(enc_nonce064) },
	{ enc_input065, enc_output065, enc_assoc065, enc_nonce065, enc_key065,
	  sizeof(enc_input065), sizeof(enc_assoc065), sizeof(enc_nonce065) },
	{ enc_input066, enc_output066, enc_assoc066, enc_nonce066, enc_key066,
	  sizeof(enc_input066), sizeof(enc_assoc066), sizeof(enc_nonce066) },
	{ enc_input067, enc_output067, enc_assoc067, enc_nonce067, enc_key067,
	  sizeof(enc_input067), sizeof(enc_assoc067), sizeof(enc_nonce067) },
	{ enc_input068, enc_output068, enc_assoc068, enc_nonce068, enc_key068,
	  sizeof(enc_input068), sizeof(enc_assoc068), sizeof(enc_nonce068) },
	{ enc_input069, enc_output069, enc_assoc069, enc_nonce069, enc_key069,
	  sizeof(enc_input069), sizeof(enc_assoc069), sizeof(enc_nonce069) },
	{ enc_input070, enc_output070, enc_assoc070, enc_nonce070, enc_key070,
	  sizeof(enc_input070), sizeof(enc_assoc070), sizeof(enc_nonce070) },
	{ enc_input071, enc_output071, enc_assoc071, enc_nonce071, enc_key071,
	  sizeof(enc_input071), sizeof(enc_assoc071), sizeof(enc_nonce071) },
	{ enc_input072, enc_output072, enc_assoc072, enc_nonce072, enc_key072,
	  sizeof(enc_input072), sizeof(enc_assoc072), sizeof(enc_nonce072) },
	{ enc_input073, enc_output073, enc_assoc073, enc_nonce073, enc_key073,
	  sizeof(enc_input073), sizeof(enc_assoc073), sizeof(enc_nonce073) },
	{ enc_input074, enc_output074, enc_assoc074, enc_nonce074, enc_key074,
	  sizeof(enc_input074), sizeof(enc_assoc074), sizeof(enc_nonce074) },
	{ enc_input075, enc_output075, enc_assoc075, enc_nonce075, enc_key075,
	  sizeof(enc_input075), sizeof(enc_assoc075), sizeof(enc_nonce075) },
	{ enc_input076, enc_output076, enc_assoc076, enc_nonce076, enc_key076,
	  sizeof(enc_input076), sizeof(enc_assoc076), sizeof(enc_nonce076) },
	{ enc_input077, enc_output077, enc_assoc077, enc_nonce077, enc_key077,
	  sizeof(enc_input077), sizeof(enc_assoc077), sizeof(enc_nonce077) },
	{ enc_input078, enc_output078, enc_assoc078, enc_nonce078, enc_key078,
	  sizeof(enc_input078), sizeof(enc_assoc078), sizeof(enc_nonce078) },
	{ enc_input079, enc_output079, enc_assoc079, enc_nonce079, enc_key079,
	  sizeof(enc_input079), sizeof(enc_assoc079), sizeof(enc_nonce079) },
	{ enc_input080, enc_output080, enc_assoc080, enc_nonce080, enc_key080,
	  sizeof(enc_input080), sizeof(enc_assoc080), sizeof(enc_nonce080) },
	{ enc_input081, enc_output081, enc_assoc081, enc_nonce081, enc_key081,
	  sizeof(enc_input081), sizeof(enc_assoc081), sizeof(enc_nonce081) },
	{ enc_input082, enc_output082, enc_assoc082, enc_nonce082, enc_key082,
	  sizeof(enc_input082), sizeof(enc_assoc082), sizeof(enc_nonce082) },
	{ enc_input083, enc_output083, enc_assoc083, enc_nonce083, enc_key083,
	  sizeof(enc_input083), sizeof(enc_assoc083), sizeof(enc_nonce083) },
	{ enc_input084, enc_output084, enc_assoc084, enc_nonce084, enc_key084,
	  sizeof(enc_input084), sizeof(enc_assoc084), sizeof(enc_nonce084) },
	{ enc_input085, enc_output085, enc_assoc085, enc_nonce085, enc_key085,
	  sizeof(enc_input085), sizeof(enc_assoc085), sizeof(enc_nonce085) },
	{ enc_input086, enc_output086, enc_assoc086, enc_nonce086, enc_key086,
	  sizeof(enc_input086), sizeof(enc_assoc086), sizeof(enc_nonce086) },
	{ enc_input087, enc_output087, enc_assoc087, enc_nonce087, enc_key087,
	  sizeof(enc_input087), sizeof(enc_assoc087), sizeof(enc_nonce087) },
	{ enc_input088, enc_output088, enc_assoc088, enc_nonce088, enc_key088,
	  sizeof(enc_input088), sizeof(enc_assoc088), sizeof(enc_nonce088) },
	{ enc_input089, enc_output089, enc_assoc089, enc_nonce089, enc_key089,
	  sizeof(enc_input089), sizeof(enc_assoc089), sizeof(enc_nonce089) },
	{ enc_input090, enc_output090, enc_assoc090, enc_nonce090, enc_key090,
	  sizeof(enc_input090), sizeof(enc_assoc090), sizeof(enc_nonce090) },
	{ enc_input091, enc_output091, enc_assoc091, enc_nonce091, enc_key091,
	  sizeof(enc_input091), sizeof(enc_assoc091), sizeof(enc_nonce091) },
	{ enc_input092, enc_output092, enc_assoc092, enc_nonce092, enc_key092,
	  sizeof(enc_input092), sizeof(enc_assoc092), sizeof(enc_nonce092) },
	{ enc_input093, enc_output093, enc_assoc093, enc_nonce093, enc_key093,
	  sizeof(enc_input093), sizeof(enc_assoc093), sizeof(enc_nonce093) },
	{ enc_input094, enc_output094, enc_assoc094, enc_nonce094, enc_key094,
	  sizeof(enc_input094), sizeof(enc_assoc094), sizeof(enc_nonce094) },
	{ enc_input095, enc_output095, enc_assoc095, enc_nonce095, enc_key095,
	  sizeof(enc_input095), sizeof(enc_assoc095), sizeof(enc_nonce095) },
	{ enc_input096, enc_output096, enc_assoc096, enc_nonce096, enc_key096,
	  sizeof(enc_input096), sizeof(enc_assoc096), sizeof(enc_nonce096) },
	{ enc_input097, enc_output097, enc_assoc097, enc_nonce097, enc_key097,
	  sizeof(enc_input097), sizeof(enc_assoc097), sizeof(enc_nonce097) },
	{ enc_input098, enc_output098, enc_assoc098, enc_nonce098, enc_key098,
	  sizeof(enc_input098), sizeof(enc_assoc098), sizeof(enc_nonce098) },
	{ enc_input099, enc_output099, enc_assoc099, enc_nonce099, enc_key099,
	  sizeof(enc_input099), sizeof(enc_assoc099), sizeof(enc_nonce099) },
	{ enc_input100, enc_output100, enc_assoc100, enc_nonce100, enc_key100,
	  sizeof(enc_input100), sizeof(enc_assoc100), sizeof(enc_nonce100) },
	{ enc_input101, enc_output101, enc_assoc101, enc_nonce101, enc_key101,
	  sizeof(enc_input101), sizeof(enc_assoc101), sizeof(enc_nonce101) },
	{ enc_input102, enc_output102, enc_assoc102, enc_nonce102, enc_key102,
	  sizeof(enc_input102), sizeof(enc_assoc102), sizeof(enc_nonce102) },
	{ enc_input103, enc_output103, enc_assoc103, enc_nonce103, enc_key103,
	  sizeof(enc_input103), sizeof(enc_assoc103), sizeof(enc_nonce103) },
	{ enc_input104, enc_output104, enc_assoc104, enc_nonce104, enc_key104,
	  sizeof(enc_input104), sizeof(enc_assoc104), sizeof(enc_nonce104) },
	{ enc_input105, enc_output105, enc_assoc105, enc_nonce105, enc_key105,
	  sizeof(enc_input105), sizeof(enc_assoc105), sizeof(enc_nonce105) },
	{ enc_input106, enc_output106, enc_assoc106, enc_nonce106, enc_key106,
	  sizeof(enc_input106), sizeof(enc_assoc106), sizeof(enc_nonce106) },
	{ enc_input107, enc_output107, enc_assoc107, enc_nonce107, enc_key107,
	  sizeof(enc_input107), sizeof(enc_assoc107), sizeof(enc_nonce107) },
	{ enc_input108, enc_output108, enc_assoc108, enc_nonce108, enc_key108,
	  sizeof(enc_input108), sizeof(enc_assoc108), sizeof(enc_nonce108) },
	{ enc_input109, enc_output109, enc_assoc109, enc_nonce109, enc_key109,
	  sizeof(enc_input109), sizeof(enc_assoc109), sizeof(enc_nonce109) },
	{ enc_input110, enc_output110, enc_assoc110, enc_nonce110, enc_key110,
	  sizeof(enc_input110), sizeof(enc_assoc110), sizeof(enc_nonce110) },
	{ enc_input111, enc_output111, enc_assoc111, enc_nonce111, enc_key111,
	  sizeof(enc_input111), sizeof(enc_assoc111), sizeof(enc_nonce111) },
	{ enc_input112, enc_output112, enc_assoc112, enc_nonce112, enc_key112,
	  sizeof(enc_input112), sizeof(enc_assoc112), sizeof(enc_nonce112) },
	{ enc_input113, enc_output113, enc_assoc113, enc_nonce113, enc_key113,
	  sizeof(enc_input113), sizeof(enc_assoc113), sizeof(enc_nonce113) },
	{ enc_input114, enc_output114, enc_assoc114, enc_nonce114, enc_key114,
	  sizeof(enc_input114), sizeof(enc_assoc114), sizeof(enc_nonce114) },
	{ enc_input115, enc_output115, enc_assoc115, enc_nonce115, enc_key115,
	  sizeof(enc_input115), sizeof(enc_assoc115), sizeof(enc_nonce115) },
	{ enc_input116, enc_output116, enc_assoc116, enc_nonce116, enc_key116,
	  sizeof(enc_input116), sizeof(enc_assoc116), sizeof(enc_nonce116) },
	{ enc_input117, enc_output117, enc_assoc117, enc_nonce117, enc_key117,
	  sizeof(enc_input117), sizeof(enc_assoc117), sizeof(enc_nonce117) },
	{ enc_input118, enc_output118, enc_assoc118, enc_nonce118, enc_key118,
	  sizeof(enc_input118), sizeof(enc_assoc118), sizeof(enc_nonce118) }
};

static const u8 dec_input001[] __initconst = {
	0x64, 0xa0, 0x86, 0x15, 0x75, 0x86, 0x1a, 0xf4,
	0x60, 0xf0, 0x62, 0xc7, 0x9b, 0xe6, 0x43, 0xbd,
	0x5e, 0x80, 0x5c, 0xfd, 0x34, 0x5c, 0xf3, 0x89,
	0xf1, 0x08, 0x67, 0x0a, 0xc7, 0x6c, 0x8c, 0xb2,
	0x4c, 0x6c, 0xfc, 0x18, 0x75, 0x5d, 0x43, 0xee,
	0xa0, 0x9e, 0xe9, 0x4e, 0x38, 0x2d, 0x26, 0xb0,
	0xbd, 0xb7, 0xb7, 0x3c, 0x32, 0x1b, 0x01, 0x00,
	0xd4, 0xf0, 0x3b, 0x7f, 0x35, 0x58, 0x94, 0xcf,
	0x33, 0x2f, 0x83, 0x0e, 0x71, 0x0b, 0x97, 0xce,
	0x98, 0xc8, 0xa8, 0x4a, 0xbd, 0x0b, 0x94, 0x81,
	0x14, 0xad, 0x17, 0x6e, 0x00, 0x8d, 0x33, 0xbd,
	0x60, 0xf9, 0x82, 0xb1, 0xff, 0x37, 0xc8, 0x55,
	0x97, 0x97, 0xa0, 0x6e, 0xf4, 0xf0, 0xef, 0x61,
	0xc1, 0x86, 0x32, 0x4e, 0x2b, 0x35, 0x06, 0x38,
	0x36, 0x06, 0x90, 0x7b, 0x6a, 0x7c, 0x02, 0xb0,
	0xf9, 0xf6, 0x15, 0x7b, 0x53, 0xc8, 0x67, 0xe4,
	0xb9, 0x16, 0x6c, 0x76, 0x7b, 0x80, 0x4d, 0x46,
	0xa5, 0x9b, 0x52, 0x16, 0xcd, 0xe7, 0xa4, 0xe9,
	0x90, 0x40, 0xc5, 0xa4, 0x04, 0x33, 0x22, 0x5e,
	0xe2, 0x82, 0xa1, 0xb0, 0xa0, 0x6c, 0x52, 0x3e,
	0xaf, 0x45, 0x34, 0xd7, 0xf8, 0x3f, 0xa1, 0x15,
	0x5b, 0x00, 0x47, 0x71, 0x8c, 0xbc, 0x54, 0x6a,
	0x0d, 0x07, 0x2b, 0x04, 0xb3, 0x56, 0x4e, 0xea,
	0x1b, 0x42, 0x22, 0x73, 0xf5, 0x48, 0x27, 0x1a,
	0x0b, 0xb2, 0x31, 0x60, 0x53, 0xfa, 0x76, 0x99,
	0x19, 0x55, 0xeb, 0xd6, 0x31, 0x59, 0x43, 0x4e,
	0xce, 0xbb, 0x4e, 0x46, 0x6d, 0xae, 0x5a, 0x10,
	0x73, 0xa6, 0x72, 0x76, 0x27, 0x09, 0x7a, 0x10,
	0x49, 0xe6, 0x17, 0xd9, 0x1d, 0x36, 0x10, 0x94,
	0xfa, 0x68, 0xf0, 0xff, 0x77, 0x98, 0x71, 0x30,
	0x30, 0x5b, 0xea, 0xba, 0x2e, 0xda, 0x04, 0xdf,
	0x99, 0x7b, 0x71, 0x4d, 0x6c, 0x6f, 0x2c, 0x29,
	0xa6, 0xad, 0x5c, 0xb4, 0x02, 0x2b, 0x02, 0x70,
	0x9b, 0xee, 0xad, 0x9d, 0x67, 0x89, 0x0c, 0xbb,
	0x22, 0x39, 0x23, 0x36, 0xfe, 0xa1, 0x85, 0x1f,
	0x38
};
static const u8 dec_output001[] __initconst = {
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74,
	0x2d, 0x44, 0x72, 0x61, 0x66, 0x74, 0x73, 0x20,
	0x61, 0x72, 0x65, 0x20, 0x64, 0x72, 0x61, 0x66,
	0x74, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x61, 0x20,
	0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x20,
	0x6f, 0x66, 0x20, 0x73, 0x69, 0x78, 0x20, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x20, 0x61, 0x6e,
	0x64, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65,
	0x20, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x2c, 0x20, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x64, 0x2c, 0x20, 0x6f, 0x72, 0x20, 0x6f,
	0x62, 0x73, 0x6f, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x20, 0x62, 0x79, 0x20, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x61, 0x74, 0x20, 0x61,
	0x6e, 0x79, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x20, 0x49, 0x74, 0x20, 0x69, 0x73, 0x20, 0x69,
	0x6e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x70, 0x72,
	0x69, 0x61, 0x74, 0x65, 0x20, 0x74, 0x6f, 0x20,
	0x75, 0x73, 0x65, 0x20, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x65, 0x74, 0x2d, 0x44, 0x72, 0x61,
	0x66, 0x74, 0x73, 0x20, 0x61, 0x73, 0x20, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x20, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x20, 0x6f, 0x72, 0x20, 0x74, 0x6f, 0x20,
	0x63, 0x69, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65,
	0x6d, 0x20, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x61, 0x73, 0x20,
	0x2f, 0xe2, 0x80, 0x9c, 0x77, 0x6f, 0x72, 0x6b,
	0x20, 0x69, 0x6e, 0x20, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x2f, 0xe2, 0x80,
	0x9d
};
static const u8 dec_assoc001[] __initconst = {
	0xf3, 0x33, 0x88, 0x86, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x4e, 0x91
};
static const u8 dec_nonce001[] __initconst = {
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08
};
static const u8 dec_key001[] __initconst = {
	0x1c, 0x92, 0x40, 0xa5, 0xeb, 0x55, 0xd3, 0x8a,
	0xf3, 0x33, 0x88, 0x86, 0x04, 0xf6, 0xb5, 0xf0,
	0x47, 0x39, 0x17, 0xc1, 0x40, 0x2b, 0x80, 0x09,
	0x9d, 0xca, 0x5c, 0xbc, 0x20, 0x70, 0x75, 0xc0
};

static const u8 dec_input002[] __initconst = {
	0xea, 0xe0, 0x1e, 0x9e, 0x2c, 0x91, 0xaa, 0xe1,
	0xdb, 0x5d, 0x99, 0x3f, 0x8a, 0xf7, 0x69, 0x92
};
static const u8 dec_output002[] __initconst = { };
static const u8 dec_assoc002[] __initconst = { };
static const u8 dec_nonce002[] __initconst = {
	0xca, 0xbf, 0x33, 0x71, 0x32, 0x45, 0x77, 0x8e
};
static const u8 dec_key002[] __initconst = {
	0x4c, 0xf5, 0x96, 0x83, 0x38, 0xe6, 0xae, 0x7f,
	0x2d, 0x29, 0x25, 0x76, 0xd5, 0x75, 0x27, 0x86,
	0x91, 0x9a, 0x27, 0x7a, 0xfb, 0x46, 0xc5, 0xef,
	0x94, 0x81, 0x79, 0x57, 0x14, 0x59, 0x40, 0x68
};

static const u8 dec_input003[] __initconst = {
	0xdd, 0x6b, 0x3b, 0x82, 0xce, 0x5a, 0xbd, 0xd6,
	0xa9, 0x35, 0x83, 0xd8, 0x8c, 0x3d, 0x85, 0x77
};
static const u8 dec_output003[] __initconst = { };
static const u8 dec_assoc003[] __initconst = {
	0x33, 0x10, 0x41, 0x12, 0x1f, 0xf3, 0xd2, 0x6b
};
static const u8 dec_nonce003[] __initconst = {
	0x3d, 0x86, 0xb5, 0x6b, 0xc8, 0xa3, 0x1f, 0x1d
};
static const u8 dec_key003[] __initconst = {
	0x2d, 0xb0, 0x5d, 0x40, 0xc8, 0xed, 0x44, 0x88,
	0x34, 0xd1, 0x13, 0xaf, 0x57, 0xa1, 0xeb, 0x3a,
	0x2a, 0x80, 0x51, 0x36, 0xec, 0x5b, 0xbc, 0x08,
	0x93, 0x84, 0x21, 0xb5, 0x13, 0x88, 0x3c, 0x0d
};

static const u8 dec_input004[] __initconst = {
	0xb7, 0x1b, 0xb0, 0x73, 0x59, 0xb0, 0x84, 0xb2,
	0x6d, 0x8e, 0xab, 0x94, 0x31, 0xa1, 0xae, 0xac,
	0x89
};
static const u8 dec_output004[] __initconst = {
	0xa4
};
static const u8 dec_assoc004[] __initconst = {
	0x6a, 0xe2, 0xad, 0x3f, 0x88, 0x39, 0x5a, 0x40
};
static const u8 dec_nonce004[] __initconst = {
	0xd2, 0x32, 0x1f, 0x29, 0x28, 0xc6, 0xc4, 0xc4
};
static const u8 dec_key004[] __initconst = {
	0x4b, 0x28, 0x4b, 0xa3, 0x7b, 0xbe, 0xe9, 0xf8,
	0x31, 0x80, 0x82, 0xd7, 0xd8, 0xe8, 0xb5, 0xa1,
	0xe2, 0x18, 0x18, 0x8a, 0x9c, 0xfa, 0xa3, 0x3d,
	0x25, 0x71, 0x3e, 0x40, 0xbc, 0x54, 0x7a, 0x3e
};

static const u8 dec_input005[] __initconst = {
	0xbf, 0xe1, 0x5b, 0x0b, 0xdb, 0x6b, 0xf5, 0x5e,
	0x6c, 0x5d, 0x84, 0x44, 0x39, 0x81, 0xc1, 0x9c,
	0xac
};
static const u8 dec_output005[] __initconst = {
	0x2d
};
static const u8 dec_assoc005[] __initconst = { };
static const u8 dec_nonce005[] __initconst = {
	0x20, 0x1c, 0xaa, 0x5f, 0x9c, 0xbf, 0x92, 0x30
};
static const u8 dec_key005[] __initconst = {
	0x66, 0xca, 0x9c, 0x23, 0x2a, 0x4b, 0x4b, 0x31,
	0x0e, 0x92, 0x89, 0x8b, 0xf4, 0x93, 0xc7, 0x87,
	0x98, 0xa3, 0xd8, 0x39, 0xf8, 0xf4, 0xa7, 0x01,
	0xc0, 0x2e, 0x0a, 0xa6, 0x7e, 0x5a, 0x78, 0x87
};

static const u8 dec_input006[] __initconst = {
	0x8b, 0x06, 0xd3, 0x31, 0xb0, 0x93, 0x45, 0xb1,
	0x75, 0x6e, 0x26, 0xf9, 0x67, 0xbc, 0x90, 0x15,
	0x81, 0x2c, 0xb5, 0xf0, 0xc6, 0x2b, 0xc7, 0x8c,
	0x56, 0xd1, 0xbf, 0x69, 0x6c, 0x07, 0xa0, 0xda,
	0x65, 0x27, 0xc9, 0x90, 0x3d, 0xef, 0x4b, 0x11,
	0x0f, 0x19, 0x07, 0xfd, 0x29, 0x92, 0xd9, 0xc8,
	0xf7, 0x99, 0x2e, 0x4a, 0xd0, 0xb8, 0x2c, 0xdc,
	0x93, 0xf5, 0x9e, 0x33, 0x78, 0xd1, 0x37, 0xc3,
	0x66, 0xd7, 0x5e, 0xbc, 0x44, 0xbf, 0x53, 0xa5,
	0xbc, 0xc4, 0xcb, 0x7b, 0x3a, 0x8e, 0x7f, 0x02,
	0xbd, 0xbb, 0xe7, 0xca, 0xa6, 0x6c, 0x6b, 0x93,
	0x21, 0x93, 0x10, 0x61, 0xe7, 0x69, 0xd0, 0x78,
	0xf3, 0x07, 0x5a, 0x1a, 0x8f, 0x73, 0xaa, 0xb1,
	0x4e, 0xd3, 0xda, 0x4f, 0xf3, 0x32, 0xe1, 0x66,
	0x3e, 0x6c, 0xc6, 0x13, 0xba, 0x06, 0x5b, 0xfc,
	0x6a, 0xe5, 0x6f, 0x60, 0xfb, 0x07, 0x40, 0xb0,
	0x8c, 0x9d, 0x84, 0x43, 0x6b, 0xc1, 0xf7, 0x8d,
	0x8d, 0x31, 0xf7, 0x7a, 0x39, 0x4d, 0x8f, 0x9a,
	0xeb
};
static const u8 dec_output006[] __initconst = {
	0x33, 0x2f, 0x94, 0xc1, 0xa4, 0xef, 0xcc, 0x2a,
	0x5b, 0xa6, 0xe5, 0x8f, 0x1d, 0x40, 0xf0, 0x92,
	0x3c, 0xd9, 0x24, 0x11, 0xa9, 0x71, 0xf9, 0x37,
	0x14, 0x99, 0xfa, 0xbe, 0xe6, 0x80, 0xde, 0x50,
	0xc9, 0x96, 0xd4, 0xb0, 0xec, 0x9e, 0x17, 0xec,
	0xd2, 0x5e, 0x72, 0x99, 0xfc, 0x0a, 0xe1, 0xcb,
	0x48, 0xd2, 0x85, 0xdd, 0x2f, 0x90, 0xe0, 0x66,
	0x3b, 0xe6, 0x20, 0x74, 0xbe, 0x23, 0x8f, 0xcb,
	0xb4, 0xe4, 0xda, 0x48, 0x40, 0xa6, 0xd1, 0x1b,
	0xc7, 0x42, 0xce, 0x2f, 0x0c, 0xa6, 0x85, 0x6e,
	0x87, 0x37, 0x03, 0xb1, 0x7c, 0x25, 0x96, 0xa3,
	0x05, 0xd8, 0xb0, 0xf4, 0xed, 0xea, 0xc2, 0xf0,
	0x31, 0x98, 0x6c, 0xd1, 0x14, 0x25, 0xc0, 0xcb,
	0x01, 0x74, 0xd0, 0x82, 0xf4, 0x36, 0xf5, 0x41,
	0xd5, 0xdc, 0xca, 0xc5, 0xbb, 0x98, 0xfe, 0xfc,
	0x69, 0x21, 0x70, 0xd8, 0xa4, 0x4b, 0xc8, 0xde,
	0x8f
};
static const u8 dec_assoc006[] __initconst = {
	0x70, 0xd3, 0x33, 0xf3, 0x8b, 0x18, 0x0b
};
static const u8 dec_nonce006[] __initconst = {
	0xdf, 0x51, 0x84, 0x82, 0x42, 0x0c, 0x75, 0x9c
};
static const u8 dec_key006[] __initconst = {
	0x68, 0x7b, 0x8d, 0x8e, 0xe3, 0xc4, 0xdd, 0xae,
	0xdf, 0x72, 0x7f, 0x53, 0x72, 0x25, 0x1e, 0x78,
	0x91, 0xcb, 0x69, 0x76, 0x1f, 0x49, 0x93, 0xf9,
	0x6f, 0x21, 0xcc, 0x39, 0x9c, 0xad, 0xb1, 0x01
};

static const u8 dec_input007[] __initconst = {
	0x85, 0x04, 0xc2, 0xed, 0x8d, 0xfd, 0x97, 0x5c,
	0xd2, 0xb7, 0xe2, 0xc1, 0x6b, 0xa3, 0xba, 0xf8,
	0xc9, 0x50, 0xc3, 0xc6, 0xa5, 0xe3, 0xa4, 0x7c,
	0xc3, 0x23, 0x49, 0x5e, 0xa9, 0xb9, 0x32, 0xeb,
	0x8a, 0x7c, 0xca, 0xe5, 0xec, 0xfb, 0x7c, 0xc0,
	0xcb, 0x7d, 0xdc, 0x2c, 0x9d, 0x92, 0x55, 0x21,
	0x0a, 0xc8, 0x43, 0x63, 0x59, 0x0a, 0x31, 0x70,
	0x82, 0x67, 0x41, 0x03, 0xf8, 0xdf, 0xf2, 0xac,
	0xa7, 0x02, 0xd4, 0xd5, 0x8a, 0x2d, 0xc8, 0x99,
	0x19, 0x66, 0xd0, 0xf6, 0x88, 0x2c, 0x77, 0xd9,
	0xd4, 0x0d, 0x6c, 0xbd, 0x98, 0xde, 0xe7, 0x7f,
	0xad, 0x7e, 0x8a, 0xfb, 0xe9, 0x4b, 0xe5, 0xf7,
	0xe5, 0x50, 0xa0, 0x90, 0x3f, 0xd6, 0x22, 0x53,
	0xe3, 0xfe, 0x1b, 0xcc, 0x79, 0x3b, 0xec, 0x12,
	0x47, 0x52, 0xa7, 0xd6, 0x04, 0xe3, 0x52, 0xe6,
	0x93, 0x90, 0x91, 0x32, 0x73, 0x79, 0xb8, 0xd0,
	0x31, 0xde, 0x1f, 0x9f, 0x2f, 0x05, 0x38, 0x54,
	0x2f, 0x35, 0x04, 0x39, 0xe0, 0xa7, 0xba, 0xc6,
	0x52, 0xf6, 0x37, 0x65, 0x4c, 0x07, 0xa9, 0x7e,
	0xb3, 0x21, 0x6f, 0x74, 0x8c, 0xc9, 0xde, 0xdb,
	0x65, 0x1b, 0x9b, 0xaa, 0x60, 0xb1, 0x03, 0x30,
	0x6b, 0xb2, 0x03, 0xc4, 0x1c, 0x04, 0xf8, 0x0f,
	0x64, 0xaf, 0x46, 0xe4, 0x65, 0x99, 0x49, 0xe2,
	0xea, 0xce, 0x78, 0x00, 0xd8, 0x8b, 0xd5, 0x2e,
	0xcf, 0xfc, 0x40, 0x49, 0xe8, 0x58, 0xdc, 0x34,
	0x9c, 0x8c, 0x61, 0xbf, 0x0a, 0x8e, 0xec, 0x39,
	0xa9, 0x30, 0x05, 0x5a, 0xd2, 0x56, 0x01, 0xc7,
	0xda, 0x8f, 0x4e, 0xbb, 0x43, 0xa3, 0x3a, 0xf9,
	0x15, 0x2a, 0xd0, 0xa0, 0x7a, 0x87, 0x34, 0x82,
	0xfe, 0x8a, 0xd1, 0x2d, 0x5e, 0xc7, 0xbf, 0x04,
	0x53, 0x5f, 0x3b, 0x36, 0xd4, 0x25, 0x5c, 0x34,
	0x7a, 0x8d, 0xd5, 0x05, 0xce, 0x72, 0xca, 0xef,
	0x7a, 0x4b, 0xbc, 0xb0, 0x10, 0x5c, 0x96, 0x42,
	0x3a, 0x00, 0x98, 0xcd, 0x15, 0xe8, 0xb7, 0x53
};
static const u8 dec_output007[] __initconst = {
	0x9b, 0x18, 0xdb, 0xdd, 0x9a, 0x0f, 0x3e, 0xa5,
	0x15, 0x17, 0xde, 0xdf, 0x08, 0x9d, 0x65, 0x0a,
	0x67, 0x30, 0x12, 0xe2, 0x34, 0x77, 0x4b, 0xc1,
	0xd9, 0xc6, 0x1f, 0xab, 0xc6, 0x18, 0x50, 0x17,
	0xa7, 0x9d, 0x3c, 0xa6, 0xc5, 0x35, 0x8c, 0x1c,
	0xc0, 0xa1, 0x7c, 0x9f, 0x03, 0x89, 0xca, 0xe1,
	0xe6, 0xe9, 0xd4, 0xd3, 0x88, 0xdb, 0xb4, 0x51,
	0x9d, 0xec, 0xb4, 0xfc, 0x52, 0xee, 0x6d, 0xf1,
	0x75, 0x42, 0xc6, 0xfd, 0xbd, 0x7a, 0x8e, 0x86,
	0xfc, 0x44, 0xb3, 0x4f, 0xf3, 0xea, 0x67, 0x5a,
	0x41, 0x13, 0xba, 0xb0, 0xdc, 0xe1, 0xd3, 0x2a,
	0x7c, 0x22, 0xb3, 0xca, 0xac, 0x6a, 0x37, 0x98,
	0x3e, 0x1d, 0x40, 0x97, 0xf7, 0x9b, 0x1d, 0x36,
	0x6b, 0xb3, 0x28, 0xbd, 0x60, 0x82, 0x47, 0x34,
	0xaa, 0x2f, 0x7d, 0xe9, 0xa8, 0x70, 0x81, 0x57,
	0xd4, 0xb9, 0x77, 0x0a, 0x9d, 0x29, 0xa7, 0x84,
	0x52, 0x4f, 0xc2, 0x4a, 0x40, 0x3b, 0x3c, 0xd4,
	0xc9, 0x2a, 0xdb, 0x4a, 0x53, 0xc4, 0xbe, 0x80,
	0xe9, 0x51, 0x7f, 0x8f, 0xc7, 0xa2, 0xce, 0x82,
	0x5c, 0x91, 0x1e, 0x74, 0xd9, 0xd0, 0xbd, 0xd5,
	0xf3, 0xfd, 0xda, 0x4d, 0x25, 0xb4, 0xbb, 0x2d,
	0xac, 0x2f, 0x3d, 0x71, 0x85, 0x7b, 0xcf, 0x3c,
	0x7b, 0x3e, 0x0e, 0x22, 0x78, 0x0c, 0x29, 0xbf,
	0xe4, 0xf4, 0x57, 0xb3, 0xcb, 0x49, 0xa0, 0xfc,
	0x1e, 0x05, 0x4e, 0x16, 0xbc, 0xd5, 0xa8, 0xa3,
	0xee, 0x05, 0x35, 0xc6, 0x7c, 0xab, 0x60, 0x14,
	0x55, 0x1a, 0x8e, 0xc5, 0x88, 0x5d, 0xd5, 0x81,
	0xc2, 0x81, 0xa5, 0xc4, 0x60, 0xdb, 0xaf, 0x77,
	0x91, 0xe1, 0xce, 0xa2, 0x7e, 0x7f, 0x42, 0xe3,
	0xb0, 0x13, 0x1c, 0x1f, 0x25, 0x60, 0x21, 0xe2,
	0x40, 0x5f, 0x99, 0xb7, 0x73, 0xec, 0x9b, 0x2b,
	0xf0, 0x65, 0x11, 0xc8, 0xd0, 0x0a, 0x9f, 0xd3
};
static const u8 dec_assoc007[] __initconst = { };
static const u8 dec_nonce007[] __initconst = {
	0xde, 0x7b, 0xef, 0xc3, 0x65, 0x1b, 0x68, 0xb0
};
static const u8 dec_key007[] __initconst = {
	0x8d, 0xb8, 0x91, 0x48, 0xf0, 0xe7, 0x0a, 0xbd,
	0xf9, 0x3f, 0xcd, 0xd9, 0xa0, 0x1e, 0x42, 0x4c,
	0xe7, 0xde, 0x25, 0x3d, 0xa3, 0xd7, 0x05, 0x80,
	0x8d, 0xf2, 0x82, 0xac, 0x44, 0x16, 0x51, 0x01
};

static const u8 dec_input008[] __initconst = {
	0x14, 0xf6, 0x41, 0x37, 0xa6, 0xd4, 0x27, 0xcd,
	0xdb, 0x06, 0x3e, 0x9a, 0x4e, 0xab, 0xd5, 0xb1,
	0x1e, 0x6b, 0xd2, 0xbc, 0x11, 0xf4, 0x28, 0x93,
	0x63, 0x54, 0xef, 0xbb, 0x5e, 0x1d, 0x3a, 0x1d,
	0x37, 0x3c, 0x0a, 0x6c, 0x1e, 0xc2, 0xd1, 0x2c,
	0xb5, 0xa3, 0xb5, 0x7b, 0xb8, 0x8f, 0x25, 0xa6,
	0x1b, 0x61, 0x1c, 0xec, 0x28, 0x58, 0x26, 0xa4,
	0xa8, 0x33, 0x28, 0x25, 0x5c, 0x45, 0x05, 0xe5,
	0x6c, 0x99, 0xe5, 0x45, 0xc4, 0xa2, 0x03, 0x84,
	0x03, 0x73, 0x1e, 0x8c, 0x49, 0xac, 0x20, 0xdd,
	0x8d, 0xb3, 0xc4, 0xf5, 0xe7, 0x4f, 0xf1, 0xed,
	0xa1, 0x98, 0xde, 0xa4, 0x96, 0xdd, 0x2f, 0xab,
	0xab, 0x97, 0xcf, 0x3e, 0xd2, 0x9e, 0xb8, 0x13,
	0x07, 0x28, 0x29, 0x19, 0xaf, 0xfd, 0xf2, 0x49,
	0x43, 0xea, 0x49, 0x26, 0x91, 0xc1, 0x07, 0xd6,
	0xbb, 0x81, 0x75, 0x35, 0x0d, 0x24, 0x7f, 0xc8,
	0xda, 0xd4, 0xb7, 0xeb, 0xe8, 0x5c, 0x09, 0xa2,
	0x2f, 0xdc, 0x28, 0x7d, 0x3a, 0x03, 0xfa, 0x94,
	0xb5, 0x1d, 0x17, 0x99, 0x36, 0xc3, 0x1c, 0x18,
	0x34, 0xe3, 0x9f, 0xf5, 0x55, 0x7c, 0xb0, 0x60,
	0x9d, 0xff, 0xac, 0xd4, 0x61, 0xf2, 0xad, 0xf8,
	0xce, 0xc7, 0xbe, 0x5c, 0xd2, 0x95, 0xa8, 0x4b,
	0x77, 0x13, 0x19, 0x59, 0x26, 0xc9, 0xb7, 0x8f,
	0x6a, 0xcb, 0x2d, 0x37, 0x91, 0xea, 0x92, 0x9c,
	0x94, 0x5b, 0xda, 0x0b, 0xce, 0xfe, 0x30, 0x20,
	0xf8, 0x51, 0xad, 0xf2, 0xbe, 0xe7, 0xc7, 0xff,
	0xb3, 0x33, 0x91, 0x6a, 0xc9, 0x1a, 0x41, 0xc9,
	0x0f, 0xf3, 0x10, 0x0e, 0xfd, 0x53, 0xff, 0x6c,
	0x16, 0x52, 0xd9, 0xf3, 0xf7, 0x98, 0x2e, 0xc9,
	0x07, 0x31, 0x2c, 0x0c, 0x72, 0xd7, 0xc5, 0xc6,
	0x08, 0x2a, 0x7b, 0xda, 0xbd, 0x7e, 0x02, 0xea,
	0x1a, 0xbb, 0xf2, 0x04, 0x27, 0x61, 0x28, 0x8e,
	0xf5, 0x04, 0x03, 0x1f, 0x4c, 0x07, 0x55, 0x82,
	0xec, 0x1e, 0xd7, 0x8b, 0x2f, 0x65, 0x56, 0xd1,
	0xd9, 0x1e, 0x3c, 0xe9, 0x1f, 0x5e, 0x98, 0x70,
	0x38, 0x4a, 0x8c, 0x49, 0xc5, 0x43, 0xa0, 0xa1,
	0x8b, 0x74, 0x9d, 0x4c, 0x62, 0x0d, 0x10, 0x0c,
	0xf4, 0x6c, 0x8f, 0xe0, 0xaa, 0x9a, 0x8d, 0xb7,
	0xe0, 0xbe, 0x4c, 0x87, 0xf1, 0x98, 0x2f, 0xcc,
	0xed, 0xc0, 0x52, 0x29, 0xdc, 0x83, 0xf8, 0xfc,
	0x2c, 0x0e, 0xa8, 0x51, 0x4d, 0x80, 0x0d, 0xa3,
	0xfe, 0xd8, 0x37, 0xe7, 0x41, 0x24, 0xfc, 0xfb,
	0x75, 0xe3, 0x71, 0x7b, 0x57, 0x45, 0xf5, 0x97,
	0x73, 0x65, 0x63, 0x14, 0x74, 0xb8, 0x82, 0x9f,
	0xf8, 0x60, 0x2f, 0x8a, 0xf2, 0x4e, 0xf1, 0x39,
	0xda, 0x33, 0x91, 0xf8, 0x36, 0xe0, 0x8d, 0x3f,
	0x1f, 0x3b, 0x56, 0xdc, 0xa0, 0x8f, 0x3c, 0x9d,
	0x71, 0x52, 0xa7, 0xb8, 0xc0, 0xa5, 0xc6, 0xa2,
	0x73, 0xda, 0xf4, 0x4b, 0x74, 0x5b, 0x00, 0x3d,
	0x99, 0xd7, 0x96, 0xba, 0xe6, 0xe1, 0xa6, 0x96,
	0x38, 0xad, 0xb3, 0xc0, 0xd2, 0xba, 0x91, 0x6b,
	0xf9, 0x19, 0xdd, 0x3b, 0xbe, 0xbe, 0x9c, 0x20,
	0x50, 0xba, 0xa1, 0xd0, 0xce, 0x11, 0xbd, 0x95,
	0xd8, 0xd1, 0xdd, 0x33, 0x85, 0x74, 0xdc, 0xdb,
	0x66, 0x76, 0x44, 0xdc, 0x03, 0x74, 0x48, 0x35,
	0x98, 0xb1, 0x18, 0x47, 0x94, 0x7d, 0xff, 0x62,
	0xe4, 0x58, 0x78, 0xab, 0xed, 0x95, 0x36, 0xd9,
	0x84, 0x91, 0x82, 0x64, 0x41, 0xbb, 0x58, 0xe6,
	0x1c, 0x20, 0x6d, 0x15, 0x6b, 0x13, 0x96, 0xe8,
	0x35, 0x7f, 0xdc, 0x40, 0x2c, 0xe9, 0xbc, 0x8a,
	0x4f, 0x92, 0xec, 0x06, 0x2d, 0x50, 0xdf, 0x93,
	0x5d, 0x65, 0x5a, 0xa8, 0xfc, 0x20, 0x50, 0x14,
	0xa9, 0x8a, 0x7e, 0x1d, 0x08, 0x1f, 0xe2, 0x99,
	0xd0, 0xbe, 0xfb, 0x3a, 0x21, 0x9d, 0xad, 0x86,
	0x54, 0xfd, 0x0d, 0x98, 0x1c, 0x5a, 0x6f, 0x1f,
	0x9a, 0x40, 0xcd, 0xa2, 0xff, 0x6a, 0xf1, 0x54
};
static const u8 dec_output008[] __initconst = {
	0xc3, 0x09, 0x94, 0x62, 0xe6, 0x46, 0x2e, 0x10,
	0xbe, 0x00, 0xe4, 0xfc, 0xf3, 0x40, 0xa3, 0xe2,
	0x0f, 0xc2, 0x8b, 0x28, 0xdc, 0xba, 0xb4, 0x3c,
	0xe4, 0x21, 0x58, 0x61, 0xcd, 0x8b, 0xcd, 0xfb,
	0xac, 0x94, 0xa1, 0x45, 0xf5, 0x1c, 0xe1, 0x12,
	0xe0, 0x3b, 0x67, 0x21, 0x54, 0x5e, 0x8c, 0xaa,
	0xcf, 0xdb, 0xb4, 0x51, 0xd4, 0x13, 0xda, 0xe6,
	0x83, 0x89, 0xb6, 0x92, 0xe9, 0x21, 0x76, 0xa4,
	0x93, 0x7d, 0x0e, 0xfd, 0x96, 0x36, 0x03, 0x91,
	0x43, 0x5c, 0x92, 0x49, 0x62, 0x61, 0x7b, 0xeb,
	0x43, 0x89, 0xb8, 0x12, 0x20, 0x43, 0xd4, 0x47,
	0x06, 0x84, 0xee, 0x47, 0xe9, 0x8a, 0x73, 0x15,
	0x0f, 0x72, 0xcf, 0xed, 0xce, 0x96, 0xb2, 0x7f,
	0x21, 0x45, 0x76, 0xeb, 0x26, 0x28, 0x83, 0x6a,
	0xad, 0xaa, 0xa6, 0x81, 0xd8, 0x55, 0xb1, 0xa3,
	0x85, 0xb3, 0x0c, 0xdf, 0xf1, 0x69, 0x2d, 0x97,
	0x05, 0x2a, 0xbc, 0x7c, 0x7b, 0x25, 0xf8, 0x80,
	0x9d, 0x39, 0x25, 0xf3, 0x62, 0xf0, 0x66, 0x5e,
	0xf4, 0xa0, 0xcf, 0xd8, 0xfd, 0x4f, 0xb1, 0x1f,
	0x60, 0x3a, 0x08, 0x47, 0xaf, 0xe1, 0xf6, 0x10,
	0x77, 0x09, 0xa7, 0x27, 0x8f, 0x9a, 0x97, 0x5a,
	0x26, 0xfa, 0xfe, 0x41, 0x32, 0x83, 0x10, 0xe0,
	0x1d, 0xbf, 0x64, 0x0d, 0xf4, 0x1c, 0x32, 0x35,
	0xe5, 0x1b, 0x36, 0xef, 0xd4, 0x4a, 0x93, 0x4d,
	0x00, 0x7c, 0xec, 0x02, 0x07, 0x8b, 0x5d, 0x7d,
	0x1b, 0x0e, 0xd1, 0xa6, 0xa5, 0x5d, 0x7d, 0x57,
	0x88, 0xa8, 0xcc, 0x81, 0xb4, 0x86, 0x4e, 0xb4,
	0x40, 0xe9, 0x1d, 0xc3, 0xb1, 0x24, 0x3e, 0x7f,
	0xcc, 0x8a, 0x24, 0x9b, 0xdf, 0x6d, 0xf0, 0x39,
	0x69, 0x3e, 0x4c, 0xc0, 0x96, 0xe4, 0x13, 0xda,
	0x90, 0xda, 0xf4, 0x95, 0x66, 0x8b, 0x17, 0x17,
	0xfe, 0x39, 0x43, 0x25, 0xaa, 0xda, 0xa0, 0x43,
	0x3c, 0xb1, 0x41, 0x02, 0xa3, 0xf0, 0xa7, 0x19,
	0x59, 0xbc, 0x1d, 0x7d, 0x6c, 0x6d, 0x91, 0x09,
	0x5c, 0xb7, 0x5b, 0x01, 0xd1, 0x6f, 0x17, 0x21,
	0x97, 0xbf, 0x89, 0x71, 0xa5, 0xb0, 0x6e, 0x07,
	0x45, 0xfd, 0x9d, 0xea, 0x07, 0xf6, 0x7a, 0x9f,
	0x10, 0x18, 0x22, 0x30, 0x73, 0xac, 0xd4, 0x6b,
	0x72, 0x44, 0xed, 0xd9, 0x19, 0x9b, 0x2d, 0x4a,
	0x41, 0xdd, 0xd1, 0x85, 0x5e, 0x37, 0x19, 0xed,
	0xd2, 0x15, 0x8f, 0x5e, 0x91, 0xdb, 0x33, 0xf2,
	0xe4, 0xdb, 0xff, 0x98, 0xfb, 0xa3, 0xb5, 0xca,
	0x21, 0x69, 0x08, 0xe7, 0x8a, 0xdf, 0x90, 0xff,
	0x3e, 0xe9, 0x20, 0x86, 0x3c, 0xe9, 0xfc, 0x0b,
	0xfe, 0x5c, 0x61, 0xaa, 0x13, 0x92, 0x7f, 0x7b,
	0xec, 0xe0, 0x6d, 0xa8, 0x23, 0x22, 0xf6, 0x6b,
	0x77, 0xc4, 0xfe, 0x40, 0x07, 0x3b, 0xb6, 0xf6,
	0x8e, 0x5f, 0xd4, 0xb9, 0xb7, 0x0f, 0x21, 0x04,
	0xef, 0x83, 0x63, 0x91, 0x69, 0x40, 0xa3, 0x48,
	0x5c, 0xd2, 0x60, 0xf9, 0x4f, 0x6c, 0x47, 0x8b,
	0x3b, 0xb1, 0x9f, 0x8e, 0xee, 0x16, 0x8a, 0x13,
	0xfc, 0x46, 0x17, 0xc3, 0xc3, 0x32, 0x56, 0xf8,
	0x3c, 0x85, 0x3a, 0xb6, 0x3e, 0xaa, 0x89, 0x4f,
	0xb3, 0xdf, 0x38, 0xfd, 0xf1, 0xe4, 0x3a, 0xc0,
	0xe6, 0x58, 0xb5, 0x8f, 0xc5, 0x29, 0xa2, 0x92,
	0x4a, 0xb6, 0xa0, 0x34, 0x7f, 0xab, 0xb5, 0x8a,
	0x90, 0xa1, 0xdb, 0x4d, 0xca, 0xb6, 0x2c, 0x41,
	0x3c, 0xf7, 0x2b, 0x21, 0xc3, 0xfd, 0xf4, 0x17,
	0x5c, 0xb5, 0x33, 0x17, 0x68, 0x2b, 0x08, 0x30,
	0xf3, 0xf7, 0x30, 0x3c, 0x96, 0xe6, 0x6a, 0x20,
	0x97, 0xe7, 0x4d, 0x10, 0x5f, 0x47, 0x5f, 0x49,
	0x96, 0x09, 0xf0, 0x27, 0x91, 0xc8, 0xf8, 0x5a,
	0x2e, 0x79, 0xb5, 0xe2, 0xb8, 0xe8, 0xb9, 0x7b,
	0xd5, 0x10, 0xcb, 0xff, 0x5d, 0x14, 0x73, 0xf3
};
static const u8 dec_assoc008[] __initconst = { };
static const u8 dec_nonce008[] __initconst = {
	0x0e, 0x0d, 0x57, 0xbb, 0x7b, 0x40, 0x54, 0x02
};
static const u8 dec_key008[] __initconst = {
	0xf2, 0xaa, 0x4f, 0x99, 0xfd, 0x3e, 0xa8, 0x53,
	0xc1, 0x44, 0xe9, 0x81, 0x18, 0xdc, 0xf5, 0xf0,
	0x3e, 0x44, 0x15, 0x59, 0xe0, 0xc5, 0x44, 0x86,
	0xc3, 0x91, 0xa8, 0x75, 0xc0, 0x12, 0x46, 0xba
};

static const u8 dec_input009[] __initconst = {
	0xfd, 0x81, 0x8d, 0xd0, 0x3d, 0xb4, 0xd5, 0xdf,
	0xd3, 0x42, 0x47, 0x5a, 0x6d, 0x19, 0x27, 0x66,
	0x4b, 0x2e, 0x0c, 0x27, 0x9c, 0x96, 0x4c, 0x72,
	0x02, 0xa3, 0x65, 0xc3, 0xb3, 0x6f, 0x2e, 0xbd,
	0x63, 0x8a, 0x4a, 0x5d, 0x29, 0xa2, 0xd0, 0x28,
	0x48, 0xc5, 0x3d, 0x98, 0xa3, 0xbc, 0xe0, 0xbe,
	0x3b, 0x3f, 0xe6, 0x8a, 0xa4, 0x7f, 0x53, 0x06,
	0xfa, 0x7f, 0x27, 0x76, 0x72, 0x31, 0xa1, 0xf5,
	0xd6, 0x0c, 0x52, 0x47, 0xba, 0xcd, 0x4f, 0xd7,
	0xeb, 0x05, 0x48, 0x0d, 0x7c, 0x35, 0x4a, 0x09,
	0xc9, 0x76, 0x71, 0x02, 0xa3, 0xfb, 0xb7, 0x1a,
	0x65, 0xb7, 0xed, 0x98, 0xc6, 0x30, 0x8a, 0x00,
	0xae, 0xa1, 0x31, 0xe5, 0xb5, 0x9e, 0x6d, 0x62,
	0xda, 0xda, 0x07, 0x0f, 0x38, 0x38, 0xd3, 0xcb,
	0xc1, 0xb0, 0xad, 0xec, 0x72, 0xec, 0xb1, 0xa2,
	0x7b, 0x59, 0xf3, 0x3d, 0x2b, 0xef, 0xcd, 0x28,
	0x5b, 0x83, 0xcc, 0x18, 0x91, 0x88, 0xb0, 0x2e,
	0xf9, 0x29, 0x31, 0x18, 0xf9, 0x4e, 0xe9, 0x0a,
	0x91, 0x92, 0x9f, 0xae, 0x2d, 0xad, 0xf4, 0xe6,
	0x1a, 0xe2, 0xa4, 0xee, 0x47, 0x15, 0xbf, 0x83,
	0x6e, 0xd7, 0x72, 0x12, 0x3b, 0x2d, 0x24, 0xe9,
	0xb2, 0x55, 0xcb, 0x3c, 0x10, 0xf0, 0x24, 0x8a,
	0x4a, 0x02, 0xea, 0x90, 0x25, 0xf0, 0xb4, 0x79,
	0x3a, 0xef, 0x6e, 0xf5, 0x52, 0xdf, 0xb0, 0x0a,
	0xcd, 0x24, 0x1c, 0xd3, 0x2e, 0x22, 0x74, 0xea,
	0x21, 0x6f, 0xe9, 0xbd, 0xc8, 0x3e, 0x36, 0x5b,
	0x19, 0xf1, 0xca, 0x99, 0x0a, 0xb4, 0xa7, 0x52,
	0x1a, 0x4e, 0xf2, 0xad, 0x8d, 0x56, 0x85, 0xbb,
	0x64, 0x89, 0xba, 0x26, 0xf9, 0xc7, 0xe1, 0x89,
	0x19, 0x22, 0x77, 0xc3, 0xa8, 0xfc, 0xff, 0xad,
	0xfe, 0xb9, 0x48, 0xae, 0x12, 0x30, 0x9f, 0x19,
	0xfb, 0x1b, 0xef, 0x14, 0x87, 0x8a, 0x78, 0x71,
	0xf3, 0xf4, 0xb7, 0x00, 0x9c, 0x1d, 0xb5, 0x3d,
	0x49, 0x00, 0x0c, 0x06, 0xd4, 0x50, 0xf9, 0x54,
	0x45, 0xb2, 0x5b, 0x43, 0xdb, 0x6d, 0xcf, 0x1a,
	0xe9, 0x7a, 0x7a, 0xcf, 0xfc, 0x8a, 0x4e, 0x4d,
	0x0b, 0x07, 0x63, 0x28, 0xd8, 0xe7, 0x08, 0x95,
	0xdf, 0xa6, 0x72, 0x93, 0x2e, 0xbb, 0xa0, 0x42,
	0x89, 0x16, 0xf1, 0xd9, 0x0c, 0xf9, 0xa1, 0x16,
	0xfd, 0xd9, 0x03, 0xb4, 0x3b, 0x8a, 0xf5, 0xf6,
	0xe7, 0x6b, 0x2e, 0x8e, 0x4c, 0x3d, 0xe2, 0xaf,
	0x08, 0x45, 0x03, 0xff, 0x09, 0xb6, 0xeb, 0x2d,
	0xc6, 0x1b, 0x88, 0x94, 0xac, 0x3e, 0xf1, 0x9f,
	0x0e, 0x0e, 0x2b, 0xd5, 0x00, 0x4d, 0x3f, 0x3b,
	0x53, 0xae, 0xaf, 0x1c, 0x33, 0x5f, 0x55, 0x6e,
	0x8d, 0xaf, 0x05, 0x7a, 0x10, 0x34, 0xc9, 0xf4,
	0x66, 0xcb, 0x62, 0x12, 0xa6, 0xee, 0xe8, 0x1c,
	0x5d, 0x12, 0x86, 0xdb, 0x6f, 0x1c, 0x33, 0xc4,
	0x1c, 0xda, 0x82, 0x2d, 0x3b, 0x59, 0xfe, 0xb1,
	0xa4, 0x59, 0x41, 0x86, 0xd0, 0xef, 0xae, 0xfb,
	0xda, 0x6d, 0x11, 0xb8, 0xca, 0xe9, 0x6e, 0xff,
	0xf7, 0xa9, 0xd9, 0x70, 0x30, 0xfc, 0x53, 0xe2,
	0xd7, 0xa2, 0x4e, 0xc7, 0x91, 0xd9, 0x07, 0x06,
	0xaa, 0xdd, 0xb0, 0x59, 0x28, 0x1d, 0x00, 0x66,
	0xc5, 0x54, 0xc2, 0xfc, 0x06, 0xda, 0x05, 0x90,
	0x52, 0x1d, 0x37, 0x66, 0xee, 0xf0, 0xb2, 0x55,
	0x8a, 0x5d, 0xd2, 0x38, 0x86, 0x94, 0x9b, 0xfc,
	0x10, 0x4c, 0xa1, 0xb9, 0x64, 0x3e, 0x44, 0xb8,
	0x5f, 0xb0, 0x0c, 0xec, 0xe0, 0xc9, 0xe5, 0x62,
	0x75, 0x3f, 0x09, 0xd5, 0xf5, 0xd9, 0x26, 0xba,
	0x9e, 0xd2, 0xf4, 0xb9, 0x48, 0x0a, 0xbc, 0xa2,
	0xd6, 0x7c, 0x36, 0x11, 0x7d, 0x26, 0x81, 0x89,
	0xcf, 0xa4, 0xad, 0x73, 0x0e, 0xee, 0xcc, 0x06,
	0xa9, 0xdb, 0xb1, 0xfd, 0xfb, 0x09, 0x7f, 0x90,
	0x42, 0x37, 0x2f, 0xe1, 0x9c, 0x0f, 0x6f, 0xcf,
	0x43, 0xb5, 0xd9, 0x90, 0xe1, 0x85, 0xf5, 0xa8,
	0xae
};
static const u8 dec_output009[] __initconst = {
	0xe6, 0xc3, 0xdb, 0x63, 0x55, 0x15, 0xe3, 0x5b,
	0xb7, 0x4b, 0x27, 0x8b, 0x5a, 0xdd, 0xc2, 0xe8,
	0x3a, 0x6b, 0xd7, 0x81, 0x96, 0x35, 0x97, 0xca,
	0xd7, 0x68, 0xe8, 0xef, 0xce, 0xab, 0xda, 0x09,
	0x6e, 0xd6, 0x8e, 0xcb, 0x55, 0xb5, 0xe1, 0xe5,
	0x57, 0xfd, 0xc4, 0xe3, 0xe0, 0x18, 0x4f, 0x85,
	0xf5, 0x3f, 0x7e, 0x4b, 0x88, 0xc9, 0x52, 0x44,
	0x0f, 0xea, 0xaf, 0x1f, 0x71, 0x48, 0x9f, 0x97,
	0x6d, 0xb9, 0x6f, 0x00, 0xa6, 0xde, 0x2b, 0x77,
	0x8b, 0x15, 0xad, 0x10, 0xa0, 0x2b, 0x7b, 0x41,
	0x90, 0x03, 0x2d, 0x69, 0xae, 0xcc, 0x77, 0x7c,
	0xa5, 0x9d, 0x29, 0x22, 0xc2, 0xea, 0xb4, 0x00,
	0x1a, 0xd2, 0x7a, 0x98, 0x8a, 0xf9, 0xf7, 0x82,
	0xb0, 0xab, 0xd8, 0xa6, 0x94, 0x8d, 0x58, 0x2f,
	0x01, 0x9e, 0x00, 0x20, 0xfc, 0x49, 0xdc, 0x0e,
	0x03, 0xe8, 0x45, 0x10, 0xd6, 0xa8, 0xda, 0x55,
	0x10, 0x9a, 0xdf, 0x67, 0x22, 0x8b, 0x43, 0xab,
	0x00, 0xbb, 0x02, 0xc8, 0xdd, 0x7b, 0x97, 0x17,
	0xd7, 0x1d, 0x9e, 0x02, 0x5e, 0x48, 0xde, 0x8e,
	0xcf, 0x99, 0x07, 0x95, 0x92, 0x3c, 0x5f, 0x9f,
	0xc5, 0x8a, 0xc0, 0x23, 0xaa, 0xd5, 0x8c, 0x82,
	0x6e, 0x16, 0x92, 0xb1, 0x12, 0x17, 0x07, 0xc3,
	0xfb, 0x36, 0xf5, 0x6c, 0x35, 0xd6, 0x06, 0x1f,
	0x9f, 0xa7, 0x94, 0xa2, 0x38, 0x63, 0x9c, 0xb0,
	0x71, 0xb3, 0xa5, 0xd2, 0xd8, 0xba, 0x9f, 0x08,
	0x01, 0xb3, 0xff, 0x04, 0x97, 0x73, 0x45, 0x1b,
	0xd5, 0xa9, 0x9c, 0x80, 0xaf, 0x04, 0x9a, 0x85,
	0xdb, 0x32, 0x5b, 0x5d, 0x1a, 0xc1, 0x36, 0x28,
	0x10, 0x79, 0xf1, 0x3c, 0xbf, 0x1a, 0x41, 0x5c,
	0x4e, 0xdf, 0xb2, 0x7c, 0x79, 0x3b, 0x7a, 0x62,
	0x3d, 0x4b, 0xc9, 0x9b, 0x2a, 0x2e, 0x7c, 0xa2,
	0xb1, 0x11, 0x98, 0xa7, 0x34, 0x1a, 0x00, 0xf3,
	0xd1, 0xbc, 0x18, 0x22, 0xba, 0x02, 0x56, 0x62,
	0x31, 0x10, 0x11, 0x6d, 0xe0, 0x54, 0x9d, 0x40,
	0x1f, 0x26, 0x80, 0x41, 0xca, 0x3f, 0x68, 0x0f,
	0x32, 0x1d, 0x0a, 0x8e, 0x79, 0xd8, 0xa4, 0x1b,
	0x29, 0x1c, 0x90, 0x8e, 0xc5, 0xe3, 0xb4, 0x91,
	0x37, 0x9a, 0x97, 0x86, 0x99, 0xd5, 0x09, 0xc5,
	0xbb, 0xa3, 0x3f, 0x21, 0x29, 0x82, 0x14, 0x5c,
	0xab, 0x25, 0xfb, 0xf2, 0x4f, 0x58, 0x26, 0xd4,
	0x83, 0xaa, 0x66, 0x89, 0x67, 0x7e, 0xc0, 0x49,
	0xe1, 0x11, 0x10, 0x7f, 0x7a, 0xda, 0x29, 0x04,
	0xff, 0xf0, 0xcb, 0x09, 0x7c, 0x9d, 0xfa, 0x03,
	0x6f, 0x81, 0x09, 0x31, 0x60, 0xfb, 0x08, 0xfa,
	0x74, 0xd3, 0x64, 0x44, 0x7c, 0x55, 0x85, 0xec,
	0x9c, 0x6e, 0x25, 0xb7, 0x6c, 0xc5, 0x37, 0xb6,
	0x83, 0x87, 0x72, 0x95, 0x8b, 0x9d, 0xe1, 0x69,
	0x5c, 0x31, 0x95, 0x42, 0xa6, 0x2c, 0xd1, 0x36,
	0x47, 0x1f, 0xec, 0x54, 0xab, 0xa2, 0x1c, 0xd8,
	0x00, 0xcc, 0xbc, 0x0d, 0x65, 0xe2, 0x67, 0xbf,
	0xbc, 0xea, 0xee, 0x9e, 0xe4, 0x36, 0x95, 0xbe,
	0x73, 0xd9, 0xa6, 0xd9, 0x0f, 0xa0, 0xcc, 0x82,
	0x76, 0x26, 0xad, 0x5b, 0x58, 0x6c, 0x4e, 0xab,
	0x29, 0x64, 0xd3, 0xd9, 0xa9, 0x08, 0x8c, 0x1d,
	0xa1, 0x4f, 0x80, 0xd8, 0x3f, 0x94, 0xfb, 0xd3,
	0x7b, 0xfc, 0xd1, 0x2b, 0xc3, 0x21, 0xeb, 0xe5,
	0x1c, 0x84, 0x23, 0x7f, 0x4b, 0xfa, 0xdb, 0x34,
	0x18, 0xa2, 0xc2, 0xe5, 0x13, 0xfe, 0x6c, 0x49,
	0x81, 0xd2, 0x73, 0xe7, 0xe2, 0xd7, 0xe4, 0x4f,
	0x4b, 0x08, 0x6e, 0xb1, 0x12, 0x22, 0x10, 0x9d,
	0xac, 0x51, 0x1e, 0x17, 0xd9, 0x8a, 0x0b, 0x42,
	0x88, 0x16, 0x81, 0x37, 0x7c, 0x6a, 0xf7, 0xef,
	0x2d, 0xe3, 0xd9, 0xf8, 0x5f, 0xe0, 0x53, 0x27,
	0x74, 0xb9, 0xe2, 0xd6, 0x1c, 0x80, 0x2c, 0x52,
	0x65
};
static const u8 dec_assoc009[] __initconst = {
	0x5a, 0x27, 0xff, 0xeb, 0xdf, 0x84, 0xb2, 0x9e,
	0xef
};
static const u8 dec_nonce009[] __initconst = {
	0xef, 0x2d, 0x63, 0xee, 0x6b, 0x80, 0x8b, 0x78
};
static const u8 dec_key009[] __initconst = {
	0xea, 0xbc, 0x56, 0x99, 0xe3, 0x50, 0xff, 0xc5,
	0xcc, 0x1a, 0xd7, 0xc1, 0x57, 0x72, 0xea, 0x86,
	0x5b, 0x89, 0x88, 0x61, 0x3d, 0x2f, 0x9b, 0xb2,
	0xe7, 0x9c, 0xec, 0x74, 0x6e, 0x3e, 0xf4, 0x3b
};

static const u8 dec_input010[] __initconst = {
	0xe5, 0x26, 0xa4, 0x3d, 0xbd, 0x33, 0xd0, 0x4b,
	0x6f, 0x05, 0xa7, 0x6e, 0x12, 0x7a, 0xd2, 0x74,
	0xa6, 0xdd, 0xbd, 0x95, 0xeb, 0xf9, 0xa4, 0xf1,
	0x59, 0x93, 0x91, 0x70, 0xd9, 0xfe, 0x9a, 0xcd,
	0x53, 0x1f, 0x3a, 0xab, 0xa6, 0x7c, 0x9f, 0xa6,
	0x9e, 0xbd, 0x99, 0xd9, 0xb5, 0x97, 0x44, 0xd5,
	0x14, 0x48, 0x4d, 0x9d, 0xc0, 0xd0, 0x05, 0x96,
	0xeb, 0x4c, 0x78, 0x55, 0x09, 0x08, 0x01, 0x02,
	0x30, 0x90, 0x7b, 0x96, 0x7a, 0x7b, 0x5f, 0x30,
	0x41, 0x24, 0xce, 0x68, 0x61, 0x49, 0x86, 0x57,
	0x82, 0xdd, 0x53, 0x1c, 0x51, 0x28, 0x2b, 0x53,
	0x6e, 0x2d, 0xc2, 0x20, 0x4c, 0xdd, 0x8f, 0x65,
	0x10, 0x20, 0x50, 0xdd, 0x9d, 0x50, 0xe5, 0x71,
	0x40, 0x53, 0x69, 0xfc, 0x77, 0x48, 0x11, 0xb9,
	0xde, 0xa4, 0x8d, 0x58, 0xe4, 0xa6, 0x1a, 0x18,
	0x47, 0x81, 0x7e, 0xfc, 0xdd, 0xf6, 0xef, 0xce,
	0x2f, 0x43, 0x68, 0xd6, 0x06, 0xe2, 0x74, 0x6a,
	0xad, 0x90, 0xf5, 0x37, 0xf3, 0x3d, 0x82, 0x69,
	0x40, 0xe9, 0x6b, 0xa7, 0x3d, 0xa8, 0x1e, 0xd2,
	0x02, 0x7c, 0xb7, 0x9b, 0xe4, 0xda, 0x8f, 0x95,
	0x06, 0xc5, 0xdf, 0x73, 0xa3, 0x20, 0x9a, 0x49,
	0xde, 0x9c, 0xbc, 0xee, 0x14, 0x3f, 0x81, 0x5e,
	0xf8, 0x3b, 0x59, 0x3c, 0xe1, 0x68, 0x12, 0x5a,
	0x3a, 0x76, 0x3a, 0x3f, 0xf7, 0x87, 0x33, 0x0a,
	0x01, 0xb8, 0xd4, 0xed, 0xb6, 0xbe, 0x94, 0x5e,
	0x70, 0x40, 0x56, 0x67, 0x1f, 0x50, 0x44, 0x19,
	0xce, 0x82, 0x70, 0x10, 0x87, 0x13, 0x20, 0x0b,
	0x4c, 0x5a, 0xb6, 0xf6, 0xa7, 0xae, 0x81, 0x75,
	0x01, 0x81, 0xe6, 0x4b, 0x57, 0x7c, 0xdd, 0x6d,
	0xf8, 0x1c, 0x29, 0x32, 0xf7, 0xda, 0x3c, 0x2d,
	0xf8, 0x9b, 0x25, 0x6e, 0x00, 0xb4, 0xf7, 0x2f,
	0xf7, 0x04, 0xf7, 0xa1, 0x56, 0xac, 0x4f, 0x1a,
	0x64, 0xb8, 0x47, 0x55, 0x18, 0x7b, 0x07, 0x4d,
	0xbd, 0x47, 0x24, 0x80, 0x5d, 0xa2, 0x70, 0xc5,
	0xdd, 0x8e, 0x82, 0xd4, 0xeb, 0xec, 0xb2, 0x0c,
	0x39, 0xd2, 0x97, 0xc1, 0xcb, 0xeb, 0xf4, 0x77,
	0x59, 0xb4, 0x87, 0xef, 0xcb, 0x43, 0x2d, 0x46,
	0x54, 0xd1, 0xa7, 0xd7, 0x15, 0x99, 0x0a, 0x43,
	0xa1, 0xe0, 0x99, 0x33, 0x71, 0xc1, 0xed, 0xfe,
	0x72, 0x46, 0x33, 0x8e, 0x91, 0x08, 0x9f, 0xc8,
	0x2e, 0xca, 0xfa, 0xdc, 0x59, 0xd5, 0xc3, 0x76,
	0x84, 0x9f, 0xa3, 0x37, 0x68, 0xc3, 0xf0, 0x47,
	0x2c, 0x68, 0xdb, 0x5e, 0xc3, 0x49, 0x4c, 0xe8,
	0x92, 0x85, 0xe2, 0x23, 0xd3, 0x3f, 0xad, 0x32,
	0xe5, 0x2b, 0x82, 0xd7, 0x8f, 0x99, 0x0a, 0x59,
	0x5c, 0x45, 0xd9, 0xb4, 0x51, 0x52, 0xc2, 0xae,
	0xbf, 0x80, 0xcf, 0xc9, 0xc9, 0x51, 0x24, 0x2a,
	0x3b, 0x3a, 0x4d, 0xae, 0xeb, 0xbd, 0x22, 0xc3,
	0x0e, 0x0f, 0x59, 0x25, 0x92, 0x17, 0xe9, 0x74,
	0xc7, 0x8b, 0x70, 0x70, 0x36, 0x55, 0x95, 0x75,
	0x4b, 0xad, 0x61, 0x2b, 0x09, 0xbc, 0x82, 0xf2,
	0x6e, 0x94, 0x43, 0xae, 0xc3, 0xd5, 0xcd, 0x8e,
	0xfe, 0x5b, 0x9a, 0x88, 0x43, 0x01, 0x75, 0xb2,
	0x23, 0x09, 0xf7, 0x89, 0x83, 0xe7, 0xfa, 0xf9,
	0xb4, 0x9b, 0xf8, 0xef, 0xbd, 0x1c, 0x92, 0xc1,
	0xda, 0x7e, 0xfe, 0x05, 0xba, 0x5a, 0xcd, 0x07,
	0x6a, 0x78, 0x9e, 0x5d, 0xfb, 0x11, 0x2f, 0x79,
	0x38, 0xb6, 0xc2, 0x5b, 0x6b, 0x51, 0xb4, 0x71,
	0xdd, 0xf7, 0x2a, 0xe4, 0xf4, 0x72, 0x76, 0xad,
	0xc2, 0xdd, 0x64, 0x5d, 0x79, 0xb6, 0xf5, 0x7a,
	0x77, 0x20, 0x05, 0x3d, 0x30, 0x06, 0xd4, 0x4c,
	0x0a, 0x2c, 0x98, 0x5a, 0xb9, 0xd4, 0x98, 0xa9,
	0x3f, 0xc6, 0x12, 0xea, 0x3b, 0x4b, 0xc5, 0x79,
	0x64, 0x63, 0x6b, 0x09, 0x54, 0x3b, 0x14, 0x27,
	0xba, 0x99, 0x80, 0xc8, 0x72, 0xa8, 0x12, 0x90,
	0x29, 0xba, 0x40, 0x54, 0x97, 0x2b, 0x7b, 0xfe,
	0xeb, 0xcd, 0x01, 0x05, 0x44, 0x72, 0xdb, 0x99,
	0xe4, 0x61, 0xc9, 0x69, 0xd6, 0xb9, 0x28, 0xd1,
	0x05, 0x3e, 0xf9, 0x0b, 0x49, 0x0a, 0x49, 0xe9,
	0x8d, 0x0e, 0xa7, 0x4a, 0x0f, 0xaf, 0x32, 0xd0,
	0xe0, 0xb2, 0x3a, 0x55, 0x58, 0xfe, 0x5c, 0x28,
	0x70, 0x51, 0x23, 0xb0, 0x7b, 0x6a, 0x5f, 0x1e,
	0xb8, 0x17, 0xd7, 0x94, 0x15, 0x8f, 0xee, 0x20,
	0xc7, 0x42, 0x25, 0x3e, 0x9a, 0x14, 0xd7, 0x60,
	0x72, 0x39, 0x47, 0x48, 0xa9, 0xfe, 0xdd, 0x47,
	0x0a, 0xb1, 0xe6, 0x60, 0x28, 0x8c, 0x11, 0x68,
	0xe1, 0xff, 0xd7, 0xce, 0xc8, 0xbe, 0xb3, 0xfe,
	0x27, 0x30, 0x09, 0x70, 0xd7, 0xfa, 0x02, 0x33,
	0x3a, 0x61, 0x2e, 0xc7, 0xff, 0xa4, 0x2a, 0xa8,
	0x6e, 0xb4, 0x79, 0x35, 0x6d, 0x4c, 0x1e, 0x38,
	0xf8, 0xee, 0xd4, 0x84, 0x4e, 0x6e, 0x28, 0xa7,
	0xce, 0xc8, 0xc1, 0xcf, 0x80, 0x05, 0xf3, 0x04,
	0xef, 0xc8, 0x18, 0x28, 0x2e, 0x8d, 0x5e, 0x0c,
	0xdf, 0xb8, 0x5f, 0x96, 0xe8, 0xc6, 0x9c, 0x2f,
	0xe5, 0xa6, 0x44, 0xd7, 0xe7, 0x99, 0x44, 0x0c,
	0xec, 0xd7, 0x05, 0x60, 0x97, 0xbb, 0x74, 0x77,
	0x58, 0xd5, 0xbb, 0x48, 0xde, 0x5a, 0xb2, 0x54,
	0x7f, 0x0e, 0x46, 0x70, 0x6a, 0x6f, 0x78, 0xa5,
	0x08, 0x89, 0x05, 0x4e, 0x7e, 0xa0, 0x69, 0xb4,
	0x40, 0x60, 0x55, 0x77, 0x75, 0x9b, 0x19, 0xf2,
	0xd5, 0x13, 0x80, 0x77, 0xf9, 0x4b, 0x3f, 0x1e,
	0xee, 0xe6, 0x76, 0x84, 0x7b, 0x8c, 0xe5, 0x27,
	0xa8, 0x0a, 0x91, 0x01, 0x68, 0x71, 0x8a, 0x3f,
	0x06, 0xab, 0xf6, 0xa9, 0xa5, 0xe6, 0x72, 0x92,
	0xe4, 0x67, 0xe2, 0xa2, 0x46, 0x35, 0x84, 0x55,
	0x7d, 0xca, 0xa8, 0x85, 0xd0, 0xf1, 0x3f, 0xbe,
	0xd7, 0x34, 0x64, 0xfc, 0xae, 0xe3, 0xe4, 0x04,
	0x9f, 0x66, 0x02, 0xb9, 0x88, 0x10, 0xd9, 0xc4,
	0x4c, 0x31, 0x43, 0x7a, 0x93, 0xe2, 0x9b, 0x56,
	0x43, 0x84, 0xdc, 0xdc, 0xde, 0x1d, 0xa4, 0x02,
	0x0e, 0xc2, 0xef, 0xc3, 0xf8, 0x78, 0xd1, 0xb2,
	0x6b, 0x63, 0x18, 0xc9, 0xa9, 0xe5, 0x72, 0xd8,
	0xf3, 0xb9, 0xd1, 0x8a, 0xc7, 0x1a, 0x02, 0x27,
	0x20, 0x77, 0x10, 0xe5, 0xc8, 0xd4, 0x4a, 0x47,
	0xe5, 0xdf, 0x5f, 0x01, 0xaa, 0xb0, 0xd4, 0x10,
	0xbb, 0x69, 0xe3, 0x36, 0xc8, 0xe1, 0x3d, 0x43,
	0xfb, 0x86, 0xcd, 0xcc, 0xbf, 0xf4, 0x88, 0xe0,
	0x20, 0xca, 0xb7, 0x1b, 0xf1, 0x2f, 0x5c, 0xee,
	0xd4, 0xd3, 0xa3, 0xcc, 0xa4, 0x1e, 0x1c, 0x47,
	0xfb, 0xbf, 0xfc, 0xa2, 0x41, 0x55, 0x9d, 0xf6,
	0x5a, 0x5e, 0x65, 0x32, 0x34, 0x7b, 0x52, 0x8d,
	0xd5, 0xd0, 0x20, 0x60, 0x03, 0xab, 0x3f, 0x8c,
	0xd4, 0x21, 0xea, 0x2a, 0xd9, 0xc4, 0xd0, 0xd3,
	0x65, 0xd8, 0x7a, 0x13, 0x28, 0x62, 0x32, 0x4b,
	0x2c, 0x87, 0x93, 0xa8, 0xb4, 0x52, 0x45, 0x09,
	0x44, 0xec, 0xec, 0xc3, 0x17, 0xdb, 0x9a, 0x4d,
	0x5c, 0xa9, 0x11, 0xd4, 0x7d, 0xaf, 0x9e, 0xf1,
	0x2d, 0xb2, 0x66, 0xc5, 0x1d, 0xed, 0xb7, 0xcd,
	0x0b, 0x25, 0x5e, 0x30, 0x47, 0x3f, 0x40, 0xf4,
	0xa1, 0xa0, 0x00, 0x94, 0x10, 0xc5, 0x6a, 0x63,
	0x1a, 0xd5, 0x88, 0x92, 0x8e, 0x82, 0x39, 0x87,
	0x3c, 0x78, 0x65, 0x58, 0x42, 0x75, 0x5b, 0xdd,
	0x77, 0x3e, 0x09, 0x4e, 0x76, 0x5b, 0xe6, 0x0e,
	0x4d, 0x38, 0xb2, 0xc0, 0xb8, 0x95, 0x01, 0x7a,
	0x10, 0xe0, 0xfb, 0x07, 0xf2, 0xab, 0x2d, 0x8c,
	0x32, 0xed, 0x2b, 0xc0, 0x46, 0xc2, 0xf5, 0x38,
	0x83, 0xf0, 0x17, 0xec, 0xc1, 0x20, 0x6a, 0x9a,
	0x0b, 0x00, 0xa0, 0x98, 0x22, 0x50, 0x23, 0xd5,
	0x80, 0x6b, 0xf6, 0x1f, 0xc3, 0xcc, 0x97, 0xc9,
	0x24, 0x9f, 0xf3, 0xaf, 0x43, 0x14, 0xd5, 0xa0
};
static const u8 dec_output010[] __initconst = {
	0x42, 0x93, 0xe4, 0xeb, 0x97, 0xb0, 0x57, 0xbf,
	0x1a, 0x8b, 0x1f, 0xe4, 0x5f, 0x36, 0x20, 0x3c,
	0xef, 0x0a, 0xa9, 0x48, 0x5f, 0x5f, 0x37, 0x22,
	0x3a, 0xde, 0xe3, 0xae, 0xbe, 0xad, 0x07, 0xcc,
	0xb1, 0xf6, 0xf5, 0xf9, 0x56, 0xdd, 0xe7, 0x16,
	0x1e, 0x7f, 0xdf, 0x7a, 0x9e, 0x75, 0xb7, 0xc7,
	0xbe, 0xbe, 0x8a, 0x36, 0x04, 0xc0, 0x10, 0xf4,
	0x95, 0x20, 0x03, 0xec, 0xdc, 0x05, 0xa1, 0x7d,
	0xc4, 0xa9, 0x2c, 0x82, 0xd0, 0xbc, 0x8b, 0xc5,
	0xc7, 0x45, 0x50, 0xf6, 0xa2, 0x1a, 0xb5, 0x46,
	0x3b, 0x73, 0x02, 0xa6, 0x83, 0x4b, 0x73, 0x82,
	0x58, 0x5e, 0x3b, 0x65, 0x2f, 0x0e, 0xfd, 0x2b,
	0x59, 0x16, 0xce, 0xa1, 0x60, 0x9c, 0xe8, 0x3a,
	0x99, 0xed, 0x8d, 0x5a, 0xcf, 0xf6, 0x83, 0xaf,
	0xba, 0xd7, 0x73, 0x73, 0x40, 0x97, 0x3d, 0xca,
	0xef, 0x07, 0x57, 0xe6, 0xd9, 0x70, 0x0e, 0x95,
	0xae, 0xa6, 0x8d, 0x04, 0xcc, 0xee, 0xf7, 0x09,
	0x31, 0x77, 0x12, 0xa3, 0x23, 0x97, 0x62, 0xb3,
	0x7b, 0x32, 0xfb, 0x80, 0x14, 0x48, 0x81, 0xc3,
	0xe5, 0xea, 0x91, 0x39, 0x52, 0x81, 0xa2, 0x4f,
	0xe4, 0xb3, 0x09, 0xff, 0xde, 0x5e, 0xe9, 0x58,
	0x84, 0x6e, 0xf9, 0x3d, 0xdf, 0x25, 0xea, 0xad,
	0xae, 0xe6, 0x9a, 0xd1, 0x89, 0x55, 0xd3, 0xde,
	0x6c, 0x52, 0xdb, 0x70, 0xfe, 0x37, 0xce, 0x44,
	0x0a, 0xa8, 0x25, 0x5f, 0x92, 0xc1, 0x33, 0x4a,
	0x4f, 0x9b, 0x62, 0x35, 0xff, 0xce, 0xc0, 0xa9,
	0x60, 0xce, 0x52, 0x00, 0x97, 0x51, 0x35, 0x26,
	0x2e, 0xb9, 0x36, 0xa9, 0x87, 0x6e, 0x1e, 0xcc,
	0x91, 0x78, 0x53, 0x98, 0x86, 0x5b, 0x9c, 0x74,
	0x7d, 0x88, 0x33, 0xe1, 0xdf, 0x37, 0x69, 0x2b,
	0xbb, 0xf1, 0x4d, 0xf4, 0xd1, 0xf1, 0x39, 0x93,
	0x17, 0x51, 0x19, 0xe3, 0x19, 0x1e, 0x76, 0x37,
	0x25, 0xfb, 0x09, 0x27, 0x6a, 0xab, 0x67, 0x6f,
	0x14, 0x12, 0x64, 0xe7, 0xc4, 0x07, 0xdf, 0x4d,
	0x17, 0xbb, 0x6d, 0xe0, 0xe9, 0xb9, 0xab, 0xca,
	0x10, 0x68, 0xaf, 0x7e, 0xb7, 0x33, 0x54, 0x73,
	0x07, 0x6e, 0xf7, 0x81, 0x97, 0x9c, 0x05, 0x6f,
	0x84, 0x5f, 0xd2, 0x42, 0xfb, 0x38, 0xcf, 0xd1,
	0x2f, 0x14, 0x30, 0x88, 0x98, 0x4d, 0x5a, 0xa9,
	0x76, 0xd5, 0x4f, 0x3e, 0x70, 0x6c, 0x85, 0x76,
	0xd7, 0x01, 0xa0, 0x1a, 0xc8, 0x4e, 0xaa, 0xac,
	0x78, 0xfe, 0x46, 0xde, 0x6a, 0x05, 0x46, 0xa7,
	0x43, 0x0c, 0xb9, 0xde, 0xb9, 0x68, 0xfb, 0xce,
	0x42, 0x99, 0x07, 0x4d, 0x0b, 0x3b, 0x5a, 0x30,
	0x35, 0xa8, 0xf9, 0x3a, 0x73, 0xef, 0x0f, 0xdb,
	0x1e, 0x16, 0x42, 0xc4, 0xba, 0xae, 0x58, 0xaa,
	0xf8, 0xe5, 0x75, 0x2f, 0x1b, 0x15, 0x5c, 0xfd,
	0x0a, 0x97, 0xd0, 0xe4, 0x37, 0x83, 0x61, 0x5f,
	0x43, 0xa6, 0xc7, 0x3f, 0x38, 0x59, 0xe6, 0xeb,
	0xa3, 0x90, 0xc3, 0xaa, 0xaa, 0x5a, 0xd3, 0x34,
	0xd4, 0x17, 0xc8, 0x65, 0x3e, 0x57, 0xbc, 0x5e,
	0xdd, 0x9e, 0xb7, 0xf0, 0x2e, 0x5b, 0xb2, 0x1f,
	0x8a, 0x08, 0x0d, 0x45, 0x91, 0x0b, 0x29, 0x53,
	0x4f, 0x4c, 0x5a, 0x73, 0x56, 0xfe, 0xaf, 0x41,
	0x01, 0x39, 0x0a, 0x24, 0x3c, 0x7e, 0xbe, 0x4e,
	0x53, 0xf3, 0xeb, 0x06, 0x66, 0x51, 0x28, 0x1d,
	0xbd, 0x41, 0x0a, 0x01, 0xab, 0x16, 0x47, 0x27,
	0x47, 0x47, 0xf7, 0xcb, 0x46, 0x0a, 0x70, 0x9e,
	0x01, 0x9c, 0x09, 0xe1, 0x2a, 0x00, 0x1a, 0xd8,
	0xd4, 0x79, 0x9d, 0x80, 0x15, 0x8e, 0x53, 0x2a,
	0x65, 0x83, 0x78, 0x3e, 0x03, 0x00, 0x07, 0x12,
	0x1f, 0x33, 0x3e, 0x7b, 0x13, 0x37, 0xf1, 0xc3,
	0xef, 0xb7, 0xc1, 0x20, 0x3c, 0x3e, 0x67, 0x66,
	0x5d, 0x88, 0xa7, 0x7d, 0x33, 0x50, 0x77, 0xb0,
	0x28, 0x8e, 0xe7, 0x2c, 0x2e, 0x7a, 0xf4, 0x3c,
	0x8d, 0x74, 0x83, 0xaf, 0x8e, 0x87, 0x0f, 0xe4,
	0x50, 0xff, 0x84, 0x5c, 0x47, 0x0c, 0x6a, 0x49,
	0xbf, 0x42, 0x86, 0x77, 0x15, 0x48, 0xa5, 0x90,
	0x5d, 0x93, 0xd6, 0x2a, 0x11, 0xd5, 0xd5, 0x11,
	0xaa, 0xce, 0xe7, 0x6f, 0xa5, 0xb0, 0x09, 0x2c,
	0x8d, 0xd3, 0x92, 0xf0, 0x5a, 0x2a, 0xda, 0x5b,
	0x1e, 0xd5, 0x9a, 0xc4, 0xc4, 0xf3, 0x49, 0x74,
	0x41, 0xca, 0xe8, 0xc1, 0xf8, 0x44, 0xd6, 0x3c,
	0xae, 0x6c, 0x1d, 0x9a, 0x30, 0x04, 0x4d, 0x27,
	0x0e, 0xb1, 0x5f, 0x59, 0xa2, 0x24, 0xe8, 0xe1,
	0x98, 0xc5, 0x6a, 0x4c, 0xfe, 0x41, 0xd2, 0x27,
	0x42, 0x52, 0xe1, 0xe9, 0x7d, 0x62, 0xe4, 0x88,
	0x0f, 0xad, 0xb2, 0x70, 0xcb, 0x9d, 0x4c, 0x27,
	0x2e, 0x76, 0x1e, 0x1a, 0x63, 0x65, 0xf5, 0x3b,
	0xf8, 0x57, 0x69, 0xeb, 0x5b, 0x38, 0x26, 0x39,
	0x33, 0x25, 0x45, 0x3e, 0x91, 0xb8, 0xd8, 0xc7,
	0xd5, 0x42, 0xc0, 0x22, 0x31, 0x74, 0xf4, 0xbc,
	0x0c, 0x23, 0xf1, 0xca, 0xc1, 0x8d, 0xd7, 0xbe,
	0xc9, 0x62, 0xe4, 0x08, 0x1a, 0xcf, 0x36, 0xd5,
	0xfe, 0x55, 0x21, 0x59, 0x91, 0x87, 0x87, 0xdf,
	0x06, 0xdb, 0xdf, 0x96, 0x45, 0x58, 0xda, 0x05,
	0xcd, 0x50, 0x4d, 0xd2, 0x7d, 0x05, 0x18, 0x73,
	0x6a, 0x8d, 0x11, 0x85, 0xa6, 0x88, 0xe8, 0xda,
	0xe6, 0x30, 0x33, 0xa4, 0x89, 0x31, 0x75, 0xbe,
	0x69, 0x43, 0x84, 0x43, 0x50, 0x87, 0xdd, 0x71,
	0x36, 0x83, 0xc3, 0x78, 0x74, 0x24, 0x0a, 0xed,
	0x7b, 0xdb, 0xa4, 0x24, 0x0b, 0xb9, 0x7e, 0x5d,
	0xff, 0xde, 0xb1, 0xef, 0x61, 0x5a, 0x45, 0x33,
	0xf6, 0x17, 0x07, 0x08, 0x98, 0x83, 0x92, 0x0f,
	0x23, 0x6d, 0xe6, 0xaa, 0x17, 0x54, 0xad, 0x6a,
	0xc8, 0xdb, 0x26, 0xbe, 0xb8, 0xb6, 0x08, 0xfa,
	0x68, 0xf1, 0xd7, 0x79, 0x6f, 0x18, 0xb4, 0x9e,
	0x2d, 0x3f, 0x1b, 0x64, 0xaf, 0x8d, 0x06, 0x0e,
	0x49, 0x28, 0xe0, 0x5d, 0x45, 0x68, 0x13, 0x87,
	0xfa, 0xde, 0x40, 0x7b, 0xd2, 0xc3, 0x94, 0xd5,
	0xe1, 0xd9, 0xc2, 0xaf, 0x55, 0x89, 0xeb, 0xb4,
	0x12, 0x59, 0xa8, 0xd4, 0xc5, 0x29, 0x66, 0x38,
	0xe6, 0xac, 0x22, 0x22, 0xd9, 0x64, 0x9b, 0x34,
	0x0a, 0x32, 0x9f, 0xc2, 0xbf, 0x17, 0x6c, 0x3f,
	0x71, 0x7a, 0x38, 0x6b, 0x98, 0xfb, 0x49, 0x36,
	0x89, 0xc9, 0xe2, 0xd6, 0xc7, 0x5d, 0xd0, 0x69,
	0x5f, 0x23, 0x35, 0xc9, 0x30, 0xe2, 0xfd, 0x44,
	0x58, 0x39, 0xd7, 0x97, 0xfb, 0x5c, 0x00, 0xd5,
	0x4f, 0x7a, 0x1a, 0x95, 0x8b, 0x62, 0x4b, 0xce,
	0xe5, 0x91, 0x21, 0x7b, 0x30, 0x00, 0xd6, 0xdd,
	0x6d, 0x02, 0x86, 0x49, 0x0f, 0x3c, 0x1a, 0x27,
	0x3c, 0xd3, 0x0e, 0x71, 0xf2, 0xff, 0xf5, 0x2f,
	0x87, 0xac, 0x67, 0x59, 0x81, 0xa3, 0xf7, 0xf8,
	0xd6, 0x11, 0x0c, 0x84, 0xa9, 0x03, 0xee, 0x2a,
	0xc4, 0xf3, 0x22, 0xab, 0x7c, 0xe2, 0x25, 0xf5,
	0x67, 0xa3, 0xe4, 0x11, 0xe0, 0x59, 0xb3, 0xca,
	0x87, 0xa0, 0xae, 0xc9, 0xa6, 0x62, 0x1b, 0x6e,
	0x4d, 0x02, 0x6b, 0x07, 0x9d, 0xfd, 0xd0, 0x92,
	0x06, 0xe1, 0xb2, 0x9a, 0x4a, 0x1f, 0x1f, 0x13,
	0x49, 0x99, 0x97, 0x08, 0xde, 0x7f, 0x98, 0xaf,
	0x51, 0x98, 0xee, 0x2c, 0xcb, 0xf0, 0x0b, 0xc6,
	0xb6, 0xb7, 0x2d, 0x9a, 0xb1, 0xac, 0xa6, 0xe3,
	0x15, 0x77, 0x9d, 0x6b, 0x1a, 0xe4, 0xfc, 0x8b,
	0xf2, 0x17, 0x59, 0x08, 0x04, 0x58, 0x81, 0x9d,
	0x1b, 0x1b, 0x69, 0x55, 0xc2, 0xb4, 0x3c, 0x1f,
	0x50, 0xf1, 0x7f, 0x77, 0x90, 0x4c, 0x66, 0x40,
	0x5a, 0xc0, 0x33, 0x1f, 0xcb, 0x05, 0x6d, 0x5c,
	0x06, 0x87, 0x52, 0xa2, 0x8f, 0x26, 0xd5, 0x4f
};
static const u8 dec_assoc010[] __initconst = {
	0xd2, 0xa1, 0x70, 0xdb, 0x7a, 0xf8, 0xfa, 0x27,
	0xba, 0x73, 0x0f, 0xbf, 0x3d, 0x1e, 0x82, 0xb2
};
static const u8 dec_nonce010[] __initconst = {
	0xdb, 0x92, 0x0f, 0x7f, 0x17, 0x54, 0x0c, 0x30
};
static const u8 dec_key010[] __initconst = {
	0x47, 0x11, 0xeb, 0x86, 0x2b, 0x2c, 0xab, 0x44,
	0x34, 0xda, 0x7f, 0x57, 0x03, 0x39, 0x0c, 0xaf,
	0x2c, 0x14, 0xfd, 0x65, 0x23, 0xe9, 0x8e, 0x74,
	0xd5, 0x08, 0x68, 0x08, 0xe7, 0xb4, 0x72, 0xd7
};

static const u8 dec_input011[] __initconst = {
	0x6a, 0xfc, 0x4b, 0x25, 0xdf, 0xc0, 0xe4, 0xe8,
	0x17, 0x4d, 0x4c, 0xc9, 0x7e, 0xde, 0x3a, 0xcc,
	0x3c, 0xba, 0x6a, 0x77, 0x47, 0xdb, 0xe3, 0x74,
	0x7a, 0x4d, 0x5f, 0x8d, 0x37, 0x55, 0x80, 0x73,
	0x90, 0x66, 0x5d, 0x3a, 0x7d, 0x5d, 0x86, 0x5e,
	0x8d, 0xfd, 0x83, 0xff, 0x4e, 0x74, 0x6f, 0xf9,
	0xe6, 0x70, 0x17, 0x70, 0x3e, 0x96, 0xa7, 0x7e,
	0xcb, 0xab, 0x8f, 0x58, 0x24, 0x9b, 0x01, 0xfd,
	0xcb, 0xe6, 0x4d, 0x9b, 0xf0, 0x88, 0x94, 0x57,
	0x66, 0xef, 0x72, 0x4c, 0x42, 0x6e, 0x16, 0x19,
	0x15, 0xea, 0x70, 0x5b, 0xac, 0x13, 0xdb, 0x9f,
	0x18, 0xe2, 0x3c, 0x26, 0x97, 0xbc, 0xdc, 0x45,
	0x8c, 0x6c, 0x24, 0x69, 0x9c, 0xf7, 0x65, 0x1e,
	0x18, 0x59, 0x31, 0x7c, 0xe4, 0x73, 0xbc, 0x39,
	0x62, 0xc6, 0x5c, 0x9f, 0xbf, 0xfa, 0x90, 0x03,
	0xc9, 0x72, 0x26, 0xb6, 0x1b, 0xc2, 0xb7, 0x3f,
	0xf2, 0x13, 0x77, 0xf2, 0x8d, 0xb9, 0x47, 0xd0,
	0x53, 0xdd, 0xc8, 0x91, 0x83, 0x8b, 0xb1, 0xce,
	0xa3, 0xfe, 0xcd, 0xd9, 0xdd, 0x92, 0x7b, 0xdb,
	0xb8, 0xfb, 0xc9, 0x2d, 0x01, 0x59, 0x39, 0x52,
	0xad, 0x1b, 0xec, 0xcf, 0xd7, 0x70, 0x13, 0x21,
	0xf5, 0x47, 0xaa, 0x18, 0x21, 0x5c, 0xc9, 0x9a,
	0xd2, 0x6b, 0x05, 0x9c, 0x01, 0xa1, 0xda, 0x35,
	0x5d, 0xb3, 0x70, 0xe6, 0xa9, 0x80, 0x8b, 0x91,
	0xb7, 0xb3, 0x5f, 0x24, 0x9a, 0xb7, 0xd1, 0x6b,
	0xa1, 0x1c, 0x50, 0xba, 0x49, 0xe0, 0xee, 0x2e,
	0x75, 0xac, 0x69, 0xc0, 0xeb, 0x03, 0xdd, 0x19,
	0xe5, 0xf6, 0x06, 0xdd, 0xc3, 0xd7, 0x2b, 0x07,
	0x07, 0x30, 0xa7, 0x19, 0x0c, 0xbf, 0xe6, 0x18,
	0xcc, 0xb1, 0x01, 0x11, 0x85, 0x77, 0x1d, 0x96,
	0xa7, 0xa3, 0x00, 0x84, 0x02, 0xa2, 0x83, 0x68,
	0xda, 0x17, 0x27, 0xc8, 0x7f, 0x23, 0xb7, 0xf4,
	0x13, 0x85, 0xcf, 0xdd, 0x7a, 0x7d, 0x24, 0x57,
	0xfe, 0x05, 0x93, 0xf5, 0x74, 0xce, 0xed, 0x0c,
	0x20, 0x98, 0x8d, 0x92, 0x30, 0xa1, 0x29, 0x23,
	0x1a, 0xa0, 0x4f, 0x69, 0x56, 0x4c, 0xe1, 0xc8,
	0xce, 0xf6, 0x9a, 0x0c, 0xa4, 0xfa, 0x04, 0xf6,
	0x62, 0x95, 0xf2, 0xfa, 0xc7, 0x40, 0x68, 0x40,
	0x8f, 0x41, 0xda, 0xb4, 0x26, 0x6f, 0x70, 0xab,
	0x40, 0x61, 0xa4, 0x0e, 0x75, 0xfb, 0x86, 0xeb,
	0x9d, 0x9a, 0x1f, 0xec, 0x76, 0x99, 0xe7, 0xea,
	0xaa, 0x1e, 0x2d, 0xb5, 0xd4, 0xa6, 0x1a, 0xb8,
	0x61, 0x0a, 0x1d, 0x16, 0x5b, 0x98, 0xc2, 0x31,
	0x40, 0xe7, 0x23, 0x1d, 0x66, 0x99, 0xc8, 0xc0,
	0xd7, 0xce, 0xf3, 0x57, 0x40, 0x04, 0x3f, 0xfc,
	0xea, 0xb3, 0xfc, 0xd2, 0xd3, 0x99, 0xa4, 0x94,
	0x69, 0xa0, 0xef, 0xd1, 0x85, 0xb3, 0xa6, 0xb1,
	0x28, 0xbf, 0x94, 0x67, 0x22, 0xc3, 0x36, 0x46,
	0xf8, 0xd2, 0x0f, 0x5f, 0xf4, 0x59, 0x80, 0xe6,
	0x2d, 0x43, 0x08, 0x7d, 0x19, 0x09, 0x97, 0xa7,
	0x4c, 0x3d, 0x8d, 0xba, 0x65, 0x62, 0xa3, 0x71,
	0x33, 0x29, 0x62, 0xdb, 0xc1, 0x33, 0x34, 0x1a,
	0x63, 0x33, 0x16, 0xb6, 0x64, 0x7e, 0xab, 0x33,
	0xf0, 0xe6, 0x26, 0x68, 0xba, 0x1d, 0x2e, 0x38,
	0x08, 0xe6, 0x02, 0xd3, 0x25, 0x2c, 0x47, 0x23,
	0x58, 0x34, 0x0f, 0x9d, 0x63, 0x4f, 0x63, 0xbb,
	0x7f, 0x3b, 0x34, 0x38, 0xa7, 0xb5, 0x8d, 0x65,
	0xd9, 0x9f, 0x79, 0x55, 0x3e, 0x4d, 0xe7, 0x73,
	0xd8, 0xf6, 0x98, 0x97, 0x84, 0x60, 0x9c, 0xc8,
	0xa9, 0x3c, 0xf6, 0xdc, 0x12, 0x5c, 0xe1, 0xbb,
	0x0b, 0x8b, 0x98, 0x9c, 0x9d, 0x26, 0x7c, 0x4a,
	0xe6, 0x46, 0x36, 0x58, 0x21, 0x4a, 0xee, 0xca,
	0xd7, 0x3b, 0xc2, 0x6c, 0x49, 0x2f, 0xe5, 0xd5,
	0x03, 0x59, 0x84, 0x53, 0xcb, 0xfe, 0x92, 0x71,
	0x2e, 0x7c, 0x21, 0xcc, 0x99, 0x85, 0x7f, 0xb8,
	0x74, 0x90, 0x13, 0x42, 0x3f, 0xe0, 0x6b, 0x1d,
	0xf2, 0x4d, 0x54, 0xd4, 0xfc, 0x3a, 0x05, 0xe6,
	0x74, 0xaf, 0xa6, 0xa0, 0x2a, 0x20, 0x23, 0x5d,
	0x34, 0x5c, 0xd9, 0x3e, 0x4e, 0xfa, 0x93, 0xe7,
	0xaa, 0xe9, 0x6f, 0x08, 0x43, 0x67, 0x41, 0xc5,
	0xad, 0xfb, 0x31, 0x95, 0x82, 0x73, 0x32, 0xd8,
	0xa6, 0xa3, 0xed, 0x0e, 0x2d, 0xf6, 0x5f, 0xfd,
	0x80, 0xa6, 0x7a, 0xe0, 0xdf, 0x78, 0x15, 0x29,
	0x74, 0x33, 0xd0, 0x9e, 0x83, 0x86, 0x72, 0x22,
	0x57, 0x29, 0xb9, 0x9e, 0x5d, 0xd3, 0x1a, 0xb5,
	0x96, 0x72, 0x41, 0x3d, 0xf1, 0x64, 0x43, 0x67,
	0xee, 0xaa, 0x5c, 0xd3, 0x9a, 0x96, 0x13, 0x11,
	0x5d, 0xf3, 0x0c, 0x87, 0x82, 0x1e, 0x41, 0x9e,
	0xd0, 0x27, 0xd7, 0x54, 0x3b, 0x67, 0x73, 0x09,
	0x91, 0xe9, 0xd5, 0x36, 0xa7, 0xb5, 0x55, 0xe4,
	0xf3, 0x21, 0x51, 0x49, 0x22, 0x07, 0x55, 0x4f,
	0x44, 0x4b, 0xd2, 0x15, 0x93, 0x17, 0x2a, 0xfa,
	0x4d, 0x4a, 0x57, 0xdb, 0x4c, 0xa6, 0xeb, 0xec,
	0x53, 0x25, 0x6c, 0x21, 0xed, 0x00, 0x4c, 0x3b,
	0xca, 0x14, 0x57, 0xa9, 0xd6, 0x6a, 0xcd, 0x8d,
	0x5e, 0x74, 0xac, 0x72, 0xc1, 0x97, 0xe5, 0x1b,
	0x45, 0x4e, 0xda, 0xfc, 0xcc, 0x40, 0xe8, 0x48,
	0x88, 0x0b, 0xa3, 0xe3, 0x8d, 0x83, 0x42, 0xc3,
	0x23, 0xfd, 0x68, 0xb5, 0x8e, 0xf1, 0x9d, 0x63,
	0x77, 0xe9, 0xa3, 0x8e, 0x8c, 0x26, 0x6b, 0xbd,
	0x72, 0x73, 0x35, 0x0c, 0x03, 0xf8, 0x43, 0x78,
	0x52, 0x71, 0x15, 0x1f, 0x71, 0x5d, 0x6e, 0xed,
	0xb9, 0xcc, 0x86, 0x30, 0xdb, 0x2b, 0xd3, 0x82,
	0x88, 0x23, 0x71, 0x90, 0x53, 0x5c, 0xa9, 0x2f,
	0x76, 0x01, 0xb7, 0x9a, 0xfe, 0x43, 0x55, 0xa3,
	0x04, 0x9b, 0x0e, 0xe4, 0x59, 0xdf, 0xc9, 0xe9,
	0xb1, 0xea, 0x29, 0x28, 0x3c, 0x5c, 0xae, 0x72,
	0x84, 0xb6, 0xc6, 0xeb, 0x0c, 0x27, 0x07, 0x74,
	0x90, 0x0d, 0x31, 0xb0, 0x00, 0x77, 0xe9, 0x40,
	0x70, 0x6f, 0x68, 0xa7, 0xfd, 0x06, 0xec, 0x4b,
	0xc0, 0xb7, 0xac, 0xbc, 0x33, 0xb7, 0x6d, 0x0a,
	0xbd, 0x12, 0x1b, 0x59, 0xcb, 0xdd, 0x32, 0xf5,
	0x1d, 0x94, 0x57, 0x76, 0x9e, 0x0c, 0x18, 0x98,
	0x71, 0xd7, 0x2a, 0xdb, 0x0b, 0x7b, 0xa7, 0x71,
	0xb7, 0x67, 0x81, 0x23, 0x96, 0xae, 0xb9, 0x7e,
	0x32, 0x43, 0x92, 0x8a, 0x19, 0xa0, 0xc4, 0xd4,
	0x3b, 0x57, 0xf9, 0x4a, 0x2c, 0xfb, 0x51, 0x46,
	0xbb, 0xcb, 0x5d, 0xb3, 0xef, 0x13, 0x93, 0x6e,
	0x68, 0x42, 0x54, 0x57, 0xd3, 0x6a, 0x3a, 0x8f,
	0x9d, 0x66, 0xbf, 0xbd, 0x36, 0x23, 0xf5, 0x93,
	0x83, 0x7b, 0x9c, 0xc0, 0xdd, 0xc5, 0x49, 0xc0,
	0x64, 0xed, 0x07, 0x12, 0xb3, 0xe6, 0xe4, 0xe5,
	0x38, 0x95, 0x23, 0xb1, 0xa0, 0x3b, 0x1a, 0x61,
	0xda, 0x17, 0xac, 0xc3, 0x58, 0xdd, 0x74, 0x64,
	0x22, 0x11, 0xe8, 0x32, 0x1d, 0x16, 0x93, 0x85,
	0x99, 0xa5, 0x9c, 0x34, 0x55, 0xb1, 0xe9, 0x20,
	0x72, 0xc9, 0x28, 0x7b, 0x79, 0x00, 0xa1, 0xa6,
	0xa3, 0x27, 0x40, 0x18, 0x8a, 0x54, 0xe0, 0xcc,
	0xe8, 0x4e, 0x8e, 0x43, 0x96, 0xe7, 0x3f, 0xc8,
	0xe9, 0xb2, 0xf9, 0xc9, 0xda, 0x04, 0x71, 0x50,
	0x47, 0xe4, 0xaa, 0xce, 0xa2, 0x30, 0xc8, 0xe4,
	0xac, 0xc7, 0x0d, 0x06, 0x2e, 0xe6, 0xe8, 0x80,
	0x36, 0x29, 0x9e, 0x01, 0xb8, 0xc3, 0xf0, 0xa0,
	0x5d, 0x7a, 0xca, 0x4d, 0xa0, 0x57, 0xbd, 0x2a,
	0x45, 0xa7, 0x7f, 0x9c, 0x93, 0x07, 0x8f, 0x35,
	0x67, 0x92, 0xe3, 0xe9, 0x7f, 0xa8, 0x61, 0x43,
	0x9e, 0x25, 0x4f, 0x33, 0x76, 0x13, 0x6e, 0x12,
	0xb9, 0xdd, 0xa4, 0x7c, 0x08, 0x9f, 0x7c, 0xe7,
	0x0a, 0x8d, 0x84, 0x06, 0xa4, 0x33, 0x17, 0x34,
	0x5e, 0x10, 0x7c, 0xc0, 0xa8, 0x3d, 0x1f, 0x42,
	0x20, 0x51, 0x65, 0x5d, 0x09, 0xc3, 0xaa, 0xc0,
	0xc8, 0x0d, 0xf0, 0x79, 0xbc, 0x20, 0x1b, 0x95,
	0xe7, 0x06, 0x7d, 0x47, 0x20, 0x03, 0x1a, 0x74,
	0xdd, 0xe2, 0xd4, 0xae, 0x38, 0x71, 0x9b, 0xf5,
	0x80, 0xec, 0x08, 0x4e, 0x56, 0xba, 0x76, 0x12,
	0x1a, 0xdf, 0x48, 0xf3, 0xae, 0xb3, 0xe6, 0xe6,
	0xbe, 0xc0, 0x91, 0x2e, 0x01, 0xb3, 0x01, 0x86,
	0xa2, 0xb9, 0x52, 0xd1, 0x21, 0xae, 0xd4, 0x97,
	0x1d, 0xef, 0x41, 0x12, 0x95, 0x3d, 0x48, 0x45,
	0x1c, 0x56, 0x32, 0x8f, 0xb8, 0x43, 0xbb, 0x19,
	0xf3, 0xca, 0xe9, 0xeb, 0x6d, 0x84, 0xbe, 0x86,
	0x06, 0xe2, 0x36, 0xb2, 0x62, 0x9d, 0xd3, 0x4c,
	0x48, 0x18, 0x54, 0x13, 0x4e, 0xcf, 0xfd, 0xba,
	0x84, 0xb9, 0x30, 0x53, 0xcf, 0xfb, 0xb9, 0x29,
	0x8f, 0xdc, 0x9f, 0xef, 0x60, 0x0b, 0x64, 0xf6,
	0x8b, 0xee, 0xa6, 0x91, 0xc2, 0x41, 0x6c, 0xf6,
	0xfa, 0x79, 0x67, 0x4b, 0xc1, 0x3f, 0xaf, 0x09,
	0x81, 0xd4, 0x5d, 0xcb, 0x09, 0xdf, 0x36, 0x31,
	0xc0, 0x14, 0x3c, 0x7c, 0x0e, 0x65, 0x95, 0x99,
	0x6d, 0xa3, 0xf4, 0xd7, 0x38, 0xee, 0x1a, 0x2b,
	0x37, 0xe2, 0xa4, 0x3b, 0x4b, 0xd0, 0x65, 0xca,
	0xf8, 0xc3, 0xe8, 0x15, 0x20, 0xef, 0xf2, 0x00,
	0xfd, 0x01, 0x09, 0xc5, 0xc8, 0x17, 0x04, 0x93,
	0xd0, 0x93, 0x03, 0x55, 0xc5, 0xfe, 0x32, 0xa3,
	0x3e, 0x28, 0x2d, 0x3b, 0x93, 0x8a, 0xcc, 0x07,
	0x72, 0x80, 0x8b, 0x74, 0x16, 0x24, 0xbb, 0xda,
	0x94, 0x39, 0x30, 0x8f, 0xb1, 0xcd, 0x4a, 0x90,
	0x92, 0x7c, 0x14, 0x8f, 0x95, 0x4e, 0xac, 0x9b,
	0xd8, 0x8f, 0x1a, 0x87, 0xa4, 0x32, 0x27, 0x8a,
	0xba, 0xf7, 0x41, 0xcf, 0x84, 0x37, 0x19, 0xe6,
	0x06, 0xf5, 0x0e, 0xcf, 0x36, 0xf5, 0x9e, 0x6c,
	0xde, 0xbc, 0xff, 0x64, 0x7e, 0x4e, 0x59, 0x57,
	0x48, 0xfe, 0x14, 0xf7, 0x9c, 0x93, 0x5d, 0x15,
	0xad, 0xcc, 0x11, 0xb1, 0x17, 0x18, 0xb2, 0x7e,
	0xcc, 0xab, 0xe9, 0xce, 0x7d, 0x77, 0x5b, 0x51,
	0x1b, 0x1e, 0x20, 0xa8, 0x32, 0x06, 0x0e, 0x75,
	0x93, 0xac, 0xdb, 0x35, 0x37, 0x1f, 0xe9, 0x19,
	0x1d, 0xb4, 0x71, 0x97, 0xd6, 0x4e, 0x2c, 0x08,
	0xa5, 0x13, 0xf9, 0x0e, 0x7e, 0x78, 0x6e, 0x14,
	0xe0, 0xa9, 0xb9, 0x96, 0x4c, 0x80, 0x82, 0xba,
	0x17, 0xb3, 0x9d, 0x69, 0xb0, 0x84, 0x46, 0xff,
	0xf9, 0x52, 0x79, 0x94, 0x58, 0x3a, 0x62, 0x90,
	0x15, 0x35, 0x71, 0x10, 0x37, 0xed, 0xa1, 0x8e,
	0x53, 0x6e, 0xf4, 0x26, 0x57, 0x93, 0x15, 0x93,
	0xf6, 0x81, 0x2c, 0x5a, 0x10, 0xda, 0x92, 0xad,
	0x2f, 0xdb, 0x28, 0x31, 0x2d, 0x55, 0x04, 0xd2,
	0x06, 0x28, 0x8c, 0x1e, 0xdc, 0xea, 0x54, 0xac,
	0xff, 0xb7, 0x6c, 0x30, 0x15, 0xd4, 0xb4, 0x0d,
	0x00, 0x93, 0x57, 0xdd, 0xd2, 0x07, 0x07, 0x06,
	0xd9, 0x43, 0x9b, 0xcd, 0x3a, 0xf4, 0x7d, 0x4c,
	0x36, 0x5d, 0x23, 0xa2, 0xcc, 0x57, 0x40, 0x91,
	0xe9, 0x2c, 0x2f, 0x2c, 0xd5, 0x30, 0x9b, 0x17,
	0xb0, 0xc9, 0xf7, 0xa7, 0x2f, 0xd1, 0x93, 0x20,
	0x6b, 0xc6, 0xc1, 0xe4, 0x6f, 0xcb, 0xd1, 0xe7,
	0x09, 0x0f, 0x9e, 0xdc, 0xaa, 0x9f, 0x2f, 0xdf,
	0x56, 0x9f, 0xd4, 0x33, 0x04, 0xaf, 0xd3, 0x6c,
	0x58, 0x61, 0xf0, 0x30, 0xec, 0xf2, 0x7f, 0xf2,
	0x9c, 0xdf, 0x39, 0xbb, 0x6f, 0xa2, 0x8c, 0x7e,
	0xc4, 0x22, 0x51, 0x71, 0xc0, 0x4d, 0x14, 0x1a,
	0xc4, 0xcd, 0x04, 0xd9, 0x87, 0x08, 0x50, 0x05,
	0xcc, 0xaf, 0xf6, 0xf0, 0x8f, 0x92, 0x54, 0x58,
	0xc2, 0xc7, 0x09, 0x7a, 0x59, 0x02, 0x05, 0xe8,
	0xb0, 0x86, 0xd9, 0xbf, 0x7b, 0x35, 0x51, 0x4d,
	0xaf, 0x08, 0x97, 0x2c, 0x65, 0xda, 0x2a, 0x71,
	0x3a, 0xa8, 0x51, 0xcc, 0xf2, 0x73, 0x27, 0xc3,
	0xfd, 0x62, 0xcf, 0xe3, 0xb2, 0xca, 0xcb, 0xbe,
	0x1a, 0x0a, 0xa1, 0x34, 0x7b, 0x77, 0xc4, 0x62,
	0x68, 0x78, 0x5f, 0x94, 0x07, 0x04, 0x65, 0x16,
	0x4b, 0x61, 0xcb, 0xff, 0x75, 0x26, 0x50, 0x66,
	0x1f, 0x6e, 0x93, 0xf8, 0xc5, 0x51, 0xeb, 0xa4,
	0x4a, 0x48, 0x68, 0x6b, 0xe2, 0x5e, 0x44, 0xb2,
	0x50, 0x2c, 0x6c, 0xae, 0x79, 0x4e, 0x66, 0x35,
	0x81, 0x50, 0xac, 0xbc, 0x3f, 0xb1, 0x0c, 0xf3,
	0x05, 0x3c, 0x4a, 0xa3, 0x6c, 0x2a, 0x79, 0xb4,
	0xb7, 0xab, 0xca, 0xc7, 0x9b, 0x8e, 0xcd, 0x5f,
	0x11, 0x03, 0xcb, 0x30, 0xa3, 0xab, 0xda, 0xfe,
	0x64, 0xb9, 0xbb, 0xd8, 0x5e, 0x3a, 0x1a, 0x56,
	0xe5, 0x05, 0x48, 0x90, 0x1e, 0x61, 0x69, 0x1b,
	0x22, 0xe6, 0x1a, 0x3c, 0x75, 0xad, 0x1f, 0x37,
	0x28, 0xdc, 0xe4, 0x6d, 0xbd, 0x42, 0xdc, 0xd3,
	0xc8, 0xb6, 0x1c, 0x48, 0xfe, 0x94, 0x77, 0x7f,
	0xbd, 0x62, 0xac, 0xa3, 0x47, 0x27, 0xcf, 0x5f,
	0xd9, 0xdb, 0xaf, 0xec, 0xf7, 0x5e, 0xc1, 0xb0,
	0x9d, 0x01, 0x26, 0x99, 0x7e, 0x8f, 0x03, 0x70,
	0xb5, 0x42, 0xbe, 0x67, 0x28, 0x1b, 0x7c, 0xbd,
	0x61, 0x21, 0x97, 0xcc, 0x5c, 0xe1, 0x97, 0x8f,
	0x8d, 0xde, 0x2b, 0xaa, 0xa7, 0x71, 0x1d, 0x1e,
	0x02, 0x73, 0x70, 0x58, 0x32, 0x5b, 0x1d, 0x67,
	0x3d, 0xe0, 0x74, 0x4f, 0x03, 0xf2, 0x70, 0x51,
	0x79, 0xf1, 0x61, 0x70, 0x15, 0x74, 0x9d, 0x23,
	0x89, 0xde, 0xac, 0xfd, 0xde, 0xd0, 0x1f, 0xc3,
	0x87, 0x44, 0x35, 0x4b, 0xe5, 0xb0, 0x60, 0xc5,
	0x22, 0xe4, 0x9e, 0xca, 0xeb, 0xd5, 0x3a, 0x09,
	0x45, 0xa4, 0xdb, 0xfa, 0x3f, 0xeb, 0x1b, 0xc7,
	0xc8, 0x14, 0x99, 0x51, 0x92, 0x10, 0xed, 0xed,
	0x28, 0xe0, 0xa1, 0xf8, 0x26, 0xcf, 0xcd, 0xcb,
	0x63, 0xa1, 0x3b, 0xe3, 0xdf, 0x7e, 0xfe, 0xa6,
	0xf0, 0x81, 0x9a, 0xbf, 0x55, 0xde, 0x54, 0xd5,
	0x56, 0x60, 0x98, 0x10, 0x68, 0xf4, 0x38, 0x96,
	0x8e, 0x6f, 0x1d, 0x44, 0x7f, 0xd6, 0x2f, 0xfe,
	0x55, 0xfb, 0x0c, 0x7e, 0x67, 0xe2, 0x61, 0x44,
	0xed, 0xf2, 0x35, 0x30, 0x5d, 0xe9, 0xc7, 0xd6,
	0x6d, 0xe0, 0xa0, 0xed, 0xf3, 0xfc, 0xd8, 0x3e,
	0x0a, 0x7b, 0xcd, 0xaf, 0x65, 0x68, 0x18, 0xc0,
	0xec, 0x04, 0x1c, 0x74, 0x6d, 0xe2, 0x6e, 0x79,
	0xd4, 0x11, 0x2b, 0x62, 0xd5, 0x27, 0xad, 0x4f,
	0x01, 0x59, 0x73, 0xcc, 0x6a, 0x53, 0xfb, 0x2d,
	0xd5, 0x4e, 0x99, 0x21, 0x65, 0x4d, 0xf5, 0x82,
	0xf7, 0xd8, 0x42, 0xce, 0x6f, 0x3d, 0x36, 0x47,
	0xf1, 0x05, 0x16, 0xe8, 0x1b, 0x6a, 0x8f, 0x93,
	0xf2, 0x8f, 0x37, 0x40, 0x12, 0x28, 0xa3, 0xe6,
	0xb9, 0x17, 0x4a, 0x1f, 0xb1, 0xd1, 0x66, 0x69,
	0x86, 0xc4, 0xfc, 0x97, 0xae, 0x3f, 0x8f, 0x1e,
	0x2b, 0xdf, 0xcd, 0xf9, 0x3c
};
static const u8 dec_output011[] __initconst = {
	0x7a, 0x57, 0xf2, 0xc7, 0x06, 0x3f, 0x50, 0x7b,
	0x36, 0x1a, 0x66, 0x5c, 0xb9, 0x0e, 0x5e, 0x3b,
	0x45, 0x60, 0xbe, 0x9a, 0x31, 0x9f, 0xff, 0x5d,
	0x66, 0x34, 0xb4, 0xdc, 0xfb, 0x9d, 0x8e, 0xee,
	0x6a, 0x33, 0xa4, 0x07, 0x3c, 0xf9, 0x4c, 0x30,
	0xa1, 0x24, 0x52, 0xf9, 0x50, 0x46, 0x88, 0x20,
	0x02, 0x32, 0x3a, 0x0e, 0x99, 0x63, 0xaf, 0x1f,
	0x15, 0x28, 0x2a, 0x05, 0xff, 0x57, 0x59, 0x5e,
	0x18, 0xa1, 0x1f, 0xd0, 0x92, 0x5c, 0x88, 0x66,
	0x1b, 0x00, 0x64, 0xa5, 0x93, 0x8d, 0x06, 0x46,
	0xb0, 0x64, 0x8b, 0x8b, 0xef, 0x99, 0x05, 0x35,
	0x85, 0xb3, 0xf3, 0x33, 0xbb, 0xec, 0x66, 0xb6,
	0x3d, 0x57, 0x42, 0xe3, 0xb4, 0xc6, 0xaa, 0xb0,
	0x41, 0x2a, 0xb9, 0x59, 0xa9, 0xf6, 0x3e, 0x15,
	0x26, 0x12, 0x03, 0x21, 0x4c, 0x74, 0x43, 0x13,
	0x2a, 0x03, 0x27, 0x09, 0xb4, 0xfb, 0xe7, 0xb7,
	0x40, 0xff, 0x5e, 0xce, 0x48, 0x9a, 0x60, 0xe3,
	0x8b, 0x80, 0x8c, 0x38, 0x2d, 0xcb, 0x93, 0x37,
	0x74, 0x05, 0x52, 0x6f, 0x73, 0x3e, 0xc3, 0xbc,
	0xca, 0x72, 0x0a, 0xeb, 0xf1, 0x3b, 0xa0, 0x95,
	0xdc, 0x8a, 0xc4, 0xa9, 0xdc, 0xca, 0x44, 0xd8,
	0x08, 0x63, 0x6a, 0x36, 0xd3, 0x3c, 0xb8, 0xac,
	0x46, 0x7d, 0xfd, 0xaa, 0xeb, 0x3e, 0x0f, 0x45,
	0x8f, 0x49, 0xda, 0x2b, 0xf2, 0x12, 0xbd, 0xaf,
	0x67, 0x8a, 0x63, 0x48, 0x4b, 0x55, 0x5f, 0x6d,
	0x8c, 0xb9, 0x76, 0x34, 0x84, 0xae, 0xc2, 0xfc,
	0x52, 0x64, 0x82, 0xf7, 0xb0, 0x06, 0xf0, 0x45,
	0x73, 0x12, 0x50, 0x30, 0x72, 0xea, 0x78, 0x9a,
	0xa8, 0xaf, 0xb5, 0xe3, 0xbb, 0x77, 0x52, 0xec,
	0x59, 0x84, 0xbf, 0x6b, 0x8f, 0xce, 0x86, 0x5e,
	0x1f, 0x23, 0xe9, 0xfb, 0x08, 0x86, 0xf7, 0x10,
	0xb9, 0xf2, 0x44, 0x96, 0x44, 0x63, 0xa9, 0xa8,
	0x78, 0x00, 0x23, 0xd6, 0xc7, 0xe7, 0x6e, 0x66,
	0x4f, 0xcc, 0xee, 0x15, 0xb3, 0xbd, 0x1d, 0xa0,
	0xe5, 0x9c, 0x1b, 0x24, 0x2c, 0x4d, 0x3c, 0x62,
	0x35, 0x9c, 0x88, 0x59, 0x09, 0xdd, 0x82, 0x1b,
	0xcf, 0x0a, 0x83, 0x6b, 0x3f, 0xae, 0x03, 0xc4,
	0xb4, 0xdd, 0x7e, 0x5b, 0x28, 0x76, 0x25, 0x96,
	0xd9, 0xc9, 0x9d, 0x5f, 0x86, 0xfa, 0xf6, 0xd7,
	0xd2, 0xe6, 0x76, 0x1d, 0x0f, 0xa1, 0xdc, 0x74,
	0x05, 0x1b, 0x1d, 0xe0, 0xcd, 0x16, 0xb0, 0xa8,
	0x8a, 0x34, 0x7b, 0x15, 0x11, 0x77, 0xe5, 0x7b,
	0x7e, 0x20, 0xf7, 0xda, 0x38, 0xda, 0xce, 0x70,
	0xe9, 0xf5, 0x6c, 0xd9, 0xbe, 0x0c, 0x4c, 0x95,
	0x4c, 0xc2, 0x9b, 0x34, 0x55, 0x55, 0xe1, 0xf3,
	0x46, 0x8e, 0x48, 0x74, 0x14, 0x4f, 0x9d, 0xc9,
	0xf5, 0xe8, 0x1a, 0xf0, 0x11, 0x4a, 0xc1, 0x8d,
	0xe0, 0x93, 0xa0, 0xbe, 0x09, 0x1c, 0x2b, 0x4e,
	0x0f, 0xb2, 0x87, 0x8b, 0x84, 0xfe, 0x92, 0x32,
	0x14, 0xd7, 0x93, 0xdf, 0xe7, 0x44, 0xbc, 0xc5,
	0xae, 0x53, 0x69, 0xd8, 0xb3, 0x79, 0x37, 0x80,
	0xe3, 0x17, 0x5c, 0xec, 0x53, 0x00, 0x9a, 0xe3,
	0x8e, 0xdc, 0x38, 0xb8, 0x66, 0xf0, 0xd3, 0xad,
	0x1d, 0x02, 0x96, 0x86, 0x3e, 0x9d, 0x3b, 0x5d,
	0xa5, 0x7f, 0x21, 0x10, 0xf1, 0x1f, 0x13, 0x20,
	0xf9, 0x57, 0x87, 0x20, 0xf5, 0x5f, 0xf1, 0x17,
	0x48, 0x0a, 0x51, 0x5a, 0xcd, 0x19, 0x03, 0xa6,
	0x5a, 0xd1, 0x12, 0x97, 0xe9, 0x48, 0xe2, 0x1d,
	0x83, 0x75, 0x50, 0xd9, 0x75, 0x7d, 0x6a, 0x82,
	0xa1, 0xf9, 0x4e, 0x54, 0x87, 0x89, 0xc9, 0x0c,
	0xb7, 0x5b, 0x6a, 0x91, 0xc1, 0x9c, 0xb2, 0xa9,
	0xdc, 0x9a, 0xa4, 0x49, 0x0a, 0x6d, 0x0d, 0xbb,
	0xde, 0x86, 0x44, 0xdd, 0x5d, 0x89, 0x2b, 0x96,
	0x0f, 0x23, 0x95, 0xad, 0xcc, 0xa2, 0xb3, 0xb9,
	0x7e, 0x74, 0x38, 0xba, 0x9f, 0x73, 0xae, 0x5f,
	0xf8, 0x68, 0xa2, 0xe0, 0xa9, 0xce, 0xbd, 0x40,
	0xd4, 0x4c, 0x6b, 0xd2, 0x56, 0x62, 0xb0, 0xcc,
	0x63, 0x7e, 0x5b, 0xd3, 0xae, 0xd1, 0x75, 0xce,
	0xbb, 0xb4, 0x5b, 0xa8, 0xf8, 0xb4, 0xac, 0x71,
	0x75, 0xaa, 0xc9, 0x9f, 0xbb, 0x6c, 0xad, 0x0f,
	0x55, 0x5d, 0xe8, 0x85, 0x7d, 0xf9, 0x21, 0x35,
	0xea, 0x92, 0x85, 0x2b, 0x00, 0xec, 0x84, 0x90,
	0x0a, 0x63, 0x96, 0xe4, 0x6b, 0xa9, 0x77, 0xb8,
	0x91, 0xf8, 0x46, 0x15, 0x72, 0x63, 0x70, 0x01,
	0x40, 0xa3, 0xa5, 0x76, 0x62, 0x2b, 0xbf, 0xf1,
	0xe5, 0x8d, 0x9f, 0xa3, 0xfa, 0x9b, 0x03, 0xbe,
	0xfe, 0x65, 0x6f, 0xa2, 0x29, 0x0d, 0x54, 0xb4,
	0x71, 0xce, 0xa9, 0xd6, 0x3d, 0x88, 0xf9, 0xaf,
	0x6b, 0xa8, 0x9e, 0xf4, 0x16, 0x96, 0x36, 0xb9,
	0x00, 0xdc, 0x10, 0xab, 0xb5, 0x08, 0x31, 0x1f,
	0x00, 0xb1, 0x3c, 0xd9, 0x38, 0x3e, 0xc6, 0x04,
	0xa7, 0x4e, 0xe8, 0xae, 0xed, 0x98, 0xc2, 0xf7,
	0xb9, 0x00, 0x5f, 0x8c, 0x60, 0xd1, 0xe5, 0x15,
	0xf7, 0xae, 0x1e, 0x84, 0x88, 0xd1, 0xf6, 0xbc,
	0x3a, 0x89, 0x35, 0x22, 0x83, 0x7c, 0xca, 0xf0,
	0x33, 0x82, 0x4c, 0x79, 0x3c, 0xfd, 0xb1, 0xae,
	0x52, 0x62, 0x55, 0xd2, 0x41, 0x60, 0xc6, 0xbb,
	0xfa, 0x0e, 0x59, 0xd6, 0xa8, 0xfe, 0x5d, 0xed,
	0x47, 0x3d, 0xe0, 0xea, 0x1f, 0x6e, 0x43, 0x51,
	0xec, 0x10, 0x52, 0x56, 0x77, 0x42, 0x6b, 0x52,
	0x87, 0xd8, 0xec, 0xe0, 0xaa, 0x76, 0xa5, 0x84,
	0x2a, 0x22, 0x24, 0xfd, 0x92, 0x40, 0x88, 0xd5,
	0x85, 0x1c, 0x1f, 0x6b, 0x47, 0xa0, 0xc4, 0xe4,
	0xef, 0xf4, 0xea, 0xd7, 0x59, 0xac, 0x2a, 0x9e,
	0x8c, 0xfa, 0x1f, 0x42, 0x08, 0xfe, 0x4f, 0x74,
	0xa0, 0x26, 0xf5, 0xb3, 0x84, 0xf6, 0x58, 0x5f,
	0x26, 0x66, 0x3e, 0xd7, 0xe4, 0x22, 0x91, 0x13,
	0xc8, 0xac, 0x25, 0x96, 0x23, 0xd8, 0x09, 0xea,
	0x45, 0x75, 0x23, 0xb8, 0x5f, 0xc2, 0x90, 0x8b,
	0x09, 0xc4, 0xfc, 0x47, 0x6c, 0x6d, 0x0a, 0xef,
	0x69, 0xa4, 0x38, 0x19, 0xcf, 0x7d, 0xf9, 0x09,
	0x73, 0x9b, 0x60, 0x5a, 0xf7, 0x37, 0xb5, 0xfe,
	0x9f, 0xe3, 0x2b, 0x4c, 0x0d, 0x6e, 0x19, 0xf1,
	0xd6, 0xc0, 0x70, 0xf3, 0x9d, 0x22, 0x3c, 0xf9,
	0x49, 0xce, 0x30, 0x8e, 0x44, 0xb5, 0x76, 0x15,
	0x8f, 0x52, 0xfd, 0xa5, 0x04, 0xb8, 0x55, 0x6a,
	0x36, 0x59, 0x7c, 0xc4, 0x48, 0xb8, 0xd7, 0xab,
	0x05, 0x66, 0xe9, 0x5e, 0x21, 0x6f, 0x6b, 0x36,
	0x29, 0xbb, 0xe9, 0xe3, 0xa2, 0x9a, 0xa8, 0xcd,
	0x55, 0x25, 0x11, 0xba, 0x5a, 0x58, 0xa0, 0xde,
	0xae, 0x19, 0x2a, 0x48, 0x5a, 0xff, 0x36, 0xcd,
	0x6d, 0x16, 0x7a, 0x73, 0x38, 0x46, 0xe5, 0x47,
	0x59, 0xc8, 0xa2, 0xf6, 0xe2, 0x6c, 0x83, 0xc5,
	0x36, 0x2c, 0x83, 0x7d, 0xb4, 0x01, 0x05, 0x69,
	0xe7, 0xaf, 0x5c, 0xc4, 0x64, 0x82, 0x12, 0x21,
	0xef, 0xf7, 0xd1, 0x7d, 0xb8, 0x8d, 0x8c, 0x98,
	0x7c, 0x5f, 0x7d, 0x92, 0x88, 0xb9, 0x94, 0x07,
	0x9c, 0xd8, 0xe9, 0x9c, 0x17, 0x38, 0xe3, 0x57,
	0x6c, 0xe0, 0xdc, 0xa5, 0x92, 0x42, 0xb3, 0xbd,
	0x50, 0xa2, 0x7e, 0xb5, 0xb1, 0x52, 0x72, 0x03,
	0x97, 0xd8, 0xaa, 0x9a, 0x1e, 0x75, 0x41, 0x11,
	0xa3, 0x4f, 0xcc, 0xd4, 0xe3, 0x73, 0xad, 0x96,
	0xdc, 0x47, 0x41, 0x9f, 0xb0, 0xbe, 0x79, 0x91,
	0xf5, 0xb6, 0x18, 0xfe, 0xc2, 0x83, 0x18, 0x7d,
	0x73, 0xd9, 0x4f, 0x83, 0x84, 0x03, 0xb3, 0xf0,
	0x77, 0x66, 0x3d, 0x83, 0x63, 0x2e, 0x2c, 0xf9,
	0xdd, 0xa6, 0x1f, 0x89, 0x82, 0xb8, 0x23, 0x42,
	0xeb, 0xe2, 0xca, 0x70, 0x82, 0x61, 0x41, 0x0a,
	0x6d, 0x5f, 0x75, 0xc5, 0xe2, 0xc4, 0x91, 0x18,
	0x44, 0x22, 0xfa, 0x34, 0x10, 0xf5, 0x20, 0xdc,
	0xb7, 0xdd, 0x2a, 0x20, 0x77, 0xf5, 0xf9, 0xce,
	0xdb, 0xa0, 0x0a, 0x52, 0x2a, 0x4e, 0xdd, 0xcc,
	0x97, 0xdf, 0x05, 0xe4, 0x5e, 0xb7, 0xaa, 0xf0,
	0xe2, 0x80, 0xff, 0xba, 0x1a, 0x0f, 0xac, 0xdf,
	0x02, 0x32, 0xe6, 0xf7, 0xc7, 0x17, 0x13, 0xb7,
	0xfc, 0x98, 0x48, 0x8c, 0x0d, 0x82, 0xc9, 0x80,
	0x7a, 0xe2, 0x0a, 0xc5, 0xb4, 0xde, 0x7c, 0x3c,
	0x79, 0x81, 0x0e, 0x28, 0x65, 0x79, 0x67, 0x82,
	0x69, 0x44, 0x66, 0x09, 0xf7, 0x16, 0x1a, 0xf9,
	0x7d, 0x80, 0xa1, 0x79, 0x14, 0xa9, 0xc8, 0x20,
	0xfb, 0xa2, 0x46, 0xbe, 0x08, 0x35, 0x17, 0x58,
	0xc1, 0x1a, 0xda, 0x2a, 0x6b, 0x2e, 0x1e, 0xe6,
	0x27, 0x55, 0x7b, 0x19, 0xe2, 0xfb, 0x64, 0xfc,
	0x5e, 0x15, 0x54, 0x3c, 0xe7, 0xc2, 0x11, 0x50,
	0x30, 0xb8, 0x72, 0x03, 0x0b, 0x1a, 0x9f, 0x86,
	0x27, 0x11, 0x5c, 0x06, 0x2b, 0xbd, 0x75, 0x1a,
	0x0a, 0xda, 0x01, 0xfa, 0x5c, 0x4a, 0xc1, 0x80,
	0x3a, 0x6e, 0x30, 0xc8, 0x2c, 0xeb, 0x56, 0xec,
	0x89, 0xfa, 0x35, 0x7b, 0xb2, 0xf0, 0x97, 0x08,
	0x86, 0x53, 0xbe, 0xbd, 0x40, 0x41, 0x38, 0x1c,
	0xb4, 0x8b, 0x79, 0x2e, 0x18, 0x96, 0x94, 0xde,
	0xe8, 0xca, 0xe5, 0x9f, 0x92, 0x9f, 0x15, 0x5d,
	0x56, 0x60, 0x5c, 0x09, 0xf9, 0x16, 0xf4, 0x17,
	0x0f, 0xf6, 0x4c, 0xda, 0xe6, 0x67, 0x89, 0x9f,
	0xca, 0x6c, 0xe7, 0x9b, 0x04, 0x62, 0x0e, 0x26,
	0xa6, 0x52, 0xbd, 0x29, 0xff, 0xc7, 0xa4, 0x96,
	0xe6, 0x6a, 0x02, 0xa5, 0x2e, 0x7b, 0xfe, 0x97,
	0x68, 0x3e, 0x2e, 0x5f, 0x3b, 0x0f, 0x36, 0xd6,
	0x98, 0x19, 0x59, 0x48, 0xd2, 0xc6, 0xe1, 0x55,
	0x1a, 0x6e, 0xd6, 0xed, 0x2c, 0xba, 0xc3, 0x9e,
	0x64, 0xc9, 0x95, 0x86, 0x35, 0x5e, 0x3e, 0x88,
	0x69, 0x99, 0x4b, 0xee, 0xbe, 0x9a, 0x99, 0xb5,
	0x6e, 0x58, 0xae, 0xdd, 0x22, 0xdb, 0xdd, 0x6b,
	0xfc, 0xaf, 0x90, 0xa3, 0x3d, 0xa4, 0xc1, 0x15,
	0x92, 0x18, 0x8d, 0xd2, 0x4b, 0x7b, 0x06, 0xd1,
	0x37, 0xb5, 0xe2, 0x7c, 0x2c, 0xf0, 0x25, 0xe4,
	0x94, 0x2a, 0xbd, 0xe3, 0x82, 0x70, 0x78, 0xa3,
	0x82, 0x10, 0x5a, 0x90, 0xd7, 0xa4, 0xfa, 0xaf,
	0x1a, 0x88, 0x59, 0xdc, 0x74, 0x12, 0xb4, 0x8e,
	0xd7, 0x19, 0x46, 0xf4, 0x84, 0x69, 0x9f, 0xbb,
	0x70, 0xa8, 0x4c, 0x52, 0x81, 0xa9, 0xff, 0x76,
	0x1c, 0xae, 0xd8, 0x11, 0x3d, 0x7f, 0x7d, 0xc5,
	0x12, 0x59, 0x28, 0x18, 0xc2, 0xa2, 0xb7, 0x1c,
	0x88, 0xf8, 0xd6, 0x1b, 0xa6, 0x7d, 0x9e, 0xde,
	0x29, 0xf8, 0xed, 0xff, 0xeb, 0x92, 0x24, 0x4f,
	0x05, 0xaa, 0xd9, 0x49, 0xba, 0x87, 0x59, 0x51,
	0xc9, 0x20, 0x5c, 0x9b, 0x74, 0xcf, 0x03, 0xd9,
	0x2d, 0x34, 0xc7, 0x5b, 0xa5, 0x40, 0xb2, 0x99,
	0xf5, 0xcb, 0xb4, 0xf6, 0xb7, 0x72, 0x4a, 0xd6,
	0xbd, 0xb0, 0xf3, 0x93, 0xe0, 0x1b, 0xa8, 0x04,
	0x1e, 0x35, 0xd4, 0x80, 0x20, 0xf4, 0x9c, 0x31,
	0x6b, 0x45, 0xb9, 0x15, 0xb0, 0x5e, 0xdd, 0x0a,
	0x33, 0x9c, 0x83, 0xcd, 0x58, 0x89, 0x50, 0x56,
	0xbb, 0x81, 0x00, 0x91, 0x32, 0xf3, 0x1b, 0x3e,
	0xcf, 0x45, 0xe1, 0xf9, 0xe1, 0x2c, 0x26, 0x78,
	0x93, 0x9a, 0x60, 0x46, 0xc9, 0xb5, 0x5e, 0x6a,
	0x28, 0x92, 0x87, 0x3f, 0x63, 0x7b, 0xdb, 0xf7,
	0xd0, 0x13, 0x9d, 0x32, 0x40, 0x5e, 0xcf, 0xfb,
	0x79, 0x68, 0x47, 0x4c, 0xfd, 0x01, 0x17, 0xe6,
	0x97, 0x93, 0x78, 0xbb, 0xa6, 0x27, 0xa3, 0xe8,
	0x1a, 0xe8, 0x94, 0x55, 0x7d, 0x08, 0xe5, 0xdc,
	0x66, 0xa3, 0x69, 0xc8, 0xca, 0xc5, 0xa1, 0x84,
	0x55, 0xde, 0x08, 0x91, 0x16, 0x3a, 0x0c, 0x86,
	0xab, 0x27, 0x2b, 0x64, 0x34, 0x02, 0x6c, 0x76,
	0x8b, 0xc6, 0xaf, 0xcc, 0xe1, 0xd6, 0x8c, 0x2a,
	0x18, 0x3d, 0xa6, 0x1b, 0x37, 0x75, 0x45, 0x73,
	0xc2, 0x75, 0xd7, 0x53, 0x78, 0x3a, 0xd6, 0xe8,
	0x29, 0xd2, 0x4a, 0xa8, 0x1e, 0x82, 0xf6, 0xb6,
	0x81, 0xde, 0x21, 0xed, 0x2b, 0x56, 0xbb, 0xf2,
	0xd0, 0x57, 0xc1, 0x7c, 0xd2, 0x6a, 0xd2, 0x56,
	0xf5, 0x13, 0x5f, 0x1c, 0x6a, 0x0b, 0x74, 0xfb,
	0xe9, 0xfe, 0x9e, 0xea, 0x95, 0xb2, 0x46, 0xab,
	0x0a, 0xfc, 0xfd, 0xf3, 0xbb, 0x04, 0x2b, 0x76,
	0x1b, 0xa4, 0x74, 0xb0, 0xc1, 0x78, 0xc3, 0x69,
	0xe2, 0xb0, 0x01, 0xe1, 0xde, 0x32, 0x4c, 0x8d,
	0x1a, 0xb3, 0x38, 0x08, 0xd5, 0xfc, 0x1f, 0xdc,
	0x0e, 0x2c, 0x9c, 0xb1, 0xa1, 0x63, 0x17, 0x22,
	0xf5, 0x6c, 0x93, 0x70, 0x74, 0x00, 0xf8, 0x39,
	0x01, 0x94, 0xd1, 0x32, 0x23, 0x56, 0x5d, 0xa6,
	0x02, 0x76, 0x76, 0x93, 0xce, 0x2f, 0x19, 0xe9,
	0x17, 0x52, 0xae, 0x6e, 0x2c, 0x6d, 0x61, 0x7f,
	0x3b, 0xaa, 0xe0, 0x52, 0x85, 0xc5, 0x65, 0xc1,
	0xbb, 0x8e, 0x5b, 0x21, 0xd5, 0xc9, 0x78, 0x83,
	0x07, 0x97, 0x4c, 0x62, 0x61, 0x41, 0xd4, 0xfc,
	0xc9, 0x39, 0xe3, 0x9b, 0xd0, 0xcc, 0x75, 0xc4,
	0x97, 0xe6, 0xdd, 0x2a, 0x5f, 0xa6, 0xe8, 0x59,
	0x6c, 0x98, 0xb9, 0x02, 0xe2, 0xa2, 0xd6, 0x68,
	0xee, 0x3b, 0x1d, 0xe3, 0x4d, 0x5b, 0x30, 0xef,
	0x03, 0xf2, 0xeb, 0x18, 0x57, 0x36, 0xe8, 0xa1,
	0xf4, 0x47, 0xfb, 0xcb, 0x8f, 0xcb, 0xc8, 0xf3,
	0x4f, 0x74, 0x9d, 0x9d, 0xb1, 0x8d, 0x14, 0x44,
	0xd9, 0x19, 0xb4, 0x54, 0x4f, 0x75, 0x19, 0x09,
	0xa0, 0x75, 0xbc, 0x3b, 0x82, 0xc6, 0x3f, 0xb8,
	0x83, 0x19, 0x6e, 0xd6, 0x37, 0xfe, 0x6e, 0x8a,
	0x4e, 0xe0, 0x4a, 0xab, 0x7b, 0xc8, 0xb4, 0x1d,
	0xf4, 0xed, 0x27, 0x03, 0x65, 0xa2, 0xa1, 0xae,
	0x11, 0xe7, 0x98, 0x78, 0x48, 0x91, 0xd2, 0xd2,
	0xd4, 0x23, 0x78, 0x50, 0xb1, 0x5b, 0x85, 0x10,
	0x8d, 0xca, 0x5f, 0x0f, 0x71, 0xae, 0x72, 0x9a,
	0xf6, 0x25, 0x19, 0x60, 0x06, 0xf7, 0x10, 0x34,
	0x18, 0x0d, 0xc9, 0x9f, 0x7b, 0x0c, 0x9b, 0x8f,
	0x91, 0x1b, 0x9f, 0xcd, 0x10, 0xee, 0x75, 0xf9,
	0x97, 0x66, 0xfc, 0x4d, 0x33, 0x6e, 0x28, 0x2b,
	0x92, 0x85, 0x4f, 0xab, 0x43, 0x8d, 0x8f, 0x7d,
	0x86, 0xa7, 0xc7, 0xd8, 0xd3, 0x0b, 0x8b, 0x57,
	0xb6, 0x1d, 0x95, 0x0d, 0xe9, 0xbc, 0xd9, 0x03,
	0xd9, 0x10, 0x19, 0xc3, 0x46, 0x63, 0x55, 0x87,
	0x61, 0x79, 0x6c, 0x95, 0x0e, 0x9c, 0xdd, 0xca,
	0xc3, 0xf3, 0x64, 0xf0, 0x7d, 0x76, 0xb7, 0x53,
	0x67, 0x2b, 0x1e, 0x44, 0x56, 0x81, 0xea, 0x8f,
	0x5c, 0x42, 0x16, 0xb8, 0x28, 0xeb, 0x1b, 0x61,
	0x10, 0x1e, 0xbf, 0xec, 0xa8
};
static const u8 dec_assoc011[] __initconst = {
	0xd6, 0x31, 0xda, 0x5d, 0x42, 0x5e, 0xd7
};
static const u8 dec_nonce011[] __initconst = {
	0xfd, 0x87, 0xd4, 0xd8, 0x62, 0xfd, 0xec, 0xaa
};
static const u8 dec_key011[] __initconst = {
	0x35, 0x4e, 0xb5, 0x70, 0x50, 0x42, 0x8a, 0x85,
	0xf2, 0xfb, 0xed, 0x7b, 0xd0, 0x9e, 0x97, 0xca,
	0xfa, 0x98, 0x66, 0x63, 0xee, 0x37, 0xcc, 0x52,
	0xfe, 0xd1, 0xdf, 0x95, 0x15, 0x34, 0x29, 0x38
};

static const u8 dec_input012[] __initconst = {
	0x52, 0x34, 0xb3, 0x65, 0x3b, 0xb7, 0xe5, 0xd3,
	0xab, 0x49, 0x17, 0x60, 0xd2, 0x52, 0x56, 0xdf,
	0xdf, 0x34, 0x56, 0x82, 0xe2, 0xbe, 0xe5, 0xe1,
	0x28, 0xd1, 0x4e, 0x5f, 0x4f, 0x01, 0x7d, 0x3f,
	0x99, 0x6b, 0x30, 0x6e, 0x1a, 0x7c, 0x4c, 0x8e,
	0x62, 0x81, 0xae, 0x86, 0x3f, 0x6b, 0xd0, 0xb5,
	0xa9, 0xcf, 0x50, 0xf1, 0x02, 0x12, 0xa0, 0x0b,
	0x24, 0xe9, 0xe6, 0x72, 0x89, 0x2c, 0x52, 0x1b,
	0x34, 0x38, 0xf8, 0x75, 0x5f, 0xa0, 0x74, 0xe2,
	0x99, 0xdd, 0xa6, 0x4b, 0x14, 0x50, 0x4e, 0xf1,
	0xbe, 0xd6, 0x9e, 0xdb, 0xb2, 0x24, 0x27, 0x74,
	0x12, 0x4a, 0x78, 0x78, 0x17, 0xa5, 0x58, 0x8e,
	0x2f, 0xf9, 0xf4, 0x8d, 0xee, 0x03, 0x88, 0xae,
	0xb8, 0x29, 0xa1, 0x2f, 0x4b, 0xee, 0x92, 0xbd,
	0x87, 0xb3, 0xce, 0x34, 0x21, 0x57, 0x46, 0x04,
	0x49, 0x0c, 0x80, 0xf2, 0x01, 0x13, 0xa1, 0x55,
	0xb3, 0xff, 0x44, 0x30, 0x3c, 0x1c, 0xd0, 0xef,
	0xbc, 0x18, 0x74, 0x26, 0xad, 0x41, 0x5b, 0x5b,
	0x3e, 0x9a, 0x7a, 0x46, 0x4f, 0x16, 0xd6, 0x74,
	0x5a, 0xb7, 0x3a, 0x28, 0x31, 0xd8, 0xae, 0x26,
	0xac, 0x50, 0x53, 0x86, 0xf2, 0x56, 0xd7, 0x3f,
	0x29, 0xbc, 0x45, 0x68, 0x8e, 0xcb, 0x98, 0x64,
	0xdd, 0xc9, 0xba, 0xb8, 0x4b, 0x7b, 0x82, 0xdd,
	0x14, 0xa7, 0xcb, 0x71, 0x72, 0x00, 0x5c, 0xad,
	0x7b, 0x6a, 0x89, 0xa4, 0x3d, 0xbf, 0xb5, 0x4b,
	0x3e, 0x7c, 0x5a, 0xcf, 0xb8, 0xa1, 0xc5, 0x6e,
	0xc8, 0xb6, 0x31, 0x57, 0x7b, 0xdf, 0xa5, 0x7e,
	0xb1, 0xd6, 0x42, 0x2a, 0x31, 0x36, 0xd1, 0xd0,
	0x3f, 0x7a, 0xe5, 0x94, 0xd6, 0x36, 0xa0, 0x6f,
	0xb7, 0x40, 0x7d, 0x37, 0xc6, 0x55, 0x7c, 0x50,
	0x40, 0x6d, 0x29, 0x89, 0xe3, 0x5a, 0xae, 0x97,
	0xe7, 0x44, 0x49, 0x6e, 0xbd, 0x81, 0x3d, 0x03,
	0x93, 0x06, 0x12, 0x06, 0xe2, 0x41, 0x12, 0x4a,
	0xf1, 0x6a, 0xa4, 0x58, 0xa2, 0xfb, 0xd2, 0x15,
	0xba, 0xc9, 0x79, 0xc9, 0xce, 0x5e, 0x13, 0xbb,
	0xf1, 0x09, 0x04, 0xcc, 0xfd, 0xe8, 0x51, 0x34,
	0x6a, 0xe8, 0x61, 0x88, 0xda, 0xed, 0x01, 0x47,
	0x84, 0xf5, 0x73, 0x25, 0xf9, 0x1c, 0x42, 0x86,
	0x07, 0xf3, 0x5b, 0x1a, 0x01, 0xb3, 0xeb, 0x24,
	0x32, 0x8d, 0xf6, 0xed, 0x7c, 0x4b, 0xeb, 0x3c,
	0x36, 0x42, 0x28, 0xdf, 0xdf, 0xb6, 0xbe, 0xd9,
	0x8c, 0x52, 0xd3, 0x2b, 0x08, 0x90, 0x8c, 0xe7,
	0x98, 0x31, 0xe2, 0x32, 0x8e, 0xfc, 0x11, 0x48,
	0x00, 0xa8, 0x6a, 0x42, 0x4a, 0x02, 0xc6, 0x4b,
	0x09, 0xf1, 0xe3, 0x49, 0xf3, 0x45, 0x1f, 0x0e,
	0xbc, 0x56, 0xe2, 0xe4, 0xdf, 0xfb, 0xeb, 0x61,
	0xfa, 0x24, 0xc1, 0x63, 0x75, 0xbb, 0x47, 0x75,
	0xaf, 0xe1, 0x53, 0x16, 0x96, 0x21, 0x85, 0x26,
	0x11, 0xb3, 0x76, 0xe3, 0x23, 0xa1, 0x6b, 0x74,
	0x37, 0xd0, 0xde, 0x06, 0x90, 0x71, 0x5d, 0x43,
	0x88, 0x9b, 0x00, 0x54, 0xa6, 0x75, 0x2f, 0xa1,
	0xc2, 0x0b, 0x73, 0x20, 0x1d, 0xb6, 0x21, 0x79,
	0x57, 0x3f, 0xfa, 0x09, 0xbe, 0x8a, 0x33, 0xc3,
	0x52, 0xf0, 0x1d, 0x82, 0x31, 0xd1, 0x55, 0xb5,
	0x6c, 0x99, 0x25, 0xcf, 0x5c, 0x32, 0xce, 0xe9,
	0x0d, 0xfa, 0x69, 0x2c, 0xd5, 0x0d, 0xc5, 0x6d,
	0x86, 0xd0, 0x0c, 0x3b, 0x06, 0x50, 0x79, 0xe8,
	0xc3, 0xae, 0x04, 0xe6, 0xcd, 0x51, 0xe4, 0x26,
	0x9b, 0x4f, 0x7e, 0xa6, 0x0f, 0xab, 0xd8, 0xe5,
	0xde, 0xa9, 0x00, 0x95, 0xbe, 0xa3, 0x9d, 0x5d,
	0xb2, 0x09, 0x70, 0x18, 0x1c, 0xf0, 0xac, 0x29,
	0x23, 0x02, 0x29, 0x28, 0xd2, 0x74, 0x35, 0x57,
	0x62, 0x0f, 0x24, 0xea, 0x5e, 0x33, 0xc2, 0x92,
	0xf3, 0x78, 0x4d, 0x30, 0x1e, 0xa1, 0x99, 0xa9,
	0x82, 0xb0, 0x42, 0x31, 0x8d, 0xad, 0x8a, 0xbc,
	0xfc, 0xd4, 0x57, 0x47, 0x3e, 0xb4, 0x50, 0xdd,
	0x6e, 0x2c, 0x80, 0x4d, 0x22, 0xf1, 0xfb, 0x57,
	0xc4, 0xdd, 0x17, 0xe1, 0x8a, 0x36, 0x4a, 0xb3,
	0x37, 0xca, 0xc9, 0x4e, 0xab, 0xd5, 0x69, 0xc4,
	0xf4, 0xbc, 0x0b, 0x3b, 0x44, 0x4b, 0x29, 0x9c,
	0xee, 0xd4, 0x35, 0x22, 0x21, 0xb0, 0x1f, 0x27,
	0x64, 0xa8, 0x51, 0x1b, 0xf0, 0x9f, 0x19, 0x5c,
	0xfb, 0x5a, 0x64, 0x74, 0x70, 0x45, 0x09, 0xf5,
	0x64, 0xfe, 0x1a, 0x2d, 0xc9, 0x14, 0x04, 0x14,
	0xcf, 0xd5, 0x7d, 0x60, 0xaf, 0x94, 0x39, 0x94,
	0xe2, 0x7d, 0x79, 0x82, 0xd0, 0x65, 0x3b, 0x6b,
	0x9c, 0x19, 0x84, 0xb4, 0x6d, 0xb3, 0x0c, 0x99,
	0xc0, 0x56, 0xa8, 0xbd, 0x73, 0xce, 0x05, 0x84,
	0x3e, 0x30, 0xaa, 0xc4, 0x9b, 0x1b, 0x04, 0x2a,
	0x9f, 0xd7, 0x43, 0x2b, 0x23, 0xdf, 0xbf, 0xaa,
	0xd5, 0xc2, 0x43, 0x2d, 0x70, 0xab, 0xdc, 0x75,
	0xad, 0xac, 0xf7, 0xc0, 0xbe, 0x67, 0xb2, 0x74,
	0xed, 0x67, 0x10, 0x4a, 0x92, 0x60, 0xc1, 0x40,
	0x50, 0x19, 0x8a, 0x8a, 0x8c, 0x09, 0x0e, 0x72,
	0xe1, 0x73, 0x5e, 0xe8, 0x41, 0x85, 0x63, 0x9f,
	0x3f, 0xd7, 0x7d, 0xc4, 0xfb, 0x22, 0x5d, 0x92,
	0x6c, 0xb3, 0x1e, 0xe2, 0x50, 0x2f, 0x82, 0xa8,
	0x28, 0xc0, 0xb5, 0xd7, 0x5f, 0x68, 0x0d, 0x2c,
	0x2d, 0xaf, 0x7e, 0xfa, 0x2e, 0x08, 0x0f, 0x1f,
	0x70, 0x9f, 0xe9, 0x19, 0x72, 0x55, 0xf8, 0xfb,
	0x51, 0xd2, 0x33, 0x5d, 0xa0, 0xd3, 0x2b, 0x0a,
	0x6c, 0xbc, 0x4e, 0xcf, 0x36, 0x4d, 0xdc, 0x3b,
	0xe9, 0x3e, 0x81, 0x7c, 0x61, 0xdb, 0x20, 0x2d,
	0x3a, 0xc3, 0xb3, 0x0c, 0x1e, 0x00, 0xb9, 0x7c,
	0xf5, 0xca, 0x10, 0x5f, 0x3a, 0x71, 0xb3, 0xe4,
	0x20, 0xdb, 0x0c, 0x2a, 0x98, 0x63, 0x45, 0x00,
	0x58, 0xf6, 0x68, 0xe4, 0x0b, 0xda, 0x13, 0x3b,
	0x60, 0x5c, 0x76, 0xdb, 0xb9, 0x97, 0x71, 0xe4,
	0xd9, 0xb7, 0xdb, 0xbd, 0x68, 0xc7, 0x84, 0x84,
	0xaa, 0x7c, 0x68, 0x62, 0x5e, 0x16, 0xfc, 0xba,
	0x72, 0xaa, 0x9a, 0xa9, 0xeb, 0x7c, 0x75, 0x47,
	0x97, 0x7e, 0xad, 0xe2, 0xd9, 0x91, 0xe8, 0xe4,
	0xa5, 0x31, 0xd7, 0x01, 0x8e, 0xa2, 0x11, 0x88,
	0x95, 0xb9, 0xf2, 0x9b, 0xd3, 0x7f, 0x1b, 0x81,
	0x22, 0xf7, 0x98, 0x60, 0x0a, 0x64, 0xa6, 0xc1,
	0xf6, 0x49, 0xc7, 0xe3, 0x07, 0x4d, 0x94, 0x7a,
	0xcf, 0x6e, 0x68, 0x0c, 0x1b, 0x3f, 0x6e, 0x2e,
	0xee, 0x92, 0xfa, 0x52, 0xb3, 0x59, 0xf8, 0xf1,
	0x8f, 0x6a, 0x66, 0xa3, 0x82, 0x76, 0x4a, 0x07,
	0x1a, 0xc7, 0xdd, 0xf5, 0xda, 0x9c, 0x3c, 0x24,
	0xbf, 0xfd, 0x42, 0xa1, 0x10, 0x64, 0x6a, 0x0f,
	0x89, 0xee, 0x36, 0xa5, 0xce, 0x99, 0x48, 0x6a,
	0xf0, 0x9f, 0x9e, 0x69, 0xa4, 0x40, 0x20, 0xe9,
	0x16, 0x15, 0xf7, 0xdb, 0x75, 0x02, 0xcb, 0xe9,
	0x73, 0x8b, 0x3b, 0x49, 0x2f, 0xf0, 0xaf, 0x51,
	0x06, 0x5c, 0xdf, 0x27, 0x27, 0x49, 0x6a, 0xd1,
	0xcc, 0xc7, 0xb5, 0x63, 0xb5, 0xfc, 0xb8, 0x5c,
	0x87, 0x7f, 0x84, 0xb4, 0xcc, 0x14, 0xa9, 0x53,
	0xda, 0xa4, 0x56, 0xf8, 0xb6, 0x1b, 0xcc, 0x40,
	0x27, 0x52, 0x06, 0x5a, 0x13, 0x81, 0xd7, 0x3a,
	0xd4, 0x3b, 0xfb, 0x49, 0x65, 0x31, 0x33, 0xb2,
	0xfa, 0xcd, 0xad, 0x58, 0x4e, 0x2b, 0xae, 0xd2,
	0x20, 0xfb, 0x1a, 0x48, 0xb4, 0x3f, 0x9a, 0xd8,
	0x7a, 0x35, 0x4a, 0xc8, 0xee, 0x88, 0x5e, 0x07,
	0x66, 0x54, 0xb9, 0xec, 0x9f, 0xa3, 0xe3, 0xb9,
	0x37, 0xaa, 0x49, 0x76, 0x31, 0xda, 0x74, 0x2d,
	0x3c, 0xa4, 0x65, 0x10, 0x32, 0x38, 0xf0, 0xde,
	0xd3, 0x99, 0x17, 0xaa, 0x71, 0xaa, 0x8f, 0x0f,
	0x8c, 0xaf, 0xa2, 0xf8, 0x5d, 0x64, 0xba, 0x1d,
	0xa3, 0xef, 0x96, 0x73, 0xe8, 0xa1, 0x02, 0x8d,
	0x0c, 0x6d, 0xb8, 0x06, 0x90, 0xb8, 0x08, 0x56,
	0x2c, 0xa7, 0x06, 0xc9, 0xc2, 0x38, 0xdb, 0x7c,
	0x63, 0xb1, 0x57, 0x8e, 0xea, 0x7c, 0x79, 0xf3,
	0x49, 0x1d, 0xfe, 0x9f, 0xf3, 0x6e, 0xb1, 0x1d,
	0xba, 0x19, 0x80, 0x1a, 0x0a, 0xd3, 0xb0, 0x26,
	0x21, 0x40, 0xb1, 0x7c, 0xf9, 0x4d, 0x8d, 0x10,
	0xc1, 0x7e, 0xf4, 0xf6, 0x3c, 0xa8, 0xfd, 0x7c,
	0xa3, 0x92, 0xb2, 0x0f, 0xaa, 0xcc, 0xa6, 0x11,
	0xfe, 0x04, 0xe3, 0xd1, 0x7a, 0x32, 0x89, 0xdf,
	0x0d, 0xc4, 0x8f, 0x79, 0x6b, 0xca, 0x16, 0x7c,
	0x6e, 0xf9, 0xad, 0x0f, 0xf6, 0xfe, 0x27, 0xdb,
	0xc4, 0x13, 0x70, 0xf1, 0x62, 0x1a, 0x4f, 0x79,
	0x40, 0xc9, 0x9b, 0x8b, 0x21, 0xea, 0x84, 0xfa,
	0xf5, 0xf1, 0x89, 0xce, 0xb7, 0x55, 0x0a, 0x80,
	0x39, 0x2f, 0x55, 0x36, 0x16, 0x9c, 0x7b, 0x08,
	0xbd, 0x87, 0x0d, 0xa5, 0x32, 0xf1, 0x52, 0x7c,
	0xe8, 0x55, 0x60, 0x5b, 0xd7, 0x69, 0xe4, 0xfc,
	0xfa, 0x12, 0x85, 0x96, 0xea, 0x50, 0x28, 0xab,
	0x8a, 0xf7, 0xbb, 0x0e, 0x53, 0x74, 0xca, 0xa6,
	0x27, 0x09, 0xc2, 0xb5, 0xde, 0x18, 0x14, 0xd9,
	0xea, 0xe5, 0x29, 0x1c, 0x40, 0x56, 0xcf, 0xd7,
	0xae, 0x05, 0x3f, 0x65, 0xaf, 0x05, 0x73, 0xe2,
	0x35, 0x96, 0x27, 0x07, 0x14, 0xc0, 0xad, 0x33,
	0xf1, 0xdc, 0x44, 0x7a, 0x89, 0x17, 0x77, 0xd2,
	0x9c, 0x58, 0x60, 0xf0, 0x3f, 0x7b, 0x2d, 0x2e,
	0x57, 0x95, 0x54, 0x87, 0xed, 0xf2, 0xc7, 0x4c,
	0xf0, 0xae, 0x56, 0x29, 0x19, 0x7d, 0x66, 0x4b,
	0x9b, 0x83, 0x84, 0x42, 0x3b, 0x01, 0x25, 0x66,
	0x8e, 0x02, 0xde, 0xb9, 0x83, 0x54, 0x19, 0xf6,
	0x9f, 0x79, 0x0d, 0x67, 0xc5, 0x1d, 0x7a, 0x44,
	0x02, 0x98, 0xa7, 0x16, 0x1c, 0x29, 0x0d, 0x74,
	0xff, 0x85, 0x40, 0x06, 0xef, 0x2c, 0xa9, 0xc6,
	0xf5, 0x53, 0x07, 0x06, 0xae, 0xe4, 0xfa, 0x5f,
	0xd8, 0x39, 0x4d, 0xf1, 0x9b, 0x6b, 0xd9, 0x24,
	0x84, 0xfe, 0x03, 0x4c, 0xb2, 0x3f, 0xdf, 0xa1,
	0x05, 0x9e, 0x50, 0x14, 0x5a, 0xd9, 0x1a, 0xa2,
	0xa7, 0xfa, 0xfa, 0x17, 0xf7, 0x78, 0xd6, 0xb5,
	0x92, 0x61, 0x91, 0xac, 0x36, 0xfa, 0x56, 0x0d,
	0x38, 0x32, 0x18, 0x85, 0x08, 0x58, 0x37, 0xf0,
	0x4b, 0xdb, 0x59, 0xe7, 0xa4, 0x34, 0xc0, 0x1b,
	0x01, 0xaf, 0x2d, 0xde, 0xa1, 0xaa, 0x5d, 0xd3,
	0xec, 0xe1, 0xd4, 0xf7, 0xe6, 0x54, 0x68, 0xf0,
	0x51, 0x97, 0xa7, 0x89, 0xea, 0x24, 0xad, 0xd3,
	0x6e, 0x47, 0x93, 0x8b, 0x4b, 0xb4, 0xf7, 0x1c,
	0x42, 0x06, 0x67, 0xe8, 0x99, 0xf6, 0xf5, 0x7b,
	0x85, 0xb5, 0x65, 0xb5, 0xb5, 0xd2, 0x37, 0xf5,
	0xf3, 0x02, 0xa6, 0x4d, 0x11, 0xa7, 0xdc, 0x51,
	0x09, 0x7f, 0xa0, 0xd8, 0x88, 0x1c, 0x13, 0x71,
	0xae, 0x9c, 0xb7, 0x7b, 0x34, 0xd6, 0x4e, 0x68,
	0x26, 0x83, 0x51, 0xaf, 0x1d, 0xee, 0x8b, 0xbb,
	0x69, 0x43, 0x2b, 0x9e, 0x8a, 0xbc, 0x02, 0x0e,
	0xa0, 0x1b, 0xe0, 0xa8, 0x5f, 0x6f, 0xaf, 0x1b,
	0x8f, 0xe7, 0x64, 0x71, 0x74, 0x11, 0x7e, 0xa8,
	0xd8, 0xf9, 0x97, 0x06, 0xc3, 0xb6, 0xfb, 0xfb,
	0xb7, 0x3d, 0x35, 0x9d, 0x3b, 0x52, 0xed, 0x54,
	0xca, 0xf4, 0x81, 0x01, 0x2d, 0x1b, 0xc3, 0xa7,
	0x00, 0x3d, 0x1a, 0x39, 0x54, 0xe1, 0xf6, 0xff,
	0xed, 0x6f, 0x0b, 0x5a, 0x68, 0xda, 0x58, 0xdd,
	0xa9, 0xcf, 0x5c, 0x4a, 0xe5, 0x09, 0x4e, 0xde,
	0x9d, 0xbc, 0x3e, 0xee, 0x5a, 0x00, 0x3b, 0x2c,
	0x87, 0x10, 0x65, 0x60, 0xdd, 0xd7, 0x56, 0xd1,
	0x4c, 0x64, 0x45, 0xe4, 0x21, 0xec, 0x78, 0xf8,
	0x25, 0x7a, 0x3e, 0x16, 0x5d, 0x09, 0x53, 0x14,
	0xbe, 0x4f, 0xae, 0x87, 0xd8, 0xd1, 0xaa, 0x3c,
	0xf6, 0x3e, 0xa4, 0x70, 0x8c, 0x5e, 0x70, 0xa4,
	0xb3, 0x6b, 0x66, 0x73, 0xd3, 0xbf, 0x31, 0x06,
	0x19, 0x62, 0x93, 0x15, 0xf2, 0x86, 0xe4, 0x52,
	0x7e, 0x53, 0x4c, 0x12, 0x38, 0xcc, 0x34, 0x7d,
	0x57, 0xf6, 0x42, 0x93, 0x8a, 0xc4, 0xee, 0x5c,
	0x8a, 0xe1, 0x52, 0x8f, 0x56, 0x64, 0xf6, 0xa6,
	0xd1, 0x91, 0x57, 0x70, 0xcd, 0x11, 0x76, 0xf5,
	0x59, 0x60, 0x60, 0x3c, 0xc1, 0xc3, 0x0b, 0x7f,
	0x58, 0x1a, 0x50, 0x91, 0xf1, 0x68, 0x8f, 0x6e,
	0x74, 0x74, 0xa8, 0x51, 0x0b, 0xf7, 0x7a, 0x98,
	0x37, 0xf2, 0x0a, 0x0e, 0xa4, 0x97, 0x04, 0xb8,
	0x9b, 0xfd, 0xa0, 0xea, 0xf7, 0x0d, 0xe1, 0xdb,
	0x03, 0xf0, 0x31, 0x29, 0xf8, 0xdd, 0x6b, 0x8b,
	0x5d, 0xd8, 0x59, 0xa9, 0x29, 0xcf, 0x9a, 0x79,
	0x89, 0x19, 0x63, 0x46, 0x09, 0x79, 0x6a, 0x11,
	0xda, 0x63, 0x68, 0x48, 0x77, 0x23, 0xfb, 0x7d,
	0x3a, 0x43, 0xcb, 0x02, 0x3b, 0x7a, 0x6d, 0x10,
	0x2a, 0x9e, 0xac, 0xf1, 0xd4, 0x19, 0xf8, 0x23,
	0x64, 0x1d, 0x2c, 0x5f, 0xf2, 0xb0, 0x5c, 0x23,
	0x27, 0xf7, 0x27, 0x30, 0x16, 0x37, 0xb1, 0x90,
	0xab, 0x38, 0xfb, 0x55, 0xcd, 0x78, 0x58, 0xd4,
	0x7d, 0x43, 0xf6, 0x45, 0x5e, 0x55, 0x8d, 0xb1,
	0x02, 0x65, 0x58, 0xb4, 0x13, 0x4b, 0x36, 0xf7,
	0xcc, 0xfe, 0x3d, 0x0b, 0x82, 0xe2, 0x12, 0x11,
	0xbb, 0xe6, 0xb8, 0x3a, 0x48, 0x71, 0xc7, 0x50,
	0x06, 0x16, 0x3a, 0xe6, 0x7c, 0x05, 0xc7, 0xc8,
	0x4d, 0x2f, 0x08, 0x6a, 0x17, 0x9a, 0x95, 0x97,
	0x50, 0x68, 0xdc, 0x28, 0x18, 0xc4, 0x61, 0x38,
	0xb9, 0xe0, 0x3e, 0x78, 0xdb, 0x29, 0xe0, 0x9f,
	0x52, 0xdd, 0xf8, 0x4f, 0x91, 0xc1, 0xd0, 0x33,
	0xa1, 0x7a, 0x8e, 0x30, 0x13, 0x82, 0x07, 0x9f,
	0xd3, 0x31, 0x0f, 0x23, 0xbe, 0x32, 0x5a, 0x75,
	0xcf, 0x96, 0xb2, 0xec, 0xb5, 0x32, 0xac, 0x21,
	0xd1, 0x82, 0x33, 0xd3, 0x15, 0x74, 0xbd, 0x90,
	0xf1, 0x2c, 0xe6, 0x5f, 0x8d, 0xe3, 0x02, 0xe8,
	0xe9, 0xc4, 0xca, 0x96, 0xeb, 0x0e, 0xbc, 0x91,
	0xf4, 0xb9, 0xea, 0xd9, 0x1b, 0x75, 0xbd, 0xe1,
	0xac, 0x2a, 0x05, 0x37, 0x52, 0x9b, 0x1b, 0x3f,
	0x5a, 0xdc, 0x21, 0xc3, 0x98, 0xbb, 0xaf, 0xa3,
	0xf2, 0x00, 0xbf, 0x0d, 0x30, 0x89, 0x05, 0xcc,
	0xa5, 0x76, 0xf5, 0x06, 0xf0, 0xc6, 0x54, 0x8a,
	0x5d, 0xd4, 0x1e, 0xc1, 0xf2, 0xce, 0xb0, 0x62,
	0xc8, 0xfc, 0x59, 0x42, 0x9a, 0x90, 0x60, 0x55,
	0xfe, 0x88, 0xa5, 0x8b, 0xb8, 0x33, 0x0c, 0x23,
	0x24, 0x0d, 0x15, 0x70, 0x37, 0x1e, 0x3d, 0xf6,
	0xd2, 0xea, 0x92, 0x10, 0xb2, 0xc4, 0x51, 0xac,
	0xf2, 0xac, 0xf3, 0x6b, 0x6c, 0xaa, 0xcf, 0x12,
	0xc5, 0x6c, 0x90, 0x50, 0xb5, 0x0c, 0xfc, 0x1a,
	0x15, 0x52, 0xe9, 0x26, 0xc6, 0x52, 0xa4, 0xe7,
	0x81, 0x69, 0xe1, 0xe7, 0x9e, 0x30, 0x01, 0xec,
	0x84, 0x89, 0xb2, 0x0d, 0x66, 0xdd, 0xce, 0x28,
	0x5c, 0xec, 0x98, 0x46, 0x68, 0x21, 0x9f, 0x88,
	0x3f, 0x1f, 0x42, 0x77, 0xce, 0xd0, 0x61, 0xd4,
	0x20, 0xa7, 0xff, 0x53, 0xad, 0x37, 0xd0, 0x17,
	0x35, 0xc9, 0xfc, 0xba, 0x0a, 0x78, 0x3f, 0xf2,
	0xcc, 0x86, 0x89, 0xe8, 0x4b, 0x3c, 0x48, 0x33,
	0x09, 0x7f, 0xc6, 0xc0, 0xdd, 0xb8, 0xfd, 0x7a,
	0x66, 0x66, 0x65, 0xeb, 0x47, 0xa7, 0x04, 0x28,
	0xa3, 0x19, 0x8e, 0xa9, 0xb1, 0x13, 0x67, 0x62,
	0x70, 0xcf, 0xd6
};
static const u8 dec_output012[] __initconst = {
	0x74, 0xa6, 0x3e, 0xe4, 0xb1, 0xcb, 0xaf, 0xb0,
	0x40, 0xe5, 0x0f, 0x9e, 0xf1, 0xf2, 0x89, 0xb5,
	0x42, 0x34, 0x8a, 0xa1, 0x03, 0xb7, 0xe9, 0x57,
	0x46, 0xbe, 0x20, 0xe4, 0x6e, 0xb0, 0xeb, 0xff,
	0xea, 0x07, 0x7e, 0xef, 0xe2, 0x55, 0x9f, 0xe5,
	0x78, 0x3a, 0xb7, 0x83, 0xc2, 0x18, 0x40, 0x7b,
	0xeb, 0xcd, 0x81, 0xfb, 0x90, 0x12, 0x9e, 0x46,
	0xa9, 0xd6, 0x4a, 0xba, 0xb0, 0x62, 0xdb, 0x6b,
	0x99, 0xc4, 0xdb, 0x54, 0x4b, 0xb8, 0xa5, 0x71,
	0xcb, 0xcd, 0x63, 0x32, 0x55, 0xfb, 0x31, 0xf0,
	0x38, 0xf5, 0xbe, 0x78, 0xe4, 0x45, 0xce, 0x1b,
	0x6a, 0x5b, 0x0e, 0xf4, 0x16, 0xe4, 0xb1, 0x3d,
	0xf6, 0x63, 0x7b, 0xa7, 0x0c, 0xde, 0x6f, 0x8f,
	0x74, 0xdf, 0xe0, 0x1e, 0x9d, 0xce, 0x8f, 0x24,
	0xef, 0x23, 0x35, 0x33, 0x7b, 0x83, 0x34, 0x23,
	0x58, 0x74, 0x14, 0x77, 0x1f, 0xc2, 0x4f, 0x4e,
	0xc6, 0x89, 0xf9, 0x52, 0x09, 0x37, 0x64, 0x14,
	0xc4, 0x01, 0x6b, 0x9d, 0x77, 0xe8, 0x90, 0x5d,
	0xa8, 0x4a, 0x2a, 0xef, 0x5c, 0x7f, 0xeb, 0xbb,
	0xb2, 0xc6, 0x93, 0x99, 0x66, 0xdc, 0x7f, 0xd4,
	0x9e, 0x2a, 0xca, 0x8d, 0xdb, 0xe7, 0x20, 0xcf,
	0xe4, 0x73, 0xae, 0x49, 0x7d, 0x64, 0x0f, 0x0e,
	0x28, 0x46, 0xa9, 0xa8, 0x32, 0xe4, 0x0e, 0xf6,
	0x51, 0x53, 0xb8, 0x3c, 0xb1, 0xff, 0xa3, 0x33,
	0x41, 0x75, 0xff, 0xf1, 0x6f, 0xf1, 0xfb, 0xbb,
	0x83, 0x7f, 0x06, 0x9b, 0xe7, 0x1b, 0x0a, 0xe0,
	0x5c, 0x33, 0x60, 0x5b, 0xdb, 0x5b, 0xed, 0xfe,
	0xa5, 0x16, 0x19, 0x72, 0xa3, 0x64, 0x23, 0x00,
	0x02, 0xc7, 0xf3, 0x6a, 0x81, 0x3e, 0x44, 0x1d,
	0x79, 0x15, 0x5f, 0x9a, 0xde, 0xe2, 0xfd, 0x1b,
	0x73, 0xc1, 0xbc, 0x23, 0xba, 0x31, 0xd2, 0x50,
	0xd5, 0xad, 0x7f, 0x74, 0xa7, 0xc9, 0xf8, 0x3e,
	0x2b, 0x26, 0x10, 0xf6, 0x03, 0x36, 0x74, 0xe4,
	0x0e, 0x6a, 0x72, 0xb7, 0x73, 0x0a, 0x42, 0x28,
	0xc2, 0xad, 0x5e, 0x03, 0xbe, 0xb8, 0x0b, 0xa8,
	0x5b, 0xd4, 0xb8, 0xba, 0x52, 0x89, 0xb1, 0x9b,
	0xc1, 0xc3, 0x65, 0x87, 0xed, 0xa5, 0xf4, 0x86,
	0xfd, 0x41, 0x80, 0x91, 0x27, 0x59, 0x53, 0x67,
	0x15, 0x78, 0x54, 0x8b, 0x2d, 0x3d, 0xc7, 0xff,
	0x02, 0x92, 0x07, 0x5f, 0x7a, 0x4b, 0x60, 0x59,
	0x3c, 0x6f, 0x5c, 0xd8, 0xec, 0x95, 0xd2, 0xfe,
	0xa0, 0x3b, 0xd8, 0x3f, 0xd1, 0x69, 0xa6, 0xd6,
	0x41, 0xb2, 0xf4, 0x4d, 0x12, 0xf4, 0x58, 0x3e,
	0x66, 0x64, 0x80, 0x31, 0x9b, 0xa8, 0x4c, 0x8b,
	0x07, 0xb2, 0xec, 0x66, 0x94, 0x66, 0x47, 0x50,
	0x50, 0x5f, 0x18, 0x0b, 0x0e, 0xd6, 0xc0, 0x39,
	0x21, 0x13, 0x9e, 0x33, 0xbc, 0x79, 0x36, 0x02,
	0x96, 0x70, 0xf0, 0x48, 0x67, 0x2f, 0x26, 0xe9,
	0x6d, 0x10, 0xbb, 0xd6, 0x3f, 0xd1, 0x64, 0x7a,
	0x2e, 0xbe, 0x0c, 0x61, 0xf0, 0x75, 0x42, 0x38,
	0x23, 0xb1, 0x9e, 0x9f, 0x7c, 0x67, 0x66, 0xd9,
	0x58, 0x9a, 0xf1, 0xbb, 0x41, 0x2a, 0x8d, 0x65,
	0x84, 0x94, 0xfc, 0xdc, 0x6a, 0x50, 0x64, 0xdb,
	0x56, 0x33, 0x76, 0x00, 0x10, 0xed, 0xbe, 0xd2,
	0x12, 0xf6, 0xf6, 0x1b, 0xa2, 0x16, 0xde, 0xae,
	0x31, 0x95, 0xdd, 0xb1, 0x08, 0x7e, 0x4e, 0xee,
	0xe7, 0xf9, 0xa5, 0xfb, 0x5b, 0x61, 0x43, 0x00,
	0x40, 0xf6, 0x7e, 0x02, 0x04, 0x32, 0x4e, 0x0c,
	0xe2, 0x66, 0x0d, 0xd7, 0x07, 0x98, 0x0e, 0xf8,
	0x72, 0x34, 0x6d, 0x95, 0x86, 0xd7, 0xcb, 0x31,
	0x54, 0x47, 0xd0, 0x38, 0x29, 0x9c, 0x5a, 0x68,
	0xd4, 0x87, 0x76, 0xc9, 0xe7, 0x7e, 0xe3, 0xf4,
	0x81, 0x6d, 0x18, 0xcb, 0xc9, 0x05, 0xaf, 0xa0,
	0xfb, 0x66, 0xf7, 0xf1, 0x1c, 0xc6, 0x14, 0x11,
	0x4f, 0x2b, 0x79, 0x42, 0x8b, 0xbc, 0xac, 0xe7,
	0x6c, 0xfe, 0x0f, 0x58, 0xe7, 0x7c, 0x78, 0x39,
	0x30, 0xb0, 0x66, 0x2c, 0x9b, 0x6d, 0x3a, 0xe1,
	0xcf, 0xc9, 0xa4, 0x0e, 0x6d, 0x6d, 0x8a, 0xa1,
	0x3a, 0xe7, 0x28, 0xd4, 0x78, 0x4c, 0xa6, 0xa2,
	0x2a, 0xa6, 0x03, 0x30, 0xd7, 0xa8, 0x25, 0x66,
	0x87, 0x2f, 0x69, 0x5c, 0x4e, 0xdd, 0xa5, 0x49,
	0x5d, 0x37, 0x4a, 0x59, 0xc4, 0xaf, 0x1f, 0xa2,
	0xe4, 0xf8, 0xa6, 0x12, 0x97, 0xd5, 0x79, 0xf5,
	0xe2, 0x4a, 0x2b, 0x5f, 0x61, 0xe4, 0x9e, 0xe3,
	0xee, 0xb8, 0xa7, 0x5b, 0x2f, 0xf4, 0x9e, 0x6c,
	0xfb, 0xd1, 0xc6, 0x56, 0x77, 0xba, 0x75, 0xaa,
	0x3d, 0x1a, 0xa8, 0x0b, 0xb3, 0x68, 0x24, 0x00,
	0x10, 0x7f, 0xfd, 0xd7, 0xa1, 0x8d, 0x83, 0x54,
	0x4f, 0x1f, 0xd8, 0x2a, 0xbe, 0x8a, 0x0c, 0x87,
	0xab, 0xa2, 0xde, 0xc3, 0x39, 0xbf, 0x09, 0x03,
	0xa5, 0xf3, 0x05, 0x28, 0xe1, 0xe1, 0xee, 0x39,
	0x70, 0x9c, 0xd8, 0x81, 0x12, 0x1e, 0x02, 0x40,
	0xd2, 0x6e, 0xf0, 0xeb, 0x1b, 0x3d, 0x22, 0xc6,
	0xe5, 0xe3, 0xb4, 0x5a, 0x98, 0xbb, 0xf0, 0x22,
	0x28, 0x8d, 0xe5, 0xd3, 0x16, 0x48, 0x24, 0xa5,
	0xe6, 0x66, 0x0c, 0xf9, 0x08, 0xf9, 0x7e, 0x1e,
	0xe1, 0x28, 0x26, 0x22, 0xc7, 0xc7, 0x0a, 0x32,
	0x47, 0xfa, 0xa3, 0xbe, 0x3c, 0xc4, 0xc5, 0x53,
	0x0a, 0xd5, 0x94, 0x4a, 0xd7, 0x93, 0xd8, 0x42,
	0x99, 0xb9, 0x0a, 0xdb, 0x56, 0xf7, 0xb9, 0x1c,
	0x53, 0x4f, 0xfa, 0xd3, 0x74, 0xad, 0xd9, 0x68,
	0xf1, 0x1b, 0xdf, 0x61, 0xc6, 0x5e, 0xa8, 0x48,
	0xfc, 0xd4, 0x4a, 0x4c, 0x3c, 0x32, 0xf7, 0x1c,
	0x96, 0x21, 0x9b, 0xf9, 0xa3, 0xcc, 0x5a, 0xce,
	0xd5, 0xd7, 0x08, 0x24, 0xf6, 0x1c, 0xfd, 0xdd,
	0x38, 0xc2, 0x32, 0xe9, 0xb8, 0xe7, 0xb6, 0xfa,
	0x9d, 0x45, 0x13, 0x2c, 0x83, 0xfd, 0x4a, 0x69,
	0x82, 0xcd, 0xdc, 0xb3, 0x76, 0x0c, 0x9e, 0xd8,
	0xf4, 0x1b, 0x45, 0x15, 0xb4, 0x97, 0xe7, 0x58,
	0x34, 0xe2, 0x03, 0x29, 0x5a, 0xbf, 0xb6, 0xe0,
	0x5d, 0x13, 0xd9, 0x2b, 0xb4, 0x80, 0xb2, 0x45,
	0x81, 0x6a, 0x2e, 0x6c, 0x89, 0x7d, 0xee, 0xbb,
	0x52, 0xdd, 0x1f, 0x18, 0xe7, 0x13, 0x6b, 0x33,
	0x0e, 0xea, 0x36, 0x92, 0x77, 0x7b, 0x6d, 0x9c,
	0x5a, 0x5f, 0x45, 0x7b, 0x7b, 0x35, 0x62, 0x23,
	0xd1, 0xbf, 0x0f, 0xd0, 0x08, 0x1b, 0x2b, 0x80,
	0x6b, 0x7e, 0xf1, 0x21, 0x47, 0xb0, 0x57, 0xd1,
	0x98, 0x72, 0x90, 0x34, 0x1c, 0x20, 0x04, 0xff,
	0x3d, 0x5c, 0xee, 0x0e, 0x57, 0x5f, 0x6f, 0x24,
	0x4e, 0x3c, 0xea, 0xfc, 0xa5, 0xa9, 0x83, 0xc9,
	0x61, 0xb4, 0x51, 0x24, 0xf8, 0x27, 0x5e, 0x46,
	0x8c, 0xb1, 0x53, 0x02, 0x96, 0x35, 0xba, 0xb8,
	0x4c, 0x71, 0xd3, 0x15, 0x59, 0x35, 0x22, 0x20,
	0xad, 0x03, 0x9f, 0x66, 0x44, 0x3b, 0x9c, 0x35,
	0x37, 0x1f, 0x9b, 0xbb, 0xf3, 0xdb, 0x35, 0x63,
	0x30, 0x64, 0xaa, 0xa2, 0x06, 0xa8, 0x5d, 0xbb,
	0xe1, 0x9f, 0x70, 0xec, 0x82, 0x11, 0x06, 0x36,
	0xec, 0x8b, 0x69, 0x66, 0x24, 0x44, 0xc9, 0x4a,
	0x57, 0xbb, 0x9b, 0x78, 0x13, 0xce, 0x9c, 0x0c,
	0xba, 0x92, 0x93, 0x63, 0xb8, 0xe2, 0x95, 0x0f,
	0x0f, 0x16, 0x39, 0x52, 0xfd, 0x3a, 0x6d, 0x02,
	0x4b, 0xdf, 0x13, 0xd3, 0x2a, 0x22, 0xb4, 0x03,
	0x7c, 0x54, 0x49, 0x96, 0x68, 0x54, 0x10, 0xfa,
	0xef, 0xaa, 0x6c, 0xe8, 0x22, 0xdc, 0x71, 0x16,
	0x13, 0x1a, 0xf6, 0x28, 0xe5, 0x6d, 0x77, 0x3d,
	0xcd, 0x30, 0x63, 0xb1, 0x70, 0x52, 0xa1, 0xc5,
	0x94, 0x5f, 0xcf, 0xe8, 0xb8, 0x26, 0x98, 0xf7,
	0x06, 0xa0, 0x0a, 0x70, 0xfa, 0x03, 0x80, 0xac,
	0xc1, 0xec, 0xd6, 0x4c, 0x54, 0xd7, 0xfe, 0x47,
	0xb6, 0x88, 0x4a, 0xf7, 0x71, 0x24, 0xee, 0xf3,
	0xd2, 0xc2, 0x4a, 0x7f, 0xfe, 0x61, 0xc7, 0x35,
	0xc9, 0x37, 0x67, 0xcb, 0x24, 0x35, 0xda, 0x7e,
	0xca, 0x5f, 0xf3, 0x8d, 0xd4, 0x13, 0x8e, 0xd6,
	0xcb, 0x4d, 0x53, 0x8f, 0x53, 0x1f, 0xc0, 0x74,
	0xf7, 0x53, 0xb9, 0x5e, 0x23, 0x37, 0xba, 0x6e,
	0xe3, 0x9d, 0x07, 0x55, 0x25, 0x7b, 0xe6, 0x2a,
	0x64, 0xd1, 0x32, 0xdd, 0x54, 0x1b, 0x4b, 0xc0,
	0xe1, 0xd7, 0x69, 0x58, 0xf8, 0x93, 0x29, 0xc4,
	0xdd, 0x23, 0x2f, 0xa5, 0xfc, 0x9d, 0x7e, 0xf8,
	0xd4, 0x90, 0xcd, 0x82, 0x55, 0xdc, 0x16, 0x16,
	0x9f, 0x07, 0x52, 0x9b, 0x9d, 0x25, 0xed, 0x32,
	0xc5, 0x7b, 0xdf, 0xf6, 0x83, 0x46, 0x3d, 0x65,
	0xb7, 0xef, 0x87, 0x7a, 0x12, 0x69, 0x8f, 0x06,
	0x7c, 0x51, 0x15, 0x4a, 0x08, 0xe8, 0xac, 0x9a,
	0x0c, 0x24, 0xa7, 0x27, 0xd8, 0x46, 0x2f, 0xe7,
	0x01, 0x0e, 0x1c, 0xc6, 0x91, 0xb0, 0x6e, 0x85,
	0x65, 0xf0, 0x29, 0x0d, 0x2e, 0x6b, 0x3b, 0xfb,
	0x4b, 0xdf, 0xe4, 0x80, 0x93, 0x03, 0x66, 0x46,
	0x3e, 0x8a, 0x6e, 0xf3, 0x5e, 0x4d, 0x62, 0x0e,
	0x49, 0x05, 0xaf, 0xd4, 0xf8, 0x21, 0x20, 0x61,
	0x1d, 0x39, 0x17, 0xf4, 0x61, 0x47, 0x95, 0xfb,
	0x15, 0x2e, 0xb3, 0x4f, 0xd0, 0x5d, 0xf5, 0x7d,
	0x40, 0xda, 0x90, 0x3c, 0x6b, 0xcb, 0x17, 0x00,
	0x13, 0x3b, 0x64, 0x34, 0x1b, 0xf0, 0xf2, 0xe5,
	0x3b, 0xb2, 0xc7, 0xd3, 0x5f, 0x3a, 0x44, 0xa6,
	0x9b, 0xb7, 0x78, 0x0e, 0x42, 0x5d, 0x4c, 0xc1,
	0xe9, 0xd2, 0xcb, 0xb7, 0x78, 0xd1, 0xfe, 0x9a,
	0xb5, 0x07, 0xe9, 0xe0, 0xbe, 0xe2, 0x8a, 0xa7,
	0x01, 0x83, 0x00, 0x8c, 0x5c, 0x08, 0xe6, 0x63,
	0x12, 0x92, 0xb7, 0xb7, 0xa6, 0x19, 0x7d, 0x38,
	0x13, 0x38, 0x92, 0x87, 0x24, 0xf9, 0x48, 0xb3,
	0x5e, 0x87, 0x6a, 0x40, 0x39, 0x5c, 0x3f, 0xed,
	0x8f, 0xee, 0xdb, 0x15, 0x82, 0x06, 0xda, 0x49,
	0x21, 0x2b, 0xb5, 0xbf, 0x32, 0x7c, 0x9f, 0x42,
	0x28, 0x63, 0xcf, 0xaf, 0x1e, 0xf8, 0xc6, 0xa0,
	0xd1, 0x02, 0x43, 0x57, 0x62, 0xec, 0x9b, 0x0f,
	0x01, 0x9e, 0x71, 0xd8, 0x87, 0x9d, 0x01, 0xc1,
	0x58, 0x77, 0xd9, 0xaf, 0xb1, 0x10, 0x7e, 0xdd,
	0xa6, 0x50, 0x96, 0xe5, 0xf0, 0x72, 0x00, 0x6d,
	0x4b, 0xf8, 0x2a, 0x8f, 0x19, 0xf3, 0x22, 0x88,
	0x11, 0x4a, 0x8b, 0x7c, 0xfd, 0xb7, 0xed, 0xe1,
	0xf6, 0x40, 0x39, 0xe0, 0xe9, 0xf6, 0x3d, 0x25,
	0xe6, 0x74, 0x3c, 0x58, 0x57, 0x7f, 0xe1, 0x22,
	0x96, 0x47, 0x31, 0x91, 0xba, 0x70, 0x85, 0x28,
	0x6b, 0x9f, 0x6e, 0x25, 0xac, 0x23, 0x66, 0x2f,
	0x29, 0x88, 0x28, 0xce, 0x8c, 0x5c, 0x88, 0x53,
	0xd1, 0x3b, 0xcc, 0x6a, 0x51, 0xb2, 0xe1, 0x28,
	0x3f, 0x91, 0xb4, 0x0d, 0x00, 0x3a, 0xe3, 0xf8,
	0xc3, 0x8f, 0xd7, 0x96, 0x62, 0x0e, 0x2e, 0xfc,
	0xc8, 0x6c, 0x77, 0xa6, 0x1d, 0x22, 0xc1, 0xb8,
	0xe6, 0x61, 0xd7, 0x67, 0x36, 0x13, 0x7b, 0xbb,
	0x9b, 0x59, 0x09, 0xa6, 0xdf, 0xf7, 0x6b, 0xa3,
	0x40, 0x1a, 0xf5, 0x4f, 0xb4, 0xda, 0xd3, 0xf3,
	0x81, 0x93, 0xc6, 0x18, 0xd9, 0x26, 0xee, 0xac,
	0xf0, 0xaa, 0xdf, 0xc5, 0x9c, 0xca, 0xc2, 0xa2,
	0xcc, 0x7b, 0x5c, 0x24, 0xb0, 0xbc, 0xd0, 0x6a,
	0x4d, 0x89, 0x09, 0xb8, 0x07, 0xfe, 0x87, 0xad,
	0x0a, 0xea, 0xb8, 0x42, 0xf9, 0x5e, 0xb3, 0x3e,
	0x36, 0x4c, 0xaf, 0x75, 0x9e, 0x1c, 0xeb, 0xbd,
	0xbc, 0xbb, 0x80, 0x40, 0xa7, 0x3a, 0x30, 0xbf,
	0xa8, 0x44, 0xf4, 0xeb, 0x38, 0xad, 0x29, 0xba,
	0x23, 0xed, 0x41, 0x0c, 0xea, 0xd2, 0xbb, 0x41,
	0x18, 0xd6, 0xb9, 0xba, 0x65, 0x2b, 0xa3, 0x91,
	0x6d, 0x1f, 0xa9, 0xf4, 0xd1, 0x25, 0x8d, 0x4d,
	0x38, 0xff, 0x64, 0xa0, 0xec, 0xde, 0xa6, 0xb6,
	0x79, 0xab, 0x8e, 0x33, 0x6c, 0x47, 0xde, 0xaf,
	0x94, 0xa4, 0xa5, 0x86, 0x77, 0x55, 0x09, 0x92,
	0x81, 0x31, 0x76, 0xc7, 0x34, 0x22, 0x89, 0x8e,
	0x3d, 0x26, 0x26, 0xd7, 0xfc, 0x1e, 0x16, 0x72,
	0x13, 0x33, 0x63, 0xd5, 0x22, 0xbe, 0xb8, 0x04,
	0x34, 0x84, 0x41, 0xbb, 0x80, 0xd0, 0x9f, 0x46,
	0x48, 0x07, 0xa7, 0xfc, 0x2b, 0x3a, 0x75, 0x55,
	0x8c, 0xc7, 0x6a, 0xbd, 0x7e, 0x46, 0x08, 0x84,
	0x0f, 0xd5, 0x74, 0xc0, 0x82, 0x8e, 0xaa, 0x61,
	0x05, 0x01, 0xb2, 0x47, 0x6e, 0x20, 0x6a, 0x2d,
	0x58, 0x70, 0x48, 0x32, 0xa7, 0x37, 0xd2, 0xb8,
	0x82, 0x1a, 0x51, 0xb9, 0x61, 0xdd, 0xfd, 0x9d,
	0x6b, 0x0e, 0x18, 0x97, 0xf8, 0x45, 0x5f, 0x87,
	0x10, 0xcf, 0x34, 0x72, 0x45, 0x26, 0x49, 0x70,
	0xe7, 0xa3, 0x78, 0xe0, 0x52, 0x89, 0x84, 0x94,
	0x83, 0x82, 0xc2, 0x69, 0x8f, 0xe3, 0xe1, 0x3f,
	0x60, 0x74, 0x88, 0xc4, 0xf7, 0x75, 0x2c, 0xfb,
	0xbd, 0xb6, 0xc4, 0x7e, 0x10, 0x0a, 0x6c, 0x90,
	0x04, 0x9e, 0xc3, 0x3f, 0x59, 0x7c, 0xce, 0x31,
	0x18, 0x60, 0x57, 0x73, 0x46, 0x94, 0x7d, 0x06,
	0xa0, 0x6d, 0x44, 0xec, 0xa2, 0x0a, 0x9e, 0x05,
	0x15, 0xef, 0xca, 0x5c, 0xbf, 0x00, 0xeb, 0xf7,
	0x3d, 0x32, 0xd4, 0xa5, 0xef, 0x49, 0x89, 0x5e,
	0x46, 0xb0, 0xa6, 0x63, 0x5b, 0x8a, 0x73, 0xae,
	0x6f, 0xd5, 0x9d, 0xf8, 0x4f, 0x40, 0xb5, 0xb2,
	0x6e, 0xd3, 0xb6, 0x01, 0xa9, 0x26, 0xa2, 0x21,
	0xcf, 0x33, 0x7a, 0x3a, 0xa4, 0x23, 0x13, 0xb0,
	0x69, 0x6a, 0xee, 0xce, 0xd8, 0x9d, 0x01, 0x1d,
	0x50, 0xc1, 0x30, 0x6c, 0xb1, 0xcd, 0xa0, 0xf0,
	0xf0, 0xa2, 0x64, 0x6f, 0xbb, 0xbf, 0x5e, 0xe6,
	0xab, 0x87, 0xb4, 0x0f, 0x4f, 0x15, 0xaf, 0xb5,
	0x25, 0xa1, 0xb2, 0xd0, 0x80, 0x2c, 0xfb, 0xf9,
	0xfe, 0xd2, 0x33, 0xbb, 0x76, 0xfe, 0x7c, 0xa8,
	0x66, 0xf7, 0xe7, 0x85, 0x9f, 0x1f, 0x85, 0x57,
	0x88, 0xe1, 0xe9, 0x63, 0xe4, 0xd8, 0x1c, 0xa1,
	0xfb, 0xda, 0x44, 0x05, 0x2e, 0x1d, 0x3a, 0x1c,
	0xff, 0xc8, 0x3b, 0xc0, 0xfe, 0xda, 0x22, 0x0b,
	0x43, 0xd6, 0x88, 0x39, 0x4c, 0x4a, 0xa6, 0x69,
	0x18, 0x93, 0x42, 0x4e, 0xb5, 0xcc, 0x66, 0x0d,
	0x09, 0xf8, 0x1e, 0x7c, 0xd3, 0x3c, 0x99, 0x0d,
	0x50, 0x1d, 0x62, 0xe9, 0x57, 0x06, 0xbf, 0x19,
	0x88, 0xdd, 0xad, 0x7b, 0x4f, 0xf9, 0xc7, 0x82,
	0x6d, 0x8d, 0xc8, 0xc4, 0xc5, 0x78, 0x17, 0x20,
	0x15, 0xc5, 0x52, 0x41, 0xcf, 0x5b, 0xd6, 0x7f,
	0x94, 0x02, 0x41, 0xe0, 0x40, 0x22, 0x03, 0x5e,
	0xd1, 0x53, 0xd4, 0x86, 0xd3, 0x2c, 0x9f, 0x0f,
	0x96, 0xe3, 0x6b, 0x9a, 0x76, 0x32, 0x06, 0x47,
	0x4b, 0x11, 0xb3, 0xdd, 0x03, 0x65, 0xbd, 0x9b,
	0x01, 0xda, 0x9c, 0xb9, 0x7e, 0x3f, 0x6a, 0xc4,
	0x7b, 0xea, 0xd4, 0x3c, 0xb9, 0xfb, 0x5c, 0x6b,
	0x64, 0x33, 0x52, 0xba, 0x64, 0x78, 0x8f, 0xa4,
	0xaf, 0x7a, 0x61, 0x8d, 0xbc, 0xc5, 0x73, 0xe9,
	0x6b, 0x58, 0x97, 0x4b, 0xbf, 0x63, 0x22, 0xd3,
	0x37, 0x02, 0x54, 0xc5, 0xb9, 0x16, 0x4a, 0xf0,
	0x19, 0xd8, 0x94, 0x57, 0xb8, 0x8a, 0xb3, 0x16,
	0x3b, 0xd0, 0x84, 0x8e, 0x67, 0xa6, 0xa3, 0x7d,
	0x78, 0xec, 0x00
};
static const u8 dec_assoc012[] __initconst = {
	0xb1, 0x69, 0x83, 0x87, 0x30, 0xaa, 0x5d, 0xb8,
	0x77, 0xe8, 0x21, 0xff, 0x06, 0x59, 0x35, 0xce,
	0x75, 0xfe, 0x38, 0xef, 0xb8, 0x91, 0x43, 0x8c,
	0xcf, 0x70, 0xdd, 0x0a, 0x68, 0xbf, 0xd4, 0xbc,
	0x16, 0x76, 0x99, 0x36, 0x1e, 0x58, 0x79, 0x5e,
	0xd4, 0x29, 0xf7, 0x33, 0x93, 0x48, 0xdb, 0x5f,
	0x01, 0xae, 0x9c, 0xb6, 0xe4, 0x88, 0x6d, 0x2b,
	0x76, 0x75, 0xe0, 0xf3, 0x74, 0xe2, 0xc9
};
static const u8 dec_nonce012[] __initconst = {
	0x05, 0xa3, 0x93, 0xed, 0x30, 0xc5, 0xa2, 0x06
};
static const u8 dec_key012[] __initconst = {
	0xb3, 0x35, 0x50, 0x03, 0x54, 0x2e, 0x40, 0x5e,
	0x8f, 0x59, 0x8e, 0xc5, 0x90, 0xd5, 0x27, 0x2d,
	0xba, 0x29, 0x2e, 0xcb, 0x1b, 0x70, 0x44, 0x1e,
	0x65, 0x91, 0x6e, 0x2a, 0x79, 0x22, 0xda, 0x64
};

static const u8 dec_input013[] __initconst = {
	0x52, 0x34, 0xb3, 0x65, 0x3b, 0xb7, 0xe5, 0xd3,
	0xab, 0x49, 0x17, 0x60, 0xd2, 0x52, 0x56, 0xdf,
	0xdf, 0x34, 0x56, 0x82, 0xe2, 0xbe, 0xe5, 0xe1,
	0x28, 0xd1, 0x4e, 0x5f, 0x4f, 0x01, 0x7d, 0x3f,
	0x99, 0x6b, 0x30, 0x6e, 0x1a, 0x7c, 0x4c, 0x8e,
	0x62, 0x81, 0xae, 0x86, 0x3f, 0x6b, 0xd0, 0xb5,
	0xa9, 0xcf, 0x50, 0xf1, 0x02, 0x12, 0xa0, 0x0b,
	0x24, 0xe9, 0xe6, 0x72, 0x89, 0x2c, 0x52, 0x1b,
	0x34, 0x38, 0xf8, 0x75, 0x5f, 0xa0, 0x74, 0xe2,
	0x99, 0xdd, 0xa6, 0x4b, 0x14, 0x50, 0x4e, 0xf1,
	0xbe, 0xd6, 0x9e, 0xdb, 0xb2, 0x24, 0x27, 0x74,
	0x12, 0x4a, 0x78, 0x78, 0x17, 0xa5, 0x58, 0x8e,
	0x2f, 0xf9, 0xf4, 0x8d, 0xee, 0x03, 0x88, 0xae,
	0xb8, 0x29, 0xa1, 0x2f, 0x4b, 0xee, 0x92, 0xbd,
	0x87, 0xb3, 0xce, 0x34, 0x21, 0x57, 0x46, 0x04,
	0x49, 0x0c, 0x80, 0xf2, 0x01, 0x13, 0xa1, 0x55,
	0xb3, 0xff, 0x44, 0x30, 0x3c, 0x1c, 0xd0, 0xef,
	0xbc, 0x18, 0x74, 0x26, 0xad, 0x41, 0x5b, 0x5b,
	0x3e, 0x9a, 0x7a, 0x46, 0x4f, 0x16, 0xd6, 0x74,
	0x5a, 0xb7, 0x3a, 0x28, 0x31, 0xd8, 0xae, 0x26,
	0xac, 0x50, 0x53, 0x86, 0xf2, 0x56, 0xd7, 0x3f,
	0x29, 0xbc, 0x45, 0x68, 0x8e, 0xcb, 0x98, 0x64,
	0xdd, 0xc9, 0xba, 0xb8, 0x4b, 0x7b, 0x82, 0xdd,
	0x14, 0xa7, 0xcb, 0x71, 0x72, 0x00, 0x5c, 0xad,
	0x7b, 0x6a, 0x89, 0xa4, 0x3d, 0xbf, 0xb5, 0x4b,
	0x3e, 0x7c, 0x5a, 0xcf, 0xb8, 0xa1, 0xc5, 0x6e,
	0xc8, 0xb6, 0x31, 0x57, 0x7b, 0xdf, 0xa5, 0x7e,
	0xb1, 0xd6, 0x42, 0x2a, 0x31, 0x36, 0xd1, 0xd0,
	0x3f, 0x7a, 0xe5, 0x94, 0xd6, 0x36, 0xa0, 0x6f,
	0xb7, 0x40, 0x7d, 0x37, 0xc6, 0x55, 0x7c, 0x50,
	0x40, 0x6d, 0x29, 0x89, 0xe3, 0x5a, 0xae, 0x97,
	0xe7, 0x44, 0x49, 0x6e, 0xbd, 0x81, 0x3d, 0x03,
	0x93, 0x06, 0x12, 0x06, 0xe2, 0x41, 0x12, 0x4a,
	0xf1, 0x6a, 0xa4, 0x58, 0xa2, 0xfb, 0xd2, 0x15,
	0xba, 0xc9, 0x79, 0xc9, 0xce, 0x5e, 0x13, 0xbb,
	0xf1, 0x09, 0x04, 0xcc, 0xfd, 0xe8, 0x51, 0x34,
	0x6a, 0xe8, 0x61, 0x88, 0xda, 0xed, 0x01, 0x47,
	0x84, 0xf5, 0x73, 0x25, 0xf9, 0x1c, 0x42, 0x86,
	0x07, 0xf3, 0x5b, 0x1a, 0x01, 0xb3, 0xeb, 0x24,
	0x32, 0x8d, 0xf6, 0xed, 0x7c, 0x4b, 0xeb, 0x3c,
	0x36, 0x42, 0x28, 0xdf, 0xdf, 0xb6, 0xbe, 0xd9,
	0x8c, 0x52, 0xd3, 0x2b, 0x08, 0x90, 0x8c, 0xe7,
	0x98, 0x31, 0xe2, 0x32, 0x8e, 0xfc, 0x11, 0x48,
	0x00, 0xa8, 0x6a, 0x42, 0x4a, 0x02, 0xc6, 0x4b,
	0x09, 0xf1, 0xe3, 0x49, 0xf3, 0x45, 0x1f, 0x0e,
	0xbc, 0x56, 0xe2, 0xe4, 0xdf, 0xfb, 0xeb, 0x61,
	0xfa, 0x24, 0xc1, 0x63, 0x75, 0xbb, 0x47, 0x75,
	0xaf, 0xe1, 0x53, 0x16, 0x96, 0x21, 0x85, 0x26,
	0x11, 0xb3, 0x76, 0xe3, 0x23, 0xa1, 0x6b, 0x74,
	0x37, 0xd0, 0xde, 0x06, 0x90, 0x71, 0x5d, 0x43,
	0x88, 0x9b, 0x00, 0x54, 0xa6, 0x75, 0x2f, 0xa1,
	0xc2, 0x0b, 0x73, 0x20, 0x1d, 0xb6, 0x21, 0x79,
	0x57, 0x3f, 0xfa, 0x09, 0xbe, 0x8a, 0x33, 0xc3,
	0x52, 0xf0, 0x1d, 0x82, 0x31, 0xd1, 0x55, 0xb5,
	0x6c, 0x99, 0x25, 0xcf, 0x5c, 0x32, 0xce, 0xe9,
	0x0d, 0xfa, 0x69, 0x2c, 0xd5, 0x0d, 0xc5, 0x6d,
	0x86, 0xd0, 0x0c, 0x3b, 0x06, 0x50, 0x79, 0xe8,
	0xc3, 0xae, 0x04, 0xe6, 0xcd, 0x51, 0xe4, 0x26,
	0x9b, 0x4f, 0x7e, 0xa6, 0x0f, 0xab, 0xd8, 0xe5,
	0xde, 0xa9, 0x00, 0x95, 0xbe, 0xa3, 0x9d, 0x5d,
	0xb2, 0x09, 0x70, 0x18, 0x1c, 0xf0, 0xac, 0x29,
	0x23, 0x02, 0x29, 0x28, 0xd2, 0x74, 0x35, 0x57,
	0x62, 0x0f, 0x24, 0xea, 0x5e, 0x33, 0xc2, 0x92,
	0xf3, 0x78, 0x4d, 0x30, 0x1e, 0xa1, 0x99, 0xa9,
	0x82, 0xb0, 0x42, 0x31, 0x8d, 0xad, 0x8a, 0xbc,
	0xfc, 0xd4, 0x57, 0x47, 0x3e, 0xb4, 0x50, 0xdd,
	0x6e, 0x2c, 0x80, 0x4d, 0x22, 0xf1, 0xfb, 0x57,
	0xc4, 0xdd, 0x17, 0xe1, 0x8a, 0x36, 0x4a, 0xb3,
	0x37, 0xca, 0xc9, 0x4e, 0xab, 0xd5, 0x69, 0xc4,
	0xf4, 0xbc, 0x0b, 0x3b, 0x44, 0x4b, 0x29, 0x9c,
	0xee, 0xd4, 0x35, 0x22, 0x21, 0xb0, 0x1f, 0x27,
	0x64, 0xa8, 0x51, 0x1b, 0xf0, 0x9f, 0x19, 0x5c,
	0xfb, 0x5a, 0x64, 0x74, 0x70, 0x45, 0x09, 0xf5,
	0x64, 0xfe, 0x1a, 0x2d, 0xc9, 0x14, 0x04, 0x14,
	0xcf, 0xd5, 0x7d, 0x60, 0xaf, 0x94, 0x39, 0x94,
	0xe2, 0x7d, 0x79, 0x82, 0xd0, 0x65, 0x3b, 0x6b,
	0x9c, 0x19, 0x84, 0xb4, 0x6d, 0xb3, 0x0c, 0x99,
	0xc0, 0x56, 0xa8, 0xbd, 0x73, 0xce, 0x05, 0x84,
	0x3e, 0x30, 0xaa, 0xc4, 0x9b, 0x1b, 0x04, 0x2a,
	0x9f, 0xd7, 0x43, 0x2b, 0x23, 0xdf, 0xbf, 0xaa,
	0xd5, 0xc2, 0x43, 0x2d, 0x70, 0xab, 0xdc, 0x75,
	0xad, 0xac, 0xf7, 0xc0, 0xbe, 0x67, 0xb2, 0x74,
	0xed, 0x67, 0x10, 0x4a, 0x92, 0x60, 0xc1, 0x40,
	0x50, 0x19, 0x8a, 0x8a, 0x8c, 0x09, 0x0e, 0x72,
	0xe1, 0x73, 0x5e, 0xe8, 0x41, 0x85, 0x63, 0x9f,
	0x3f, 0xd7, 0x7d, 0xc4, 0xfb, 0x22, 0x5d, 0x92,
	0x6c, 0xb3, 0x1e, 0xe2, 0x50, 0x2f, 0x82, 0xa8,
	0x28, 0xc0, 0xb5, 0xd7, 0x5f, 0x68, 0x0d, 0x2c,
	0x2d, 0xaf, 0x7e, 0xfa, 0x2e, 0x08, 0x0f, 0x1f,
	0x70, 0x9f, 0xe9, 0x19, 0x72, 0x55, 0xf8, 0xfb,
	0x51, 0xd2, 0x33, 0x5d, 0xa0, 0xd3, 0x2b, 0x0a,
	0x6c, 0xbc, 0x4e, 0xcf, 0x36, 0x4d, 0xdc, 0x3b,
	0xe9, 0x3e, 0x81, 0x7c, 0x61, 0xdb, 0x20, 0x2d,
	0x3a, 0xc3, 0xb3, 0x0c, 0x1e, 0x00, 0xb9, 0x7c,
	0xf5, 0xca, 0x10, 0x5f, 0x3a, 0x71, 0xb3, 0xe4,
	0x20, 0xdb, 0x0c, 0x2a, 0x98, 0x63, 0x45, 0x00,
	0x58, 0xf6, 0x68, 0xe4, 0x0b, 0xda, 0x13, 0x3b,
	0x60, 0x5c, 0x76, 0xdb, 0xb9, 0x97, 0x71, 0xe4,
	0xd9, 0xb7, 0xdb, 0xbd, 0x68, 0xc7, 0x84, 0x84,
	0xaa, 0x7c, 0x68, 0x62, 0x5e, 0x16, 0xfc, 0xba,
	0x72, 0xaa, 0x9a, 0xa9, 0xeb, 0x7c, 0x75, 0x47,
	0x97, 0x7e, 0xad, 0xe2, 0xd9, 0x91, 0xe8, 0xe4,
	0xa5, 0x31, 0xd7, 0x01, 0x8e, 0xa2, 0x11, 0x88,
	0x95, 0xb9, 0xf2, 0x9b, 0xd3, 0x7f, 0x1b, 0x81,
	0x22, 0xf7, 0x98, 0x60, 0x0a, 0x64, 0xa6, 0xc1,
	0xf6, 0x49, 0xc7, 0xe3, 0x07, 0x4d, 0x94, 0x7a,
	0xcf, 0x6e, 0x68, 0x0c, 0x1b, 0x3f, 0x6e, 0x2e,
	0xee, 0x92, 0xfa, 0x52, 0xb3, 0x59, 0xf8, 0xf1,
	0x8f, 0x6a, 0x66, 0xa3, 0x82, 0x76, 0x4a, 0x07,
	0x1a, 0xc7, 0xdd, 0xf5, 0xda, 0x9c, 0x3c, 0x24,
	0xbf, 0xfd, 0x42, 0xa1, 0x10, 0x64, 0x6a, 0x0f,
	0x89, 0xee, 0x36, 0xa5, 0xce, 0x99, 0x48, 0x6a,
	0xf0, 0x9f, 0x9e, 0x69, 0xa4, 0x40, 0x20, 0xe9,
	0x16, 0x15, 0xf7, 0xdb, 0x75, 0x02, 0xcb, 0xe9,
	0x73, 0x8b, 0x3b, 0x49, 0x2f, 0xf0, 0xaf, 0x51,
	0x06, 0x5c, 0xdf, 0x27, 0x27, 0x49, 0x6a, 0xd1,
	0xcc, 0xc7, 0xb5, 0x63, 0xb5, 0xfc, 0xb8, 0x5c,
	0x87, 0x7f, 0x84, 0xb4, 0xcc, 0x14, 0xa9, 0x53,
	0xda, 0xa4, 0x56, 0xf8, 0xb6, 0x1b, 0xcc, 0x40,
	0x27, 0x52, 0x06, 0x5a, 0x13, 0x81, 0xd7, 0x3a,
	0xd4, 0x3b, 0xfb, 0x49, 0x65, 0x31, 0x33, 0xb2,
	0xfa, 0xcd, 0xad, 0x58, 0x4e, 0x2b, 0xae, 0xd2,
	0x20, 0xfb, 0x1a, 0x48, 0xb4, 0x3f, 0x9a, 0xd8,
	0x7a, 0x35, 0x4a, 0xc8, 0xee, 0x88, 0x5e, 0x07,
	0x66, 0x54, 0xb9, 0xec, 0x9f, 0xa3, 0xe3, 0xb9,
	0x37, 0xaa, 0x49, 0x76, 0x31, 0xda, 0x74, 0x2d,
	0x3c, 0xa4, 0x65, 0x10, 0x32, 0x38, 0xf0, 0xde,
	0xd3, 0x99, 0x17, 0xaa, 0x71, 0xaa, 0x8f, 0x0f,
	0x8c, 0xaf, 0xa2, 0xf8, 0x5d, 0x64, 0xba, 0x1d,
	0xa3, 0xef, 0x96, 0x73, 0xe8, 0xa1, 0x02, 0x8d,
	0x0c, 0x6d, 0xb8, 0x06, 0x90, 0xb8, 0x08, 0x56,
	0x2c, 0xa7, 0x06, 0xc9, 0xc2, 0x38, 0xdb, 0x7c,
	0x63, 0xb1, 0x57, 0x8e, 0xea, 0x7c, 0x79, 0xf3,
	0x49, 0x1d, 0xfe, 0x9f, 0xf3, 0x6e, 0xb1, 0x1d,
	0xba, 0x19, 0x80, 0x1a, 0x0a, 0xd3, 0xb0, 0x26,
	0x21, 0x40, 0xb1, 0x7c, 0xf9, 0x4d, 0x8d, 0x10,
	0xc1, 0x7e, 0xf4, 0xf6, 0x3c, 0xa8, 0xfd, 0x7c,
	0xa3, 0x92, 0xb2, 0x0f, 0xaa, 0xcc, 0xa6, 0x11,
	0xfe, 0x04, 0xe3, 0xd1, 0x7a, 0x32, 0x89, 0xdf,
	0x0d, 0xc4, 0x8f, 0x79, 0x6b, 0xca, 0x16, 0x7c,
	0x6e, 0xf9, 0xad, 0x0f, 0xf6, 0xfe, 0x27, 0xdb,
	0xc4, 0x13, 0x70, 0xf1, 0x62, 0x1a, 0x4f, 0x79,
	0x40, 0xc9, 0x9b, 0x8b, 0x21, 0xea, 0x84, 0xfa,
	0xf5, 0xf1, 0x89, 0xce, 0xb7, 0x55, 0x0a, 0x80,
	0x39, 0x2f, 0x55, 0x36, 0x16, 0x9c, 0x7b, 0x08,
	0xbd, 0x87, 0x0d, 0xa5, 0x32, 0xf1, 0x52, 0x7c,
	0xe8, 0x55, 0x60, 0x5b, 0xd7, 0x69, 0xe4, 0xfc,
	0xfa, 0x12, 0x85, 0x96, 0xea, 0x50, 0x28, 0xab,
	0x8a, 0xf7, 0xbb, 0x0e, 0x53, 0x74, 0xca, 0xa6,
	0x27, 0x09, 0xc2, 0xb5, 0xde, 0x18, 0x14, 0xd9,
	0xea, 0xe5, 0x29, 0x1c, 0x40, 0x56, 0xcf, 0xd7,
	0xae, 0x05, 0x3f, 0x65, 0xaf, 0x05, 0x73, 0xe2,
	0x35, 0x96, 0x27, 0x07, 0x14, 0xc0, 0xad, 0x33,
	0xf1, 0xdc, 0x44, 0x7a, 0x89, 0x17, 0x77, 0xd2,
	0x9c, 0x58, 0x60, 0xf0, 0x3f, 0x7b, 0x2d, 0x2e,
	0x57, 0x95, 0x54, 0x87, 0xed, 0xf2, 0xc7, 0x4c,
	0xf0, 0xae, 0x56, 0x29, 0x19, 0x7d, 0x66, 0x4b,
	0x9b, 0x83, 0x84, 0x42, 0x3b, 0x01, 0x25, 0x66,
	0x8e, 0x02, 0xde, 0xb9, 0x83, 0x54, 0x19, 0xf6,
	0x9f, 0x79, 0x0d, 0x67, 0xc5, 0x1d, 0x7a, 0x44,
	0x02, 0x98, 0xa7, 0x16, 0x1c, 0x29, 0x0d, 0x74,
	0xff, 0x85, 0x40, 0x06, 0xef, 0x2c, 0xa9, 0xc6,
	0xf5, 0x53, 0x07, 0x06, 0xae, 0xe4, 0xfa, 0x5f,
	0xd8, 0x39, 0x4d, 0xf1, 0x9b, 0x6b, 0xd9, 0x24,
	0x84, 0xfe, 0x03, 0x4c, 0xb2, 0x3f, 0xdf, 0xa1,
	0x05, 0x9e, 0x50, 0x14, 0x5a, 0xd9, 0x1a, 0xa2,
	0xa7, 0xfa, 0xfa, 0x17, 0xf7, 0x78, 0xd6, 0xb5,
	0x92, 0x61, 0x91, 0xac, 0x36, 0xfa, 0x56, 0x0d,
	0x38, 0x32, 0x18, 0x85, 0x08, 0x58, 0x37, 0xf0,
	0x4b, 0xdb, 0x59, 0xe7, 0xa4, 0x34, 0xc0, 0x1b,
	0x01, 0xaf, 0x2d, 0xde, 0xa1, 0xaa, 0x5d, 0xd3,
	0xec, 0xe1, 0xd4, 0xf7, 0xe6, 0x54, 0x68, 0xf0,
	0x51, 0x97, 0xa7, 0x89, 0xea, 0x24, 0xad, 0xd3,
	0x6e, 0x47, 0x93, 0x8b, 0x4b, 0xb4, 0xf7, 0x1c,
	0x42, 0x06, 0x67, 0xe8, 0x99, 0xf6, 0xf5, 0x7b,
	0x85, 0xb5, 0x65, 0xb5, 0xb5, 0xd2, 0x37, 0xf5,
	0xf3, 0x02, 0xa6, 0x4d, 0x11, 0xa7, 0xdc, 0x51,
	0x09, 0x7f, 0xa0, 0xd8, 0x88, 0x1c, 0x13, 0x71,
	0xae, 0x9c, 0xb7, 0x7b, 0x34, 0xd6, 0x4e, 0x68,
	0x26, 0x83, 0x51, 0xaf, 0x1d, 0xee, 0x8b, 0xbb,
	0x69, 0x43, 0x2b, 0x9e, 0x8a, 0xbc, 0x02, 0x0e,
	0xa0, 0x1b, 0xe0, 0xa8, 0x5f, 0x6f, 0xaf, 0x1b,
	0x8f, 0xe7, 0x64, 0x71, 0x74, 0x11, 0x7e, 0xa8,
	0xd8, 0xf9, 0x97, 0x06, 0xc3, 0xb6, 0xfb, 0xfb,
	0xb7, 0x3d, 0x35, 0x9d, 0x3b, 0x52, 0xed, 0x54,
	0xca, 0xf4, 0x81, 0x01, 0x2d, 0x1b, 0xc3, 0xa7,
	0x00, 0x3d, 0x1a, 0x39, 0x54, 0xe1, 0xf6, 0xff,
	0xed, 0x6f, 0x0b, 0x5a, 0x68, 0xda, 0x58, 0xdd,
	0xa9, 0xcf, 0x5c, 0x4a, 0xe5, 0x09, 0x4e, 0xde,
	0x9d, 0xbc, 0x3e, 0xee, 0x5a, 0x00, 0x3b, 0x2c,
	0x87, 0x10, 0x65, 0x60, 0xdd, 0xd7, 0x56, 0xd1,
	0x4c, 0x64, 0x45, 0xe4, 0x21, 0xec, 0x78, 0xf8,
	0x25, 0x7a, 0x3e, 0x16, 0x5d, 0x09, 0x53, 0x14,
	0xbe, 0x4f, 0xae, 0x87, 0xd8, 0xd1, 0xaa, 0x3c,
	0xf6, 0x3e, 0xa4, 0x70, 0x8c, 0x5e, 0x70, 0xa4,
	0xb3, 0x6b, 0x66, 0x73, 0xd3, 0xbf, 0x31, 0x06,
	0x19, 0x62, 0x93, 0x15, 0xf2, 0x86, 0xe4, 0x52,
	0x7e, 0x53, 0x4c, 0x12, 0x38, 0xcc, 0x34, 0x7d,
	0x57, 0xf6, 0x42, 0x93, 0x8a, 0xc4, 0xee, 0x5c,
	0x8a, 0xe1, 0x52, 0x8f, 0x56, 0x64, 0xf6, 0xa6,
	0xd1, 0x91, 0x57, 0x70, 0xcd, 0x11, 0x76, 0xf5,
	0x59, 0x60, 0x60, 0x3c, 0xc1, 0xc3, 0x0b, 0x7f,
	0x58, 0x1a, 0x50, 0x91, 0xf1, 0x68, 0x8f, 0x6e,
	0x74, 0x74, 0xa8, 0x51, 0x0b, 0xf7, 0x7a, 0x98,
	0x37, 0xf2, 0x0a, 0x0e, 0xa4, 0x97, 0x04, 0xb8,
	0x9b, 0xfd, 0xa0, 0xea, 0xf7, 0x0d, 0xe1, 0xdb,
	0x03, 0xf0, 0x31, 0x29, 0xf8, 0xdd, 0x6b, 0x8b,
	0x5d, 0xd8, 0x59, 0xa9, 0x29, 0xcf, 0x9a, 0x79,
	0x89, 0x19, 0x63, 0x46, 0x09, 0x79, 0x6a, 0x11,
	0xda, 0x63, 0x68, 0x48, 0x77, 0x23, 0xfb, 0x7d,
	0x3a, 0x43, 0xcb, 0x02, 0x3b, 0x7a, 0x6d, 0x10,
	0x2a, 0x9e, 0xac, 0xf1, 0xd4, 0x19, 0xf8, 0x23,
	0x64, 0x1d, 0x2c, 0x5f, 0xf2, 0xb0, 0x5c, 0x23,
	0x27, 0xf7, 0x27, 0x30, 0x16, 0x37, 0xb1, 0x90,
	0xab, 0x38, 0xfb, 0x55, 0xcd, 0x78, 0x58, 0xd4,
	0x7d, 0x43, 0xf6, 0x45, 0x5e, 0x55, 0x8d, 0xb1,
	0x02, 0x65, 0x58, 0xb4, 0x13, 0x4b, 0x36, 0xf7,
	0xcc, 0xfe, 0x3d, 0x0b, 0x82, 0xe2, 0x12, 0x11,
	0xbb, 0xe6, 0xb8, 0x3a, 0x48, 0x71, 0xc7, 0x50,
	0x06, 0x16, 0x3a, 0xe6, 0x7c, 0x05, 0xc7, 0xc8,
	0x4d, 0x2f, 0x08, 0x6a, 0x17, 0x9a, 0x95, 0x97,
	0x50, 0x68, 0xdc, 0x28, 0x18, 0xc4, 0x61, 0x38,
	0xb9, 0xe0, 0x3e, 0x78, 0xdb, 0x29, 0xe0, 0x9f,
	0x52, 0xdd, 0xf8, 0x4f, 0x91, 0xc1, 0xd0, 0x33,
	0xa1, 0x7a, 0x8e, 0x30, 0x13, 0x82, 0x07, 0x9f,
	0xd3, 0x31, 0x0f, 0x23, 0xbe, 0x32, 0x5a, 0x75,
	0xcf, 0x96, 0xb2, 0xec, 0xb5, 0x32, 0xac, 0x21,
	0xd1, 0x82, 0x33, 0xd3, 0x15, 0x74, 0xbd, 0x90,
	0xf1, 0x2c, 0xe6, 0x5f, 0x8d, 0xe3, 0x02, 0xe8,
	0xe9, 0xc4, 0xca, 0x96, 0xeb, 0x0e, 0xbc, 0x91,
	0xf4, 0xb9, 0xea, 0xd9, 0x1b, 0x75, 0xbd, 0xe1,
	0xac, 0x2a, 0x05, 0x37, 0x52, 0x9b, 0x1b, 0x3f,
	0x5a, 0xdc, 0x21, 0xc3, 0x98, 0xbb, 0xaf, 0xa3,
	0xf2, 0x00, 0xbf, 0x0d, 0x30, 0x89, 0x05, 0xcc,
	0xa5, 0x76, 0xf5, 0x06, 0xf0, 0xc6, 0x54, 0x8a,
	0x5d, 0xd4, 0x1e, 0xc1, 0xf2, 0xce, 0xb0, 0x62,
	0xc8, 0xfc, 0x59, 0x42, 0x9a, 0x90, 0x60, 0x55,
	0xfe, 0x88, 0xa5, 0x8b, 0xb8, 0x33, 0x0c, 0x23,
	0x24, 0x0d, 0x15, 0x70, 0x37, 0x1e, 0x3d, 0xf6,
	0xd2, 0xea, 0x92, 0x10, 0xb2, 0xc4, 0x51, 0xac,
	0xf2, 0xac, 0xf3, 0x6b, 0x6c, 0xaa, 0xcf, 0x12,
	0xc5, 0x6c, 0x90, 0x50, 0xb5, 0x0c, 0xfc, 0x1a,
	0x15, 0x52, 0xe9, 0x26, 0xc6, 0x52, 0xa4, 0xe7,
	0x81, 0x69, 0xe1, 0xe7, 0x9e, 0x30, 0x01, 0xec,
	0x84, 0x89, 0xb2, 0x0d, 0x66, 0xdd, 0xce, 0x28,
	0x5c, 0xec, 0x98, 0x46, 0x68, 0x21, 0x9f, 0x88,
	0x3f, 0x1f, 0x42, 0x77, 0xce, 0xd0, 0x61, 0xd4,
	0x20, 0xa7, 0xff, 0x53, 0xad, 0x37, 0xd0, 0x17,
	0x35, 0xc9, 0xfc, 0xba, 0x0a, 0x78, 0x3f, 0xf2,
	0xcc, 0x86, 0x89, 0xe8, 0x4b, 0x3c, 0x48, 0x33,
	0x09, 0x7f, 0xc6, 0xc0, 0xdd, 0xb8, 0xfd, 0x7a,
	0x66, 0x66, 0x65, 0xeb, 0x47, 0xa7, 0x04, 0x28,
	0xa3, 0x19, 0x8e, 0xa9, 0xb1, 0x13, 0x67, 0x62,
	0x70, 0xcf, 0xd7
};
static const u8 dec_output013[] __initconst = {
	0x74, 0xa6, 0x3e, 0xe4, 0xb1, 0xcb, 0xaf, 0xb0,
	0x40, 0xe5, 0x0f, 0x9e, 0xf1, 0xf2, 0x89, 0xb5,
	0x42, 0x34, 0x8a, 0xa1, 0x03, 0xb7, 0xe9, 0x57,
	0x46, 0xbe, 0x20, 0xe4, 0x6e, 0xb0, 0xeb, 0xff,
	0xea, 0x07, 0x7e, 0xef, 0xe2, 0x55, 0x9f, 0xe5,
	0x78, 0x3a, 0xb7, 0x83, 0xc2, 0x18, 0x40, 0x7b,
	0xeb, 0xcd, 0x81, 0xfb, 0x90, 0x12, 0x9e, 0x46,
	0xa9, 0xd6, 0x4a, 0xba, 0xb0, 0x62, 0xdb, 0x6b,
	0x99, 0xc4, 0xdb, 0x54, 0x4b, 0xb8, 0xa5, 0x71,
	0xcb, 0xcd, 0x63, 0x32, 0x55, 0xfb, 0x31, 0xf0,
	0x38, 0xf5, 0xbe, 0x78, 0xe4, 0x45, 0xce, 0x1b,
	0x6a, 0x5b, 0x0e, 0xf4, 0x16, 0xe4, 0xb1, 0x3d,
	0xf6, 0x63, 0x7b, 0xa7, 0x0c, 0xde, 0x6f, 0x8f,
	0x74, 0xdf, 0xe0, 0x1e, 0x9d, 0xce, 0x8f, 0x24,
	0xef, 0x23, 0x35, 0x33, 0x7b, 0x83, 0x34, 0x23,
	0x58, 0x74, 0x14, 0x77, 0x1f, 0xc2, 0x4f, 0x4e,
	0xc6, 0x89, 0xf9, 0x52, 0x09, 0x37, 0x64, 0x14,
	0xc4, 0x01, 0x6b, 0x9d, 0x77, 0xe8, 0x90, 0x5d,
	0xa8, 0x4a, 0x2a, 0xef, 0x5c, 0x7f, 0xeb, 0xbb,
	0xb2, 0xc6, 0x93, 0x99, 0x66, 0xdc, 0x7f, 0xd4,
	0x9e, 0x2a, 0xca, 0x8d, 0xdb, 0xe7, 0x20, 0xcf,
	0xe4, 0x73, 0xae, 0x49, 0x7d, 0x64, 0x0f, 0x0e,
	0x28, 0x46, 0xa9, 0xa8, 0x32, 0xe4, 0x0e, 0xf6,
	0x51, 0x53, 0xb8, 0x3c, 0xb1, 0xff, 0xa3, 0x33,
	0x41, 0x75, 0xff, 0xf1, 0x6f, 0xf1, 0xfb, 0xbb,
	0x83, 0x7f, 0x06, 0x9b, 0xe7, 0x1b, 0x0a, 0xe0,
	0x5c, 0x33, 0x60, 0x5b, 0xdb, 0x5b, 0xed, 0xfe,
	0xa5, 0x16, 0x19, 0x72, 0xa3, 0x64, 0x23, 0x00,
	0x02, 0xc7, 0xf3, 0x6a, 0x81, 0x3e, 0x44, 0x1d,
	0x79, 0x15, 0x5f, 0x9a, 0xde, 0xe2, 0xfd, 0x1b,
	0x73, 0xc1, 0xbc, 0x23, 0xba, 0x31, 0xd2, 0x50,
	0xd5, 0xad, 0x7f, 0x74, 0xa7, 0xc9, 0xf8, 0x3e,
	0x2b, 0x26, 0x10, 0xf6, 0x03, 0x36, 0x74, 0xe4,
	0x0e, 0x6a, 0x72, 0xb7, 0x73, 0x0a, 0x42, 0x28,
	0xc2, 0xad, 0x5e, 0x03, 0xbe, 0xb8, 0x0b, 0xa8,
	0x5b, 0xd4, 0xb8, 0xba, 0x52, 0x89, 0xb1, 0x9b,
	0xc1, 0xc3, 0x65, 0x87, 0xed, 0xa5, 0xf4, 0x86,
	0xfd, 0x41, 0x80, 0x91, 0x27, 0x59, 0x53, 0x67,
	0x15, 0x78, 0x54, 0x8b, 0x2d, 0x3d, 0xc7, 0xff,
	0x02, 0x92, 0x07, 0x5f, 0x7a, 0x4b, 0x60, 0x59,
	0x3c, 0x6f, 0x5c, 0xd8, 0xec, 0x95, 0xd2, 0xfe,
	0xa0, 0x3b, 0xd8, 0x3f, 0xd1, 0x69, 0xa6, 0xd6,
	0x41, 0xb2, 0xf4, 0x4d, 0x12, 0xf4, 0x58, 0x3e,
	0x66, 0x64, 0x80, 0x31, 0x9b, 0xa8, 0x4c, 0x8b,
	0x07, 0xb2, 0xec, 0x66, 0x94, 0x66, 0x47, 0x50,
	0x50, 0x5f, 0x18, 0x0b, 0x0e, 0xd6, 0xc0, 0x39,
	0x21, 0x13, 0x9e, 0x33, 0xbc, 0x79, 0x36, 0x02,
	0x96, 0x70, 0xf0, 0x48, 0x67, 0x2f, 0x26, 0xe9,
	0x6d, 0x10, 0xbb, 0xd6, 0x3f, 0xd1, 0x64, 0x7a,
	0x2e, 0xbe, 0x0c, 0x61, 0xf0, 0x75, 0x42, 0x38,
	0x23, 0xb1, 0x9e, 0x9f, 0x7c, 0x67, 0x66, 0xd9,
	0x58, 0x9a, 0xf1, 0xbb, 0x41, 0x2a, 0x8d, 0x65,
	0x84, 0x94, 0xfc, 0xdc, 0x6a, 0x50, 0x64, 0xdb,
	0x56, 0x33, 0x76, 0x00, 0x10, 0xed, 0xbe, 0xd2,
	0x12, 0xf6, 0xf6, 0x1b, 0xa2, 0x16, 0xde, 0xae,
	0x31, 0x95, 0xdd, 0xb1, 0x08, 0x7e, 0x4e, 0xee,
	0xe7, 0xf9, 0xa5, 0xfb, 0x5b, 0x61, 0x43, 0x00,
	0x40, 0xf6, 0x7e, 0x02, 0x04, 0x32, 0x4e, 0x0c,
	0xe2, 0x66, 0x0d, 0xd7, 0x07, 0x98, 0x0e, 0xf8,
	0x72, 0x34, 0x6d, 0x95, 0x86, 0xd7, 0xcb, 0x31,
	0x54, 0x47, 0xd0, 0x38, 0x29, 0x9c, 0x5a, 0x68,
	0xd4, 0x87, 0x76, 0xc9, 0xe7, 0x7e, 0xe3, 0xf4,
	0x81, 0x6d, 0x18, 0xcb, 0xc9, 0x05, 0xaf, 0xa0,
	0xfb, 0x66, 0xf7, 0xf1, 0x1c, 0xc6, 0x14, 0x11,
	0x4f, 0x2b, 0x79, 0x42, 0x8b, 0xbc, 0xac, 0xe7,
	0x6c, 0xfe, 0x0f, 0x58, 0xe7, 0x7c, 0x78, 0x39,
	0x30, 0xb0, 0x66, 0x2c, 0x9b, 0x6d, 0x3a, 0xe1,
	0xcf, 0xc9, 0xa4, 0x0e, 0x6d, 0x6d, 0x8a, 0xa1,
	0x3a, 0xe7, 0x28, 0xd4, 0x78, 0x4c, 0xa6, 0xa2,
	0x2a, 0xa6, 0x03, 0x30, 0xd7, 0xa8, 0x25, 0x66,
	0x87, 0x2f, 0x69, 0x5c, 0x4e, 0xdd, 0xa5, 0x49,
	0x5d, 0x37, 0x4a, 0x59, 0xc4, 0xaf, 0x1f, 0xa2,
	0xe4, 0xf8, 0xa6, 0x12, 0x97, 0xd5, 0x79, 0xf5,
	0xe2, 0x4a, 0x2b, 0x5f, 0x61, 0xe4, 0x9e, 0xe3,
	0xee, 0xb8, 0xa7, 0x5b, 0x2f, 0xf4, 0x9e, 0x6c,
	0xfb, 0xd1, 0xc6, 0x56, 0x77, 0xba, 0x75, 0xaa,
	0x3d, 0x1a, 0xa8, 0x0b, 0xb3, 0x68, 0x24, 0x00,
	0x10, 0x7f, 0xfd, 0xd7, 0xa1, 0x8d, 0x83, 0x54,
	0x4f, 0x1f, 0xd8, 0x2a, 0xbe, 0x8a, 0x0c, 0x87,
	0xab, 0xa2, 0xde, 0xc3, 0x39, 0xbf, 0x09, 0x03,
	0xa5, 0xf3, 0x05, 0x28, 0xe1, 0xe1, 0xee, 0x39,
	0x70, 0x9c, 0xd8, 0x81, 0x12, 0x1e, 0x02, 0x40,
	0xd2, 0x6e, 0xf0, 0xeb, 0x1b, 0x3d, 0x22, 0xc6,
	0xe5, 0xe3, 0xb4, 0x5a, 0x98, 0xbb, 0xf0, 0x22,
	0x28, 0x8d, 0xe5, 0xd3, 0x16, 0x48, 0x24, 0xa5,
	0xe6, 0x66, 0x0c, 0xf9, 0x08, 0xf9, 0x7e, 0x1e,
	0xe1, 0x28, 0x26, 0x22, 0xc7, 0xc7, 0x0a, 0x32,
	0x47, 0xfa, 0xa3, 0xbe, 0x3c, 0xc4, 0xc5, 0x53,
	0x0a, 0xd5, 0x94, 0x4a, 0xd7, 0x93, 0xd8, 0x42,
	0x99, 0xb9, 0x0a, 0xdb, 0x56, 0xf7, 0xb9, 0x1c,
	0x53, 0x4f, 0xfa, 0xd3, 0x74, 0xad, 0xd9, 0x68,
	0xf1, 0x1b, 0xdf, 0x61, 0xc6, 0x5e, 0xa8, 0x48,
	0xfc, 0xd4, 0x4a, 0x4c, 0x3c, 0x32, 0xf7, 0x1c,
	0x96, 0x21, 0x9b, 0xf9, 0xa3, 0xcc, 0x5a, 0xce,
	0xd5, 0xd7, 0x08, 0x24, 0xf6, 0x1c, 0xfd, 0xdd,
	0x38, 0xc2, 0x32, 0xe9, 0xb8, 0xe7, 0xb6, 0xfa,
	0x9d, 0x45, 0x13, 0x2c, 0x83, 0xfd, 0x4a, 0x69,
	0x82, 0xcd, 0xdc, 0xb3, 0x76, 0x0c, 0x9e, 0xd8,
	0xf4, 0x1b, 0x45, 0x15, 0xb4, 0x97, 0xe7, 0x58,
	0x34, 0xe2, 0x03, 0x29, 0x5a, 0xbf, 0xb6, 0xe0,
	0x5d, 0x13, 0xd9, 0x2b, 0xb4, 0x80, 0xb2, 0x45,
	0x81, 0x6a, 0x2e, 0x6c, 0x89, 0x7d, 0xee, 0xbb,
	0x52, 0xdd, 0x1f, 0x18, 0xe7, 0x13, 0x6b, 0x33,
	0x0e, 0xea, 0x36, 0x92, 0x77, 0x7b, 0x6d, 0x9c,
	0x5a, 0x5f, 0x45, 0x7b, 0x7b, 0x35, 0x62, 0x23,
	0xd1, 0xbf, 0x0f, 0xd0, 0x08, 0x1b, 0x2b, 0x80,
	0x6b, 0x7e, 0xf1, 0x21, 0x47, 0xb0, 0x57, 0xd1,
	0x98, 0x72, 0x90, 0x34, 0x1c, 0x20, 0x04, 0xff,
	0x3d, 0x5c, 0xee, 0x0e, 0x57, 0x5f, 0x6f, 0x24,
	0x4e, 0x3c, 0xea, 0xfc, 0xa5, 0xa9, 0x83, 0xc9,
	0x61, 0xb4, 0x51, 0x24, 0xf8, 0x27, 0x5e, 0x46,
	0x8c, 0xb1, 0x53, 0x02, 0x96, 0x35, 0xba, 0xb8,
	0x4c, 0x71, 0xd3, 0x15, 0x59, 0x35, 0x22, 0x20,
	0xad, 0x03, 0x9f, 0x66, 0x44, 0x3b, 0x9c, 0x35,
	0x37, 0x1f, 0x9b, 0xbb, 0xf3, 0xdb, 0x35, 0x63,
	0x30, 0x64, 0xaa, 0xa2, 0x06, 0xa8, 0x5d, 0xbb,
	0xe1, 0x9f, 0x70, 0xec, 0x82, 0x11, 0x06, 0x36,
	0xec, 0x8b, 0x69, 0x66, 0x24, 0x44, 0xc9, 0x4a,
	0x57, 0xbb, 0x9b, 0x78, 0x13, 0xce, 0x9c, 0x0c,
	0xba, 0x92, 0x93, 0x63, 0xb8, 0xe2, 0x95, 0x0f,
	0x0f, 0x16, 0x39, 0x52, 0xfd, 0x3a, 0x6d, 0x02,
	0x4b, 0xdf, 0x13, 0xd3, 0x2a, 0x22, 0xb4, 0x03,
	0x7c, 0x54, 0x49, 0x96, 0x68, 0x54, 0x10, 0xfa,
	0xef, 0xaa, 0x6c, 0xe8, 0x22, 0xdc, 0x71, 0x16,
	0x13, 0x1a, 0xf6, 0x28, 0xe5, 0x6d, 0x77, 0x3d,
	0xcd, 0x30, 0x63, 0xb1, 0x70, 0x52, 0xa1, 0xc5,
	0x94, 0x5f, 0xcf, 0xe8, 0xb8, 0x26, 0x98, 0xf7,
	0x06, 0xa0, 0x0a, 0x70, 0xfa, 0x03, 0x80, 0xac,
	0xc1, 0xec, 0xd6, 0x4c, 0x54, 0xd7, 0xfe, 0x47,
	0xb6, 0x88, 0x4a, 0xf7, 0x71, 0x24, 0xee, 0xf3,
	0xd2, 0xc2, 0x4a, 0x7f, 0xfe, 0x61, 0xc7, 0x35,
	0xc9, 0x37, 0x67, 0xcb, 0x24, 0x35, 0xda, 0x7e,
	0xca, 0x5f, 0xf3, 0x8d, 0xd4, 0x13, 0x8e, 0xd6,
	0xcb, 0x4d, 0x53, 0x8f, 0x53, 0x1f, 0xc0, 0x74,
	0xf7, 0x53, 0xb9, 0x5e, 0x23, 0x37, 0xba, 0x6e,
	0xe3, 0x9d, 0x07, 0x55, 0x25, 0x7b, 0xe6, 0x2a,
	0x64, 0xd1, 0x32, 0xdd, 0x54, 0x1b, 0x4b, 0xc0,
	0xe1, 0xd7, 0x69, 0x58, 0xf8, 0x93, 0x29, 0xc4,
	0xdd, 0x23, 0x2f, 0xa5, 0xfc, 0x9d, 0x7e, 0xf8,
	0xd4, 0x90, 0xcd, 0x82, 0x55, 0xdc, 0x16, 0x16,
	0x9f, 0x07, 0x52, 0x9b, 0x9d, 0x25, 0xed, 0x32,
	0xc5, 0x7b, 0xdf, 0xf6, 0x83, 0x46, 0x3d, 0x65,
	0xb7, 0xef, 0x87, 0x7a, 0x12, 0x69, 0x8f, 0x06,
	0x7c, 0x51, 0x15, 0x4a, 0x08, 0xe8, 0xac, 0x9a,
	0x0c, 0x24, 0xa7, 0x27, 0xd8, 0x46, 0x2f, 0xe7,
	0x01, 0x0e, 0x1c, 0xc6, 0x91, 0xb0, 0x6e, 0x85,
	0x65, 0xf0, 0x29, 0x0d, 0x2e, 0x6b, 0x3b, 0xfb,
	0x4b, 0xdf, 0xe4, 0x80, 0x93, 0x03, 0x66, 0x46,
	0x3e, 0x8a, 0x6e, 0xf3, 0x5e, 0x4d, 0x62, 0x0e,
	0x49, 0x05, 0xaf, 0xd4, 0xf8, 0x21, 0x20, 0x61,
	0x1d, 0x39, 0x17, 0xf4, 0x61, 0x47, 0x95, 0xfb,
	0x15, 0x2e, 0xb3, 0x4f, 0xd0, 0x5d, 0xf5, 0x7d,
	0x40, 0xda, 0x90, 0x3c, 0x6b, 0xcb, 0x17, 0x00,
	0x13, 0x3b, 0x64, 0x34, 0x1b, 0xf0, 0xf2, 0xe5,
	0x3b, 0xb2, 0xc7, 0xd3, 0x5f, 0x3a, 0x44, 0xa6,
	0x9b, 0xb7, 0x78, 0x0e, 0x42, 0x5d, 0x4c, 0xc1,
	0xe9, 0xd2, 0xcb, 0xb7, 0x78, 0xd1, 0xfe, 0x9a,
	0xb5, 0x07, 0xe9, 0xe0, 0xbe, 0xe2, 0x8a, 0xa7,
	0x01, 0x83, 0x00, 0x8c, 0x5c, 0x08, 0xe6, 0x63,
	0x12, 0x92, 0xb7, 0xb7, 0xa6, 0x19, 0x7d, 0x38,
	0x13, 0x38, 0x92, 0x87, 0x24, 0xf9, 0x48, 0xb3,
	0x5e, 0x87, 0x6a, 0x40, 0x39, 0x5c, 0x3f, 0xed,
	0x8f, 0xee, 0xdb, 0x15, 0x82, 0x06, 0xda, 0x49,
	0x21, 0x2b, 0xb5, 0xbf, 0x32, 0x7c, 0x9f, 0x42,
	0x28, 0x63, 0xcf, 0xaf, 0x1e, 0xf8, 0xc6, 0xa0,
	0xd1, 0x02, 0x43, 0x57, 0x62, 0xec, 0x9b, 0x0f,
	0x01, 0x9e, 0x71, 0xd8, 0x87, 0x9d, 0x01, 0xc1,
	0x58, 0x77, 0xd9, 0xaf, 0xb1, 0x10, 0x7e, 0xdd,
	0xa6, 0x50, 0x96, 0xe5, 0xf0, 0x72, 0x00, 0x6d,
	0x4b, 0xf8, 0x2a, 0x8f, 0x19, 0xf3, 0x22, 0x88,
	0x11, 0x4a, 0x8b, 0x7c, 0xfd, 0xb7, 0xed, 0xe1,
	0xf6, 0x40, 0x39, 0xe0, 0xe9, 0xf6, 0x3d, 0x25,
	0xe6, 0x74, 0x3c, 0x58, 0x57, 0x7f, 0xe1, 0x22,
	0x96, 0x47, 0x31, 0x91, 0xba, 0x70, 0x85, 0x28,
	0x6b, 0x9f, 0x6e, 0x25, 0xac, 0x23, 0x66, 0x2f,
	0x29, 0x88, 0x28, 0xce, 0x8c, 0x5c, 0x88, 0x53,
	0xd1, 0x3b, 0xcc, 0x6a, 0x51, 0xb2, 0xe1, 0x28,
	0x3f, 0x91, 0xb4, 0x0d, 0x00, 0x3a, 0xe3, 0xf8,
	0xc3, 0x8f, 0xd7, 0x96, 0x62, 0x0e, 0x2e, 0xfc,
	0xc8, 0x6c, 0x77, 0xa6, 0x1d, 0x22, 0xc1, 0xb8,
	0xe6, 0x61, 0xd7, 0x67, 0x36, 0x13, 0x7b, 0xbb,
	0x9b, 0x59, 0x09, 0xa6, 0xdf, 0xf7, 0x6b, 0xa3,
	0x40, 0x1a, 0xf5, 0x4f, 0xb4, 0xda, 0xd3, 0xf3,
	0x81, 0x93, 0xc6, 0x18, 0xd9, 0x26, 0xee, 0xac,
	0xf0, 0xaa, 0xdf, 0xc5, 0x9c, 0xca, 0xc2, 0xa2,
	0xcc, 0x7b, 0x5c, 0x24, 0xb0, 0xbc, 0xd0, 0x6a,
	0x4d, 0x89, 0x09, 0xb8, 0x07, 0xfe, 0x87, 0xad,
	0x0a, 0xea, 0xb8, 0x42, 0xf9, 0x5e, 0xb3, 0x3e,
	0x36, 0x4c, 0xaf, 0x75, 0x9e, 0x1c, 0xeb, 0xbd,
	0xbc, 0xbb, 0x80, 0x40, 0xa7, 0x3a, 0x30, 0xbf,
	0xa8, 0x44, 0xf4, 0xeb, 0x38, 0xad, 0x29, 0xba,
	0x23, 0xed, 0x41, 0x0c, 0xea, 0xd2, 0xbb, 0x41,
	0x18, 0xd6, 0xb9, 0xba, 0x65, 0x2b, 0xa3, 0x91,
	0x6d, 0x1f, 0xa9, 0xf4, 0xd1, 0x25, 0x8d, 0x4d,
	0x38, 0xff, 0x64, 0xa0, 0xec, 0xde, 0xa6, 0xb6,
	0x79, 0xab, 0x8e, 0x33, 0x6c, 0x47, 0xde, 0xaf,
	0x94, 0xa4, 0xa5, 0x86, 0x77, 0x55, 0x09, 0x92,
	0x81, 0x31, 0x76, 0xc7, 0x34, 0x22, 0x89, 0x8e,
	0x3d, 0x26, 0x26, 0xd7, 0xfc, 0x1e, 0x16, 0x72,
	0x13, 0x33, 0x63, 0xd5, 0x22, 0xbe, 0xb8, 0x04,
	0x34, 0x84, 0x41, 0xbb, 0x80, 0xd0, 0x9f, 0x46,
	0x48, 0x07, 0xa7, 0xfc, 0x2b, 0x3a, 0x75, 0x55,
	0x8c, 0xc7, 0x6a, 0xbd, 0x7e, 0x46, 0x08, 0x84,
	0x0f, 0xd5, 0x74, 0xc0, 0x82, 0x8e, 0xaa, 0x61,
	0x05, 0x01, 0xb2, 0x47, 0x6e, 0x20, 0x6a, 0x2d,
	0x58, 0x70, 0x48, 0x32, 0xa7, 0x37, 0xd2, 0xb8,
	0x82, 0x1a, 0x51, 0xb9, 0x61, 0xdd, 0xfd, 0x9d,
	0x6b, 0x0e, 0x18, 0x97, 0xf8, 0x45, 0x5f, 0x87,
	0x10, 0xcf, 0x34, 0x72, 0x45, 0x26, 0x49, 0x70,
	0xe7, 0xa3, 0x78, 0xe0, 0x52, 0x89, 0x84, 0x94,
	0x83, 0x82, 0xc2, 0x69, 0x8f, 0xe3, 0xe1, 0x3f,
	0x60, 0x74, 0x88, 0xc4, 0xf7, 0x75, 0x2c, 0xfb,
	0xbd, 0xb6, 0xc4, 0x7e, 0x10, 0x0a, 0x6c, 0x90,
	0x04, 0x9e, 0xc3, 0x3f, 0x59, 0x7c, 0xce, 0x31,
	0x18, 0x60, 0x57, 0x73, 0x46, 0x94, 0x7d, 0x06,
	0xa0, 0x6d, 0x44, 0xec, 0xa2, 0x0a, 0x9e, 0x05,
	0x15, 0xef, 0xca, 0x5c, 0xbf, 0x00, 0xeb, 0xf7,
	0x3d, 0x32, 0xd4, 0xa5, 0xef, 0x49, 0x89, 0x5e,
	0x46, 0xb0, 0xa6, 0x63, 0x5b, 0x8a, 0x73, 0xae,
	0x6f, 0xd5, 0x9d, 0xf8, 0x4f, 0x40, 0xb5, 0xb2,
	0x6e, 0xd3, 0xb6, 0x01, 0xa9, 0x26, 0xa2, 0x21,
	0xcf, 0x33, 0x7a, 0x3a, 0xa4, 0x23, 0x13, 0xb0,
	0x69, 0x6a, 0xee, 0xce, 0xd8, 0x9d, 0x01, 0x1d,
	0x50, 0xc1, 0x30, 0x6c, 0xb1, 0xcd, 0xa0, 0xf0,
	0xf0, 0xa2, 0x64, 0x6f, 0xbb, 0xbf, 0x5e, 0xe6,
	0xab, 0x87, 0xb4, 0x0f, 0x4f, 0x15, 0xaf, 0xb5,
	0x25, 0xa1, 0xb2, 0xd0, 0x80, 0x2c, 0xfb, 0xf9,
	0xfe, 0xd2, 0x33, 0xbb, 0x76, 0xfe, 0x7c, 0xa8,
	0x66, 0xf7, 0xe7, 0x85, 0x9f, 0x1f, 0x85, 0x57,
	0x88, 0xe1, 0xe9, 0x63, 0xe4, 0xd8, 0x1c, 0xa1,
	0xfb, 0xda, 0x44, 0x05, 0x2e, 0x1d, 0x3a, 0x1c,
	0xff, 0xc8, 0x3b, 0xc0, 0xfe, 0xda, 0x22, 0x0b,
	0x43, 0xd6, 0x88, 0x39, 0x4c, 0x4a, 0xa6, 0x69,
	0x18, 0x93, 0x42, 0x4e, 0xb5, 0xcc, 0x66, 0x0d,
	0x09, 0xf8, 0x1e, 0x7c, 0xd3, 0x3c, 0x99, 0x0d,
	0x50, 0x1d, 0x62, 0xe9, 0x57, 0x06, 0xbf, 0x19,
	0x88, 0xdd, 0xad, 0x7b, 0x4f, 0xf9, 0xc7, 0x82,
	0x6d, 0x8d, 0xc8, 0xc4, 0xc5, 0x78, 0x17, 0x20,
	0x15, 0xc5, 0x52, 0x41, 0xcf, 0x5b, 0xd6, 0x7f,
	0x94, 0x02, 0x41, 0xe0, 0x40, 0x22, 0x03, 0x5e,
	0xd1, 0x53, 0xd4, 0x86, 0xd3, 0x2c, 0x9f, 0x0f,
	0x96, 0xe3, 0x6b, 0x9a, 0x76, 0x32, 0x06, 0x47,
	0x4b, 0x11, 0xb3, 0xdd, 0x03, 0x65, 0xbd, 0x9b,
	0x01, 0xda, 0x9c, 0xb9, 0x7e, 0x3f, 0x6a, 0xc4,
	0x7b, 0xea, 0xd4, 0x3c, 0xb9, 0xfb, 0x5c, 0x6b,
	0x64, 0x33, 0x52, 0xba, 0x64, 0x78, 0x8f, 0xa4,
	0xaf, 0x7a, 0x61, 0x8d, 0xbc, 0xc5, 0x73, 0xe9,
	0x6b, 0x58, 0x97, 0x4b, 0xbf, 0x63, 0x22, 0xd3,
	0x37, 0x02, 0x54, 0xc5, 0xb9, 0x16, 0x4a, 0xf0,
	0x19, 0xd8, 0x94, 0x57, 0xb8, 0x8a, 0xb3, 0x16,
	0x3b, 0xd0, 0x84, 0x8e, 0x67, 0xa6, 0xa3, 0x7d,
	0x78, 0xec, 0x00
};
static const u8 dec_assoc013[] __initconst = {
	0xb1, 0x69, 0x83, 0x87, 0x30, 0xaa, 0x5d, 0xb8,
	0x77, 0xe8, 0x21, 0xff, 0x06, 0x59, 0x35, 0xce,
	0x75, 0xfe, 0x38, 0xef, 0xb8, 0x91, 0x43, 0x8c,
	0xcf, 0x70, 0xdd, 0x0a, 0x68, 0xbf, 0xd4, 0xbc,
	0x16, 0x76, 0x99, 0x36, 0x1e, 0x58, 0x79, 0x5e,
	0xd4, 0x29, 0xf7, 0x33, 0x93, 0x48, 0xdb, 0x5f,
	0x01, 0xae, 0x9c, 0xb6, 0xe4, 0x88, 0x6d, 0x2b,
	0x76, 0x75, 0xe0, 0xf3, 0x74, 0xe2, 0xc9
};
static const u8 dec_nonce013[] __initconst = {
	0x05, 0xa3, 0x93, 0xed, 0x30, 0xc5, 0xa2, 0x06
};
static const u8 dec_key013[] __initconst = {
	0xb3, 0x35, 0x50, 0x03, 0x54, 0x2e, 0x40, 0x5e,
	0x8f, 0x59, 0x8e, 0xc5, 0x90, 0xd5, 0x27, 0x2d,
	0xba, 0x29, 0x2e, 0xcb, 0x1b, 0x70, 0x44, 0x1e,
	0x65, 0x91, 0x6e, 0x2a, 0x79, 0x22, 0xda, 0x64
};

static const struct chacha20poly1305_testvec
chacha20poly1305_dec_vectors[] __initconst = {
	{ dec_input001, dec_output001, dec_assoc001, dec_nonce001, dec_key001,
	  sizeof(dec_input001), sizeof(dec_assoc001), sizeof(dec_nonce001) },
	{ dec_input002, dec_output002, dec_assoc002, dec_nonce002, dec_key002,
	  sizeof(dec_input002), sizeof(dec_assoc002), sizeof(dec_nonce002) },
	{ dec_input003, dec_output003, dec_assoc003, dec_nonce003, dec_key003,
	  sizeof(dec_input003), sizeof(dec_assoc003), sizeof(dec_nonce003) },
	{ dec_input004, dec_output004, dec_assoc004, dec_nonce004, dec_key004,
	  sizeof(dec_input004), sizeof(dec_assoc004), sizeof(dec_nonce004) },
	{ dec_input005, dec_output005, dec_assoc005, dec_nonce005, dec_key005,
	  sizeof(dec_input005), sizeof(dec_assoc005), sizeof(dec_nonce005) },
	{ dec_input006, dec_output006, dec_assoc006, dec_nonce006, dec_key006,
	  sizeof(dec_input006), sizeof(dec_assoc006), sizeof(dec_nonce006) },
	{ dec_input007, dec_output007, dec_assoc007, dec_nonce007, dec_key007,
	  sizeof(dec_input007), sizeof(dec_assoc007), sizeof(dec_nonce007) },
	{ dec_input008, dec_output008, dec_assoc008, dec_nonce008, dec_key008,
	  sizeof(dec_input008), sizeof(dec_assoc008), sizeof(dec_nonce008) },
	{ dec_input009, dec_output009, dec_assoc009, dec_nonce009, dec_key009,
	  sizeof(dec_input009), sizeof(dec_assoc009), sizeof(dec_nonce009) },
	{ dec_input010, dec_output010, dec_assoc010, dec_nonce010, dec_key010,
	  sizeof(dec_input010), sizeof(dec_assoc010), sizeof(dec_nonce010) },
	{ dec_input011, dec_output011, dec_assoc011, dec_nonce011, dec_key011,
	  sizeof(dec_input011), sizeof(dec_assoc011), sizeof(dec_nonce011) },
	{ dec_input012, dec_output012, dec_assoc012, dec_nonce012, dec_key012,
	  sizeof(dec_input012), sizeof(dec_assoc012), sizeof(dec_nonce012) },
	{ dec_input013, dec_output013, dec_assoc013, dec_nonce013, dec_key013,
	  sizeof(dec_input013), sizeof(dec_assoc013), sizeof(dec_nonce013),
	  true }
};

static const u8 xenc_input001[] __initconst = {
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74,
	0x2d, 0x44, 0x72, 0x61, 0x66, 0x74, 0x73, 0x20,
	0x61, 0x72, 0x65, 0x20, 0x64, 0x72, 0x61, 0x66,
	0x74, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x61, 0x20,
	0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x20,
	0x6f, 0x66, 0x20, 0x73, 0x69, 0x78, 0x20, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x20, 0x61, 0x6e,
	0x64, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65,
	0x20, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x2c, 0x20, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x64, 0x2c, 0x20, 0x6f, 0x72, 0x20, 0x6f,
	0x62, 0x73, 0x6f, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x20, 0x62, 0x79, 0x20, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x61, 0x74, 0x20, 0x61,
	0x6e, 0x79, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x20, 0x49, 0x74, 0x20, 0x69, 0x73, 0x20, 0x69,
	0x6e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x70, 0x72,
	0x69, 0x61, 0x74, 0x65, 0x20, 0x74, 0x6f, 0x20,
	0x75, 0x73, 0x65, 0x20, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x65, 0x74, 0x2d, 0x44, 0x72, 0x61,
	0x66, 0x74, 0x73, 0x20, 0x61, 0x73, 0x20, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x20, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x20, 0x6f, 0x72, 0x20, 0x74, 0x6f, 0x20,
	0x63, 0x69, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65,
	0x6d, 0x20, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x61, 0x73, 0x20,
	0x2f, 0xe2, 0x80, 0x9c, 0x77, 0x6f, 0x72, 0x6b,
	0x20, 0x69, 0x6e, 0x20, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x2f, 0xe2, 0x80,
	0x9d
};
static const u8 xenc_output001[] __initconst = {
	0x1a, 0x6e, 0x3a, 0xd9, 0xfd, 0x41, 0x3f, 0x77,
	0x54, 0x72, 0x0a, 0x70, 0x9a, 0xa0, 0x29, 0x92,
	0x2e, 0xed, 0x93, 0xcf, 0x0f, 0x71, 0x88, 0x18,
	0x7a, 0x9d, 0x2d, 0x24, 0xe0, 0xf5, 0xea, 0x3d,
	0x55, 0x64, 0xd7, 0xad, 0x2a, 0x1a, 0x1f, 0x7e,
	0x86, 0x6d, 0xb0, 0xce, 0x80, 0x41, 0x72, 0x86,
	0x26, 0xee, 0x84, 0xd7, 0xef, 0x82, 0x9e, 0xe2,
	0x60, 0x9d, 0x5a, 0xfc, 0xf0, 0xe4, 0x19, 0x85,
	0xea, 0x09, 0xc6, 0xfb, 0xb3, 0xa9, 0x50, 0x09,
	0xec, 0x5e, 0x11, 0x90, 0xa1, 0xc5, 0x4e, 0x49,
	0xef, 0x50, 0xd8, 0x8f, 0xe0, 0x78, 0xd7, 0xfd,
	0xb9, 0x3b, 0xc9, 0xf2, 0x91, 0xc8, 0x25, 0xc8,
	0xa7, 0x63, 0x60, 0xce, 0x10, 0xcd, 0xc6, 0x7f,
	0xf8, 0x16, 0xf8, 0xe1, 0x0a, 0xd9, 0xde, 0x79,
	0x50, 0x33, 0xf2, 0x16, 0x0f, 0x17, 0xba, 0xb8,
	0x5d, 0xd8, 0xdf, 0x4e, 0x51, 0xa8, 0x39, 0xd0,
	0x85, 0xca, 0x46, 0x6a, 0x10, 0xa7, 0xa3, 0x88,
	0xef, 0x79, 0xb9, 0xf8, 0x24, 0xf3, 0xe0, 0x71,
	0x7b, 0x76, 0x28, 0x46, 0x3a, 0x3a, 0x1b, 0x91,
	0xb6, 0xd4, 0x3e, 0x23, 0xe5, 0x44, 0x15, 0xbf,
	0x60, 0x43, 0x9d, 0xa4, 0xbb, 0xd5, 0x5f, 0x89,
	0xeb, 0xef, 0x8e, 0xfd, 0xdd, 0xb4, 0x0d, 0x46,
	0xf0, 0x69, 0x23, 0x63, 0xae, 0x94, 0xf5, 0x5e,
	0xa5, 0xad, 0x13, 0x1c, 0x41, 0x76, 0xe6, 0x90,
	0xd6, 0x6d, 0xa2, 0x8f, 0x97, 0x4c, 0xa8, 0x0b,
	0xcf, 0x8d, 0x43, 0x2b, 0x9c, 0x9b, 0xc5, 0x58,
	0xa5, 0xb6, 0x95, 0x9a, 0xbf, 0x81, 0xc6, 0x54,
	0xc9, 0x66, 0x0c, 0xe5, 0x4f, 0x6a, 0x53, 0xa1,
	0xe5, 0x0c, 0xba, 0x31, 0xde, 0x34, 0x64, 0x73,
	0x8a, 0x3b, 0xbd, 0x92, 0x01, 0xdb, 0x71, 0x69,
	0xf3, 0x58, 0x99, 0xbc, 0xd1, 0xcb, 0x4a, 0x05,
	0xe2, 0x58, 0x9c, 0x25, 0x17, 0xcd, 0xdc, 0x83,
	0xb7, 0xff, 0xfb, 0x09, 0x61, 0xad, 0xbf, 0x13,
	0x5b, 0x5e, 0xed, 0x46, 0x82, 0x6f, 0x22, 0xd8,
	0x93, 0xa6, 0x85, 0x5b, 0x40, 0x39, 0x5c, 0xc5,
	0x9c
};
static const u8 xenc_assoc001[] __initconst = {
	0xf3, 0x33, 0x88, 0x86, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x4e, 0x91
};
static const u8 xenc_nonce001[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
	0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17
};
static const u8 xenc_key001[] __initconst = {
	0x1c, 0x92, 0x40, 0xa5, 0xeb, 0x55, 0xd3, 0x8a,
	0xf3, 0x33, 0x88, 0x86, 0x04, 0xf6, 0xb5, 0xf0,
	0x47, 0x39, 0x17, 0xc1, 0x40, 0x2b, 0x80, 0x09,
	0x9d, 0xca, 0x5c, 0xbc, 0x20, 0x70, 0x75, 0xc0
};

static const struct chacha20poly1305_testvec
xchacha20poly1305_enc_vectors[] __initconst = {
	{ xenc_input001, xenc_output001, xenc_assoc001, xenc_nonce001, xenc_key001,
	  sizeof(xenc_input001), sizeof(xenc_assoc001), sizeof(xenc_nonce001) }
};

static const u8 xdec_input001[] __initconst = {
	0x1a, 0x6e, 0x3a, 0xd9, 0xfd, 0x41, 0x3f, 0x77,
	0x54, 0x72, 0x0a, 0x70, 0x9a, 0xa0, 0x29, 0x92,
	0x2e, 0xed, 0x93, 0xcf, 0x0f, 0x71, 0x88, 0x18,
	0x7a, 0x9d, 0x2d, 0x24, 0xe0, 0xf5, 0xea, 0x3d,
	0x55, 0x64, 0xd7, 0xad, 0x2a, 0x1a, 0x1f, 0x7e,
	0x86, 0x6d, 0xb0, 0xce, 0x80, 0x41, 0x72, 0x86,
	0x26, 0xee, 0x84, 0xd7, 0xef, 0x82, 0x9e, 0xe2,
	0x60, 0x9d, 0x5a, 0xfc, 0xf0, 0xe4, 0x19, 0x85,
	0xea, 0x09, 0xc6, 0xfb, 0xb3, 0xa9, 0x50, 0x09,
	0xec, 0x5e, 0x11, 0x90, 0xa1, 0xc5, 0x4e, 0x49,
	0xef, 0x50, 0xd8, 0x8f, 0xe0, 0x78, 0xd7, 0xfd,
	0xb9, 0x3b, 0xc9, 0xf2, 0x91, 0xc8, 0x25, 0xc8,
	0xa7, 0x63, 0x60, 0xce, 0x10, 0xcd, 0xc6, 0x7f,
	0xf8, 0x16, 0xf8, 0xe1, 0x0a, 0xd9, 0xde, 0x79,
	0x50, 0x33, 0xf2, 0x16, 0x0f, 0x17, 0xba, 0xb8,
	0x5d, 0xd8, 0xdf, 0x4e, 0x51, 0xa8, 0x39, 0xd0,
	0x85, 0xca, 0x46, 0x6a, 0x10, 0xa7, 0xa3, 0x88,
	0xef, 0x79, 0xb9, 0xf8, 0x24, 0xf3, 0xe0, 0x71,
	0x7b, 0x76, 0x28, 0x46, 0x3a, 0x3a, 0x1b, 0x91,
	0xb6, 0xd4, 0x3e, 0x23, 0xe5, 0x44, 0x15, 0xbf,
	0x60, 0x43, 0x9d, 0xa4, 0xbb, 0xd5, 0x5f, 0x89,
	0xeb, 0xef, 0x8e, 0xfd, 0xdd, 0xb4, 0x0d, 0x46,
	0xf0, 0x69, 0x23, 0x63, 0xae, 0x94, 0xf5, 0x5e,
	0xa5, 0xad, 0x13, 0x1c, 0x41, 0x76, 0xe6, 0x90,
	0xd6, 0x6d, 0xa2, 0x8f, 0x97, 0x4c, 0xa8, 0x0b,
	0xcf, 0x8d, 0x43, 0x2b, 0x9c, 0x9b, 0xc5, 0x58,
	0xa5, 0xb6, 0x95, 0x9a, 0xbf, 0x81, 0xc6, 0x54,
	0xc9, 0x66, 0x0c, 0xe5, 0x4f, 0x6a, 0x53, 0xa1,
	0xe5, 0x0c, 0xba, 0x31, 0xde, 0x34, 0x64, 0x73,
	0x8a, 0x3b, 0xbd, 0x92, 0x01, 0xdb, 0x71, 0x69,
	0xf3, 0x58, 0x99, 0xbc, 0xd1, 0xcb, 0x4a, 0x05,
	0xe2, 0x58, 0x9c, 0x25, 0x17, 0xcd, 0xdc, 0x83,
	0xb7, 0xff, 0xfb, 0x09, 0x61, 0xad, 0xbf, 0x13,
	0x5b, 0x5e, 0xed, 0x46, 0x82, 0x6f, 0x22, 0xd8,
	0x93, 0xa6, 0x85, 0x5b, 0x40, 0x39, 0x5c, 0xc5,
	0x9c
};
static const u8 xdec_output001[] __initconst = {
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74,
	0x2d, 0x44, 0x72, 0x61, 0x66, 0x74, 0x73, 0x20,
	0x61, 0x72, 0x65, 0x20, 0x64, 0x72, 0x61, 0x66,
	0x74, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x61, 0x20,
	0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x20,
	0x6f, 0x66, 0x20, 0x73, 0x69, 0x78, 0x20, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x20, 0x61, 0x6e,
	0x64, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65,
	0x20, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x2c, 0x20, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x64, 0x2c, 0x20, 0x6f, 0x72, 0x20, 0x6f,
	0x62, 0x73, 0x6f, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x20, 0x62, 0x79, 0x20, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x20, 0x61, 0x74, 0x20, 0x61,
	0x6e, 0x79, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x20, 0x49, 0x74, 0x20, 0x69, 0x73, 0x20, 0x69,
	0x6e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x70, 0x72,
	0x69, 0x61, 0x74, 0x65, 0x20, 0x74, 0x6f, 0x20,
	0x75, 0x73, 0x65, 0x20, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x65, 0x74, 0x2d, 0x44, 0x72, 0x61,
	0x66, 0x74, 0x73, 0x20, 0x61, 0x73, 0x20, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x20, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x20, 0x6f, 0x72, 0x20, 0x74, 0x6f, 0x20,
	0x63, 0x69, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65,
	0x6d, 0x20, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x61, 0x73, 0x20,
	0x2f, 0xe2, 0x80, 0x9c, 0x77, 0x6f, 0x72, 0x6b,
	0x20, 0x69, 0x6e, 0x20, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x2f, 0xe2, 0x80,
	0x9d
};
static const u8 xdec_assoc001[] __initconst = {
	0xf3, 0x33, 0x88, 0x86, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x4e, 0x91
};
static const u8 xdec_nonce001[] __initconst = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
	0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17
};
static const u8 xdec_key001[] __initconst = {
	0x1c, 0x92, 0x40, 0xa5, 0xeb, 0x55, 0xd3, 0x8a,
	0xf3, 0x33, 0x88, 0x86, 0x04, 0xf6, 0xb5, 0xf0,
	0x47, 0x39, 0x17, 0xc1, 0x40, 0x2b, 0x80, 0x09,
	0x9d, 0xca, 0x5c, 0xbc, 0x20, 0x70, 0x75, 0xc0
};

static const struct chacha20poly1305_testvec
xchacha20poly1305_dec_vectors[] __initconst = {
	{ xdec_input001, xdec_output001, xdec_assoc001, xdec_nonce001, xdec_key001,
	  sizeof(xdec_input001), sizeof(xdec_assoc001), sizeof(xdec_nonce001) }
};

static void __init
chacha20poly1305_selftest_encrypt_bignonce(u8 *dst, const u8 *src,
					   const size_t src_len, const u8 *ad,
					   const size_t ad_len,
					   const u8 nonce[12],
					   const u8 key[CHACHA20POLY1305_KEY_SIZE])
{
	simd_context_t simd_context;
	struct poly1305_ctx poly1305_state;
	struct chacha20_ctx chacha20_state;
	union {
		u8 block0[POLY1305_KEY_SIZE];
		__le64 lens[2];
	} b = {{ 0 }};

	simd_get(&simd_context);
	chacha20_init(&chacha20_state, key, 0);
	chacha20_state.counter[1] = get_unaligned_le32(nonce + 0);
	chacha20_state.counter[2] = get_unaligned_le32(nonce + 4);
	chacha20_state.counter[3] = get_unaligned_le32(nonce + 8);
	chacha20(&chacha20_state, b.block0, b.block0, sizeof(b.block0),
		 &simd_context);
	poly1305_init(&poly1305_state, b.block0);
	poly1305_update(&poly1305_state, ad, ad_len, &simd_context);
	poly1305_update(&poly1305_state, pad0, (0x10 - ad_len) & 0xf,
			&simd_context);
	chacha20(&chacha20_state, dst, src, src_len, &simd_context);
	poly1305_update(&poly1305_state, dst, src_len, &simd_context);
	poly1305_update(&poly1305_state, pad0, (0x10 - src_len) & 0xf,
			&simd_context);
	b.lens[0] = cpu_to_le64(ad_len);
	b.lens[1] = cpu_to_le64(src_len);
	poly1305_update(&poly1305_state, (u8 *)b.lens, sizeof(b.lens),
			&simd_context);
	poly1305_final(&poly1305_state, dst + src_len, &simd_context);
	simd_put(&simd_context);
	memzero_explicit(&chacha20_state, sizeof(chacha20_state));
	memzero_explicit(&b, sizeof(b));
}

static void __init
chacha20poly1305_selftest_encrypt(u8 *dst, const u8 *src, const size_t src_len,
				  const u8 *ad, const size_t ad_len,
				  const u8 *nonce, const size_t nonce_len,
				  const u8 key[CHACHA20POLY1305_KEY_SIZE])
{
	if (nonce_len == 8)
		chacha20poly1305_encrypt(dst, src, src_len, ad, ad_len,
					 get_unaligned_le64(nonce), key);
	else if (nonce_len == 12)
		chacha20poly1305_selftest_encrypt_bignonce(dst, src, src_len,
							   ad, ad_len, nonce,
							   key);
	else
		BUG();
}

static bool __init
decryption_success(bool func_ret, bool expect_failure, int memcmp_result)
{
	if (expect_failure)
		return !func_ret;
	return func_ret && !memcmp_result;
}

static bool __init chacha20poly1305_selftest(void)
{
	enum { MAXIMUM_TEST_BUFFER_LEN = 1UL << 12 };
	size_t i, j, k, total_len;
	u8 *computed_output = NULL, *input = NULL;
	bool success = true, ret;
	simd_context_t simd_context;
	struct scatterlist sg_src[3];

	computed_output = kmalloc(MAXIMUM_TEST_BUFFER_LEN, GFP_KERNEL);
	input = kmalloc(MAXIMUM_TEST_BUFFER_LEN, GFP_KERNEL);
	if (!computed_output || !input) {
		pr_err("chacha20poly1305 self-test malloc: FAIL\n");
		success = false;
		goto out;
	}

	for (i = 0; i < ARRAY_SIZE(chacha20poly1305_enc_vectors); ++i) {
		memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN);
		chacha20poly1305_selftest_encrypt(computed_output,
					chacha20poly1305_enc_vectors[i].input,
					chacha20poly1305_enc_vectors[i].ilen,
					chacha20poly1305_enc_vectors[i].assoc,
					chacha20poly1305_enc_vectors[i].alen,
					chacha20poly1305_enc_vectors[i].nonce,
					chacha20poly1305_enc_vectors[i].nlen,
					chacha20poly1305_enc_vectors[i].key);
		if (memcmp(computed_output,
			   chacha20poly1305_enc_vectors[i].output,
			   chacha20poly1305_enc_vectors[i].ilen +
							POLY1305_MAC_SIZE)) {
			pr_err("chacha20poly1305 encryption self-test %zu: FAIL\n",
			       i + 1);
			success = false;
		}
	}
	simd_get(&simd_context);
	for (i = 0; i < ARRAY_SIZE(chacha20poly1305_enc_vectors); ++i) {
		if (chacha20poly1305_enc_vectors[i].nlen != 8)
			continue;
		memcpy(computed_output, chacha20poly1305_enc_vectors[i].input,
		       chacha20poly1305_enc_vectors[i].ilen);
		sg_init_one(sg_src, computed_output,
			    chacha20poly1305_enc_vectors[i].ilen +
				POLY1305_MAC_SIZE);
		ret = chacha20poly1305_encrypt_sg_inplace(sg_src,
			chacha20poly1305_enc_vectors[i].ilen,
			chacha20poly1305_enc_vectors[i].assoc,
			chacha20poly1305_enc_vectors[i].alen,
			get_unaligned_le64(chacha20poly1305_enc_vectors[i].nonce),
			chacha20poly1305_enc_vectors[i].key,
			&simd_context);
		if (!ret || memcmp(computed_output,
				   chacha20poly1305_enc_vectors[i].output,
				   chacha20poly1305_enc_vectors[i].ilen +
							POLY1305_MAC_SIZE)) {
			pr_err("chacha20poly1305 sg encryption self-test %zu: FAIL\n",
			       i + 1);
			success = false;
		}
	}
	simd_put(&simd_context);
	for (i = 0; i < ARRAY_SIZE(chacha20poly1305_dec_vectors); ++i) {
		memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN);
		ret = chacha20poly1305_decrypt(computed_output,
			chacha20poly1305_dec_vectors[i].input,
			chacha20poly1305_dec_vectors[i].ilen,
			chacha20poly1305_dec_vectors[i].assoc,
			chacha20poly1305_dec_vectors[i].alen,
			get_unaligned_le64(chacha20poly1305_dec_vectors[i].nonce),
			chacha20poly1305_dec_vectors[i].key);
		if (!decryption_success(ret,
				chacha20poly1305_dec_vectors[i].failure,
				memcmp(computed_output,
				       chacha20poly1305_dec_vectors[i].output,
				       chacha20poly1305_dec_vectors[i].ilen -
							POLY1305_MAC_SIZE))) {
			pr_err("chacha20poly1305 decryption self-test %zu: FAIL\n",
			       i + 1);
			success = false;
		}
	}
	simd_get(&simd_context);
	for (i = 0; i < ARRAY_SIZE(chacha20poly1305_dec_vectors); ++i) {
		memcpy(computed_output, chacha20poly1305_dec_vectors[i].input,
		       chacha20poly1305_dec_vectors[i].ilen);
		sg_init_one(sg_src, computed_output,
			    chacha20poly1305_dec_vectors[i].ilen);
		ret = chacha20poly1305_decrypt_sg_inplace(sg_src,
			chacha20poly1305_dec_vectors[i].ilen,
			chacha20poly1305_dec_vectors[i].assoc,
			chacha20poly1305_dec_vectors[i].alen,
			get_unaligned_le64(chacha20poly1305_dec_vectors[i].nonce),
			chacha20poly1305_dec_vectors[i].key, &simd_context);
		if (!decryption_success(ret,
			chacha20poly1305_dec_vectors[i].failure,
			memcmp(computed_output, chacha20poly1305_dec_vectors[i].output,
			       chacha20poly1305_dec_vectors[i].ilen -
							POLY1305_MAC_SIZE))) {
			pr_err("chacha20poly1305 sg decryption self-test %zu: FAIL\n",
			       i + 1);
			success = false;
		}
	}
	simd_put(&simd_context);
	for (i = 0; i < ARRAY_SIZE(xchacha20poly1305_enc_vectors); ++i) {
		memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN);
		xchacha20poly1305_encrypt(computed_output,
					xchacha20poly1305_enc_vectors[i].input,
					xchacha20poly1305_enc_vectors[i].ilen,
					xchacha20poly1305_enc_vectors[i].assoc,
					xchacha20poly1305_enc_vectors[i].alen,
					xchacha20poly1305_enc_vectors[i].nonce,
					xchacha20poly1305_enc_vectors[i].key);
		if (memcmp(computed_output,
			   xchacha20poly1305_enc_vectors[i].output,
			   xchacha20poly1305_enc_vectors[i].ilen +
							POLY1305_MAC_SIZE)) {
			pr_err("xchacha20poly1305 encryption self-test %zu: FAIL\n",
			       i + 1);
			success = false;
		}
	}
	for (i = 0; i < ARRAY_SIZE(xchacha20poly1305_dec_vectors); ++i) {
		memset(computed_output, 0, MAXIMUM_TEST_BUFFER_LEN);
		ret = xchacha20poly1305_decrypt(computed_output,
					xchacha20poly1305_dec_vectors[i].input,
					xchacha20poly1305_dec_vectors[i].ilen,
					xchacha20poly1305_dec_vectors[i].assoc,
					xchacha20poly1305_dec_vectors[i].alen,
					xchacha20poly1305_dec_vectors[i].nonce,
					xchacha20poly1305_dec_vectors[i].key);
		if (!decryption_success(ret,
				xchacha20poly1305_dec_vectors[i].failure,
				memcmp(computed_output,
				       xchacha20poly1305_dec_vectors[i].output,
				       xchacha20poly1305_dec_vectors[i].ilen -
							POLY1305_MAC_SIZE))) {
			pr_err("xchacha20poly1305 decryption self-test %zu: FAIL\n",
			       i + 1);
			success = false;
		}
	}

	simd_get(&simd_context);
	for (total_len = POLY1305_MAC_SIZE; IS_ENABLED(DEBUG_CHACHA20POLY1305_SLOW_CHUNK_TEST)
	     && total_len <= 1 << 10; ++total_len) {
		for (i = 0; i <= total_len; ++i) {
			for (j = i; j <= total_len; ++j) {
				sg_init_table(sg_src, 3);
				sg_set_buf(&sg_src[0], input, i);
				sg_set_buf(&sg_src[1], input + i, j - i);
				sg_set_buf(&sg_src[2], input + j, total_len - j);
				memset(computed_output, 0, total_len);
				memset(input, 0, total_len);

				if (!chacha20poly1305_encrypt_sg_inplace(sg_src,
					total_len - POLY1305_MAC_SIZE, NULL, 0,
					0, enc_key001, &simd_context))
					goto chunkfail;
				chacha20poly1305_encrypt(computed_output,
					computed_output,
					total_len - POLY1305_MAC_SIZE, NULL, 0, 0,
					enc_key001);
				if (memcmp(computed_output, input, total_len))
					goto chunkfail;;
				if (!chacha20poly1305_decrypt(computed_output,
					input, total_len, NULL, 0, 0, enc_key001))
					goto chunkfail;
				for (k = 0; k < total_len - POLY1305_MAC_SIZE; ++k) {
					if (computed_output[k])
						goto chunkfail;
				}
				if (!chacha20poly1305_decrypt_sg_inplace(sg_src,
					total_len, NULL, 0, 0, enc_key001,
					&simd_context))
					goto chunkfail;
				for (k = 0; k < total_len - POLY1305_MAC_SIZE; ++k) {
					if (input[k])
						goto chunkfail;
				}
				continue;

			chunkfail:
				pr_err("chacha20poly1305 chunked self-test %zu/%zu/%zu: FAIL\n",
				       total_len, i, j);
				success = false;
			}

		}
	}
	simd_put(&simd_context);

out:
	kfree(computed_output);
	kfree(input);
	return success;
}
