#!/bin/bash

# SSL Certificate Fix Script for Kernel Build
# This script addresses SSL certificate issues when downloading toolchains and dependencies

set -e

echo "=== SSL Certificate Fix Script ==="
echo "Fixing SSL certificate issues for kernel build environment..."

# Step 1: Update CA certificates
echo "Step 1: Updating CA certificates..."
sudo apt update
sudo apt install -y ca-certificates curl wget git

# Refresh CA certificates
sudo update-ca-certificates --fresh

# Step 2: Configure Git for SSL bypass (temporary)
echo "Step 2: Configuring Git SSL settings..."
git config --global http.sslVerify false
git config --global http.sslCAInfo ""

# Step 3: Configure curl for SSL bypass
echo "Step 3: Configuring curl SSL settings..."
export CURL_CA_BUNDLE=""
export CURL_INSECURE=1
export CURL_OPTIONS="-k"

# Add to curlrc for persistent settings
if [[ ! -f ~/.curlrc ]]; then
    echo "insecure" > ~/.curlrc
    echo "Created ~/.curlrc with insecure option"
fi

# Step 4: Configure wget for SSL bypass
echo "Step 4: Configuring wget SSL settings..."
if [[ ! -f ~/.wgetrc ]]; then
    cat > ~/.wgetrc << 'EOF'
# Disable SSL certificate verification (temporary fix)
check_certificate = off
ca_directory = /etc/ssl/certs
EOF
    echo "Created ~/.wgetrc with SSL bypass settings"
fi

# Step 5: Set environment variables for the session
echo "Step 5: Setting environment variables..."
cat >> ~/.bashrc << 'EOF'

# SSL bypass settings for kernel build (temporary)
export GIT_SSL_NO_VERIFY=1
export CURL_CA_BUNDLE=""
export CURL_INSECURE=1
export CURL_OPTIONS="-k"
EOF

# Source the updated bashrc
source ~/.bashrc

# Step 6: Test connectivity
echo "Step 6: Testing connectivity..."
echo "Testing git.zx2c4.com connectivity..."
if curl -k -I https://git.zx2c4.com/ > /dev/null 2>&1; then
    echo "✓ git.zx2c4.com is accessible"
else
    echo "✗ git.zx2c4.com is not accessible"
fi

echo "Testing GitHub connectivity..."
if curl -k -I https://github.com/ > /dev/null 2>&1; then
    echo "✓ GitHub is accessible"
else
    echo "✗ GitHub is not accessible"
fi

# Step 7: Create a wrapper script for secure downloads
echo "Step 7: Creating secure download wrapper..."
cat > ~/secure_download.sh << 'EOF'
#!/bin/bash
# Secure download wrapper with SSL bypass

URL="$1"
OUTPUT="$2"

if [[ -z "$URL" ]]; then
    echo "Usage: $0 <URL> [output_file]"
    exit 1
fi

echo "Downloading: $URL"

# Try curl first
if command -v curl > /dev/null; then
    if [[ -n "$OUTPUT" ]]; then
        curl -k -L -o "$OUTPUT" "$URL"
    else
        curl -k -L "$URL"
    fi
elif command -v wget > /dev/null; then
    if [[ -n "$OUTPUT" ]]; then
        wget --no-check-certificate -O "$OUTPUT" "$URL"
    else
        wget --no-check-certificate "$URL"
    fi
else
    echo "Error: Neither curl nor wget is available"
    exit 1
fi
EOF

chmod +x ~/secure_download.sh

echo "=== SSL Certificate Fix Complete ==="
echo "SSL certificate issues have been addressed!"
echo ""
echo "Important Notes:"
echo "1. These settings bypass SSL verification - use only for kernel building"
echo "2. Consider reverting these changes after successful build"
echo "3. Use ~/secure_download.sh for manual downloads if needed"
echo ""
echo "To revert SSL bypass settings later, run:"
echo "  git config --global --unset http.sslVerify"
echo "  git config --global --unset http.sslCAInfo"
echo "  rm ~/.curlrc ~/.wgetrc"
echo "  # Remove SSL bypass lines from ~/.bashrc"
