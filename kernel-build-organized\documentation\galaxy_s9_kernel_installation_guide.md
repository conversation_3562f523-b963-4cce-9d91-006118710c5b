# Galaxy S9 Kernel Installation Guide
## ArtPlus-NEXT-G960F-250827-Permissive-KSU

🎉 **BUILD SUCCESSFUL!** Your custom kernel is ready for installation.

---

## 📋 Kernel Summary

**Kernel Name**: `ArtPlus-NEXT-G960F-250827-Permissive-KSU`
- **Device**: Samsung Galaxy S9 International (SM-G960F) **ONLY**
- **Build Date**: August 27, 2025
- **KernelSU**: Version 12030 (Root solution)
- **SELinux**: Permissive mode
- **Compiler**: Clang 19.0.0

**Build Location**: `~/kernel-build-wsl/AnyKernel3/ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip`

---

## ⚠️ CRITICAL REQUIREMENTS

### Before You Start
- ✅ **Exact Model**: SM-G960F (International Galaxy S9) **ONLY**
- ✅ **Unlocked Bootloader** (voids warranty)
- ✅ **Custom Recovery** (TWRP recommended)
- ✅ **Full Device Backup** (NANDroid backup)
- ✅ **Stock firmware** downloaded (for recovery)

### ❌ DO NOT INSTALL IF:
- Your device is NOT SM-G960F
- Bootloader is locked
- You don't have custom recovery
- You haven't made a backup

---

## 🚀 Installation Steps

### Method 1: TWRP Recovery (Recommended)

#### Step 1: Prepare Files
```bash
# Copy kernel ZIP to device storage
cp ~/kernel-build-wsl/AnyKernel3/ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip /path/to/device/
```

#### Step 2: Boot to TWRP
- Power off device completely
- Hold: **Volume Up + Bixby + Power**
- Release when Samsung logo appears

#### Step 3: Install Kernel
1. In TWRP: **Install** → Select ZIP file
2. Choose: `ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip`
3. **Swipe to confirm flash**
4. Wait for completion
5. **Reboot System**

#### Step 4: First Boot
- First boot takes 5-10 minutes
- Device may restart once during boot
- Be patient!

---

## ✅ Verification

### Check Kernel Version
```bash
adb shell cat /proc/version
# Should show: ArtPlus-NEXT-G960F-250827-Permissive-KSU
```

### Install KernelSU Manager
1. Download: https://github.com/tiann/KernelSU/releases
2. Install: `KernelSU_v0.x.x.apk`
3. Open app → Should show "Working" status

### Test Basic Functions
- ✅ Device boots normally
- ✅ WiFi/Mobile data works
- ✅ Camera functions
- ✅ Calls/SMS work
- ✅ Fingerprint/Face unlock

---

## 🔧 Troubleshooting

### Boot Loop (Stuck on Samsung Logo)
**Solution 1**: Flash stock kernel via ODIN
**Solution 2**: Wipe cache in TWRP

### KernelSU Not Working
1. Verify kernel: `adb shell cat /proc/version | grep KSU`
2. Reinstall KernelSU Manager app
3. Check root permissions in app

### Performance Issues
1. Check SELinux: `adb shell getenforce` (should be "Permissive")
2. Monitor logs: `adb shell dmesg | grep -i error`
3. Try different CPU governor

---

## 🛡️ Recovery Options

### If Something Goes Wrong
1. **Stock Firmware**: Download from SamMobile
2. **ODIN Flash**: Use stock TAR files
3. **TWRP Restore**: Use NANDroid backup

### Emergency Recovery
- **Download Mode**: Volume Down + Bixby + Power
- **Recovery Mode**: Volume Up + Bixby + Power
- **Safe Mode**: Volume Down during boot

---

## 🎯 What You Get

### Features Enabled
- **Root Access**: Via KernelSU (safer than Magisk)
- **Module Support**: Install compatible modules
- **Performance Tuning**: Custom governors and schedulers
- **Security**: Latest patches with permissive SELinux
- **Compatibility**: Works with most ROMs

### KernelSU Advantages
- **Systemless**: Doesn't modify system partition
- **Module Support**: Install root modules
- **App Management**: Per-app root permissions
- **Safety**: Easy to uninstall

---

## 📱 Post-Installation

### Recommended Apps
1. **KernelSU Manager**: Root management
2. **CPU-Z**: Verify kernel info
3. **Franco Kernel Manager**: Performance tuning
4. **Root Checker**: Verify root access

### Performance Optimization
- **CPU Governor**: Try "performance" or "ondemand"
- **I/O Scheduler**: "deadline" or "cfq" for better performance
- **Thermal Management**: Monitor temperatures

---

## 🔄 Updates & Maintenance

### Kernel Updates
- Check GitHub for newer versions
- Always backup before updating
- Follow same installation process

### Module Management
- Use KernelSU Manager for modules
- Only install trusted modules
- Test one module at a time

---

## 📞 Support

### If You Need Help
- **XDA Forums**: Galaxy S9 development section
- **GitHub Issues**: Report kernel bugs
- **Telegram**: KernelSU support groups

### Before Asking for Help
1. Provide exact device model (SM-G960F)
2. Share kernel version output
3. Describe exact steps taken
4. Include error messages/logs

---

## 🎉 Success!

**Congratulations!** You now have:
- ✅ Custom kernel with latest optimizations
- ✅ Root access via KernelSU
- ✅ Enhanced performance and features
- ✅ Full control over your Galaxy S9

**Enjoy your rooted Galaxy S9 with custom kernel!** 🚀

---

## 📝 Quick Reference

**Kernel File**: `ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip`  
**Device**: SM-G960F only  
**Method**: TWRP flash  
**Root**: KernelSU v12030  
**SELinux**: Permissive  

**Remember**: Always backup before modifications!
