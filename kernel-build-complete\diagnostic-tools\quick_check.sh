#!/bin/bash
# Quick system check before building
echo "🔍 Quick Build Requirements Check"
echo ""

# Check essential tools
TOOLS=("make" "gcc" "clang" "tar" "xz" "aarch64-linux-gnu-gcc")
MISSING=0

for tool in "${TOOLS[@]}"; do
    if command -v "$tool" > /dev/null 2>&1; then
        echo "✅ $tool"
    else
        echo "❌ $tool - MISSING"
        ((MISSING++))
    fi
done

echo ""
if [[ $MISSING -eq 0 ]]; then
    echo "🎉 All essential tools available!"
    echo "Run: ./comprehensive_build_diagnostic.sh for detailed analysis"
else
    echo "⚠️  $MISSING tools missing"
    echo "Run: sudo apt install build-essential gcc-aarch64-linux-gnu xz-utils"
fi
