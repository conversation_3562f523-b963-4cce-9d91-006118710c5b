# Step-by-Step Galaxy S9 Kernel Build Guide

## Phase 1: System Preparation

### Step 1: Run Comprehensive Diagnostic
```bash
cd /mnt/c/Sviluppo/kernelsu-galaxys9/kernel-build-complete/diagnostic-tools/
./comprehensive_build_diagnostic.sh
```

### Step 2: Install Missing Tools
Based on diagnostic results, install missing packages:
```bash
sudo apt update
sudo apt install -y build-essential gcc-aarch64-linux-gnu gcc-arm-linux-gnueabihf \
    xz-utils tar gzip bison flex bc python3 perl rsync cpio git curl wget clang
```

### Step 3: Verify Installation
```bash
./quick_check.sh
```

## Phase 2: Kernel Build

### Method 1: Apollo Script (Recommended)
```bash
cd ~/kernel-build-wsl
export ANDROID_MAJOR_VERSION=q
./apollo.sh

# Select:
# 1) starlte (Galaxy S9)
# 4) Clang 19
# 1) SELinux Permissive
# y) Enable KernelSU
# y) Clean build
```

## Phase 3: Installation

### Prerequisites
- Samsung Galaxy S9 International (SM-G960F) **ONLY**
- Unlocked bootloader
- TWRP recovery installed
- Full device backup

### Installation Steps
1. Copy kernel ZIP to device storage
2. Boot to TWRP recovery
3. Install → Select ZIP file
4. Swipe to confirm flash
5. Reboot system
6. Install KernelSU Manager APK
