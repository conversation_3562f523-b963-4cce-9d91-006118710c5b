#!/bin/bash
# Fix remaining defconfig issues after WSL fixes have been applied

set -e

echo "========================================"
echo "  Defconfig Issues Fix Script"
echo "========================================"
echo ""

# Check we're in the right directory
if [[ ! -f "Makefile" ]] || [[ ! -d "arch" ]]; then
    echo "❌ Error: Run this from the kernel root directory"
    exit 1
fi

echo "Working in: $(pwd)"
echo ""

# Set environment variables
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export ANDROID_MAJOR_VERSION=q

echo "Environment set:"
echo "  ARCH=$ARCH"
echo "  CROSS_COMPILE=$CROSS_COMPILE" 
echo "  ANDROID_MAJOR_VERSION=$ANDROID_MAJOR_VERSION"
echo ""

# Step 1: Install any missing dependencies
echo "=== Step 1: Installing Additional Dependencies ==="
sudo apt update
sudo apt install -y python3 python3-dev perl libncurses5-dev libncursesw5-dev

echo "✓ Additional dependencies installed"
echo ""

# Step 2: Clean any previous build artifacts
echo "=== Step 2: Cleaning Previous Build Artifacts ==="
make clean > /dev/null 2>&1 || true
make mrproper > /dev/null 2>&1 || true

echo "✓ Build artifacts cleaned"
echo ""

# Step 3: Build kconfig tools first
echo "=== Step 3: Building Kconfig Tools ==="
echo "Building configuration tools..."

if make -C scripts/kconfig conf > /dev/null 2>&1; then
    echo "✓ Kconfig tools built successfully"
else
    echo "❌ Failed to build kconfig tools, trying with verbose output:"
    make -C scripts/kconfig conf
    echo ""
    echo "Attempting to continue anyway..."
fi
echo ""

# Step 4: Fix any remaining Kbuild.include issues
echo "=== Step 4: Checking Kbuild.include ==="

# Check if the fetch scripts are causing issues
if grep -q "fetch-latest-wireguard.sh" scripts/Kbuild.include; then
    echo "Found WireGuard fetch reference in Kbuild.include"
    
    # Create a backup
    cp scripts/Kbuild.include scripts/Kbuild.include.backup
    
    # Comment out the problematic lines
    sed -i 's/.*fetch-latest-wireguard.sh.*/#&/' scripts/Kbuild.include
    sed -i 's/.*fetch-latest-kernelsu.sh.*/#&/' scripts/Kbuild.include
    
    echo "✓ Commented out fetch scripts in Kbuild.include"
else
    echo "✓ No problematic fetch scripts found in Kbuild.include"
fi
echo ""

# Step 5: Verify WireGuard setup
echo "=== Step 5: Verifying WireGuard Setup ==="
if [[ -f "net/wireguard/Kconfig" ]]; then
    echo "✓ WireGuard Kconfig exists"
else
    echo "❌ WireGuard Kconfig missing, recreating..."
    mkdir -p net/wireguard
    cat > net/wireguard/Kconfig << 'WGEOF'
# SPDX-License-Identifier: GPL-2.0
config WIREGUARD
    tristate "WireGuard secure network tunnel"
    depends on NET && INET
    default m
    help
      WireGuard is a secure, fast, and easy to use replacement for IPSec.
WGEOF
    echo "✓ WireGuard Kconfig recreated"
fi

# Ensure .check file exists
touch net/wireguard/.check
echo ""

# Step 6: Try defconfig with different approaches
echo "=== Step 6: Testing Defconfig Generation ==="

DEFCONFIGS=("exynos9810_defconfig" "starlte_defconfig" "star2lte_defconfig" "crownlte_defconfig")
SUCCESS=false

for config in "${DEFCONFIGS[@]}"; do
    echo "Testing $config..."
    
    # Method 1: Direct make
    if make $config > /dev/null 2>&1; then
        echo "✅ $config successful with direct make!"
        SUCCESS=true
        WORKING_CONFIG=$config
        break
    fi
    
    # Method 2: Make with explicit arch
    if make ARCH=arm64 $config > /dev/null 2>&1; then
        echo "✅ $config successful with explicit ARCH!"
        SUCCESS=true
        WORKING_CONFIG=$config
        break
    fi
    
    # Method 3: Make with all explicit variables
    if make ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- $config > /dev/null 2>&1; then
        echo "✅ $config successful with all explicit variables!"
        SUCCESS=true
        WORKING_CONFIG=$config
        break
    fi
    
    echo "❌ $config failed with all methods"
done

echo ""

# Step 7: If still failing, try manual kconfig approach
if [[ $SUCCESS == false ]]; then
    echo "=== Step 7: Manual Kconfig Approach ==="
    echo "All standard methods failed, trying manual approach..."
    
    # Try to run kconfig directly
    if [[ -f "scripts/kconfig/conf" ]]; then
        echo "Trying manual kconfig execution..."
        
        for config in "${DEFCONFIGS[@]}"; do
            if [[ -f "arch/arm64/configs/$config" ]]; then
                echo "Attempting manual config for $config..."
                
                # Try direct kconfig execution
                if scripts/kconfig/conf --defconfig=arch/arm64/configs/$config Kconfig > /dev/null 2>&1; then
                    echo "✅ Manual kconfig successful for $config!"
                    SUCCESS=true
                    WORKING_CONFIG=$config
                    break
                fi
            fi
        done
    fi
fi

echo ""
echo "========================================"
echo "  Fix Results"
echo "========================================"
echo ""

if [[ $SUCCESS == true ]]; then
    echo "🎉 Defconfig fix successful!"
    echo ""
    echo "Working configuration: $WORKING_CONFIG"
    echo "Kernel source location: $(pwd)"
    echo ""
    echo "To build the kernel:"
    echo "  export ARCH=arm64"
    echo "  export CROSS_COMPILE=aarch64-linux-gnu-"
    echo "  export ANDROID_MAJOR_VERSION=q"
    echo "  make -j\$(nproc)"
    echo ""
    echo "Or use the apollo.sh script:"
    echo "  ./apollo.sh"
    
    # Test if we can proceed to actual build
    echo ""
    echo "Testing build preparation..."
    if make prepare > /dev/null 2>&1; then
        echo "✅ Build preparation successful - ready to compile!"
    else
        echo "⚠️  Build preparation had issues, but defconfig works"
    fi
    
else
    echo "❌ Defconfig issues persist"
    echo ""
    echo "This suggests deeper kernel configuration problems."
    echo "Try the apollo.sh script which may have custom configuration:"
    echo "  ./apollo.sh"
fi

echo ""
echo "Applied fixes:"
echo "✓ Built kconfig tools"
echo "✓ Cleaned build artifacts"
echo "✓ Fixed Kbuild.include fetch script issues"
echo "✓ Verified WireGuard setup"
echo "✓ Tested multiple defconfig approaches"
