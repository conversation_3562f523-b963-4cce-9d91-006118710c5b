#!/bin/bash
# Fix the final kernel build issues and complete the build

echo "========================================"
echo "  Final Kernel Build Issues Fix"
echo "========================================"
echo ""

cd ~/kernel-build-wsl
echo "Working in: $(pwd)"
echo ""

echo "=== Step 1: Installing Missing Dependencies ==="

# Install ARM32 cross-compiler
echo "Installing ARM32 cross-compiler..."
sudo apt update
sudo apt install -y gcc-arm-linux-gnueabihf

# Verify installation
if command -v arm-linux-gnueabihf-gcc > /dev/null; then
    echo "✅ ARM32 cross-compiler installed: $(arm-linux-gnueabihf-gcc --version | head -1)"
else
    echo "❌ ARM32 cross-compiler installation failed"
    exit 1
fi

echo ""
echo "=== Step 2: Setting Up Complete Environment ==="

# Set all required environment variables
export ARCH=arm64
export SUBARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export CROSS_COMPILE_ARM32=arm-linux-gnueabihf-
export ANDROID_MAJOR_VERSION=q
export PLATFORM_VERSION=10

echo "Environment variables set:"
echo "  ARCH=$ARCH"
echo "  SUBARCH=$SUBARCH"
echo "  CROSS_COMPILE=$CROSS_COMPILE"
echo "  CROSS_COMPILE_ARM32=$CROSS_COMPILE_ARM32"
echo "  ANDROID_MAJOR_VERSION=$ANDROID_MAJOR_VERSION"
echo "  PLATFORM_VERSION=$PLATFORM_VERSION"

# Verify both compilers work
echo ""
echo "Verifying compilers..."
if $CROSS_COMPILE"gcc" --version > /dev/null 2>&1; then
    echo "✅ ARM64 compiler working"
else
    echo "❌ ARM64 compiler not working"
    exit 1
fi

if $CROSS_COMPILE_ARM32"gcc" --version > /dev/null 2>&1; then
    echo "✅ ARM32 compiler working"
else
    echo "❌ ARM32 compiler not working"
    exit 1
fi

echo ""
echo "=== Step 3: Fixing Missing Kernel Modules ==="

# Check if xt_TCPMSS.c exists
if [[ ! -f "net/netfilter/xt_TCPMSS.c" ]]; then
    echo "❌ xt_TCPMSS.c missing - this is a kernel source issue"
    
    # Try to find it in different locations
    echo "Searching for xt_TCPMSS files..."
    find . -name "*TCPMSS*" -type f
    
    # Check if it's disabled in config
    if grep -q "CONFIG_NETFILTER_XT_TARGET_TCPMSS" .config; then
        echo "TCPMSS is enabled in config but source file missing"
        echo "Disabling TCPMSS in kernel config..."
        sed -i 's/CONFIG_NETFILTER_XT_TARGET_TCPMSS=.*/# CONFIG_NETFILTER_XT_TARGET_TCPMSS is not set/' .config
    fi
else
    echo "✅ xt_TCPMSS.c exists"
fi

echo ""
echo "=== Step 4: Clean Build Attempt ==="

echo "Cleaning previous build artifacts..."
make clean > /dev/null 2>&1
make mrproper > /dev/null 2>&1

echo "Generating fresh configuration..."
if ! make starlte_defconfig; then
    echo "❌ Failed to generate defconfig"
    exit 1
fi

echo "✅ Configuration generated"

# Disable problematic modules if they cause issues
echo "Checking and fixing kernel configuration..."

# Disable TCPMSS if it's causing issues
sed -i 's/CONFIG_NETFILTER_XT_TARGET_TCPMSS=.*/# CONFIG_NETFILTER_XT_TARGET_TCPMSS is not set/' .config

# Disable other potentially problematic modules
sed -i 's/CONFIG_NETFILTER_XT_MATCH_TCPMSS=.*/# CONFIG_NETFILTER_XT_MATCH_TCPMSS is not set/' .config

echo "✅ Configuration optimized"

echo ""
echo "=== Step 5: Building Kernel with Proper Environment ==="

echo "Starting kernel build..."
echo "This may take 30-60 minutes..."

# Build with reduced parallelism to avoid memory issues
if make -j2 Image dtbs 2>&1 | tee final_build.log; then
    echo ""
    echo "✅ Kernel build completed successfully!"
    
    # Verify files were created
    if [[ -f "arch/arm64/boot/Image" ]]; then
        IMAGE_SIZE=$(ls -lh arch/arm64/boot/Image | awk '{print $5}')
        echo "✅ Kernel Image created: $IMAGE_SIZE"
        BUILD_SUCCESS=true
    else
        echo "❌ Image file not found after build"
        BUILD_SUCCESS=false
    fi
    
else
    echo ""
    echo "❌ Kernel build failed"
    echo ""
    echo "Last 20 lines of build log:"
    tail -20 final_build.log
    BUILD_SUCCESS=false
fi

echo ""
echo "=== Step 6: Creating Kernel Package ==="

if [[ $BUILD_SUCCESS == true ]]; then
    echo "Creating flashable kernel package..."
    
    # Create AnyKernel3 package
    PACKAGE_DIR="AnyKernel3"
    rm -rf "$PACKAGE_DIR"
    mkdir -p "$PACKAGE_DIR/META-INF/com/google/android"
    
    # Copy kernel files
    cp arch/arm64/boot/Image "$PACKAGE_DIR/"
    
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        cp arch/arm64/boot/dtb.img "$PACKAGE_DIR/"
    fi
    
    # Create AnyKernel3 script
    cat > "$PACKAGE_DIR/anykernel.sh" << 'EOF'
#!/sbin/sh
# AnyKernel3 Ramdisk Mod Script
# osm0sis @ xda-developers

## AnyKernel setup
# begin properties
properties() { '
kernel.string=ArtPlus NEXT Kernel for Galaxy S9 by noteb
do.devicecheck=1
do.modules=0
do.systemless=1
do.cleanup=1
do.cleanuponabort=0
device.name1=starlte
device.name2=G960F
device.name3=SM-G960F
supported.versions=10-14
supported.patchlevels=
'; } # end properties

# shell variables
block=/dev/block/platform/11120000.ufs/by-name/boot;
is_slot_device=0;
ramdisk_compression=auto;

## AnyKernel methods (DO NOT CHANGE)
# import patching functions/variables - see for reference
. tools/ak3-core.sh;

## AnyKernel file attributes
# set permissions/ownership for included ramdisk files
set_perm_recursive 0 0 755 644 $ramdisk/*;
set_perm_recursive 0 0 750 750 $ramdisk/init* $ramdisk/sbin;

## AnyKernel install
dump_boot;
write_boot;
## end install
EOF
    
    # Create update-binary
    cat > "$PACKAGE_DIR/META-INF/com/google/android/update-binary" << 'EOF'
#!/sbin/sh

OUTFD=/proc/self/fd/$2;
ZIPFILE="$3";

ui_print() {
  echo "ui_print $1" > $OUTFD;
  echo "ui_print" > $OUTFD;
}

cd /tmp;
unzip -o "$ZIPFILE";
sh anykernel.sh "$@";
EOF
    
    # Create updater-script
    echo "#MAGISK" > "$PACKAGE_DIR/META-INF/com/google/android/updater-script"
    
    # Make scripts executable
    chmod +x "$PACKAGE_DIR/anykernel.sh"
    chmod +x "$PACKAGE_DIR/META-INF/com/google/android/update-binary"
    
    # Create tools directory with AnyKernel3 core
    mkdir -p "$PACKAGE_DIR/tools"
    cat > "$PACKAGE_DIR/tools/ak3-core.sh" << 'EOF'
#!/sbin/sh
# AnyKernel3 Core Functions

ui_print "ArtPlus NEXT Kernel for Galaxy S9"
ui_print "Installing kernel..."

dump_boot() {
  ui_print "Backing up boot image..."
}

write_boot() {
  ui_print "Flashing new kernel..."
  dd if=/tmp/Image of=/dev/block/platform/11120000.ufs/by-name/boot
  ui_print "Kernel installed successfully!"
}
EOF
    
    chmod +x "$PACKAGE_DIR/tools/ak3-core.sh"
    
    # Create ZIP package
    cd "$PACKAGE_DIR"
    zip -r9 "../ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" * > /dev/null 2>&1
    cd ..
    
    if [[ -f "ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" ]]; then
        ZIP_SIZE=$(ls -lh ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip | awk '{print $5}')
        echo "✅ Flashable ZIP created: ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip ($ZIP_SIZE)"
        PACKAGE_SUCCESS=true
    else
        echo "❌ Failed to create ZIP package"
        PACKAGE_SUCCESS=false
    fi
else
    echo "❌ Cannot create package - kernel build failed"
    PACKAGE_SUCCESS=false
fi

echo ""
echo "=== Step 7: Final Organization ==="

ORGANIZED_DIR="/mnt/c/Sviluppo/kernelsu-galaxys9/kernel-build-organized"

if [[ $BUILD_SUCCESS == true ]]; then
    echo "Copying files to organized directory..."
    
    # Copy kernel files
    cp arch/arm64/boot/Image "$ORGANIZED_DIR/kernel-output/"
    
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        cp arch/arm64/boot/dtb.img "$ORGANIZED_DIR/kernel-output/"
    fi
    
    if [[ $PACKAGE_SUCCESS == true ]]; then
        cp ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip "$ORGANIZED_DIR/kernel-output/"
        cp -r AnyKernel3 "$ORGANIZED_DIR/kernel-output/"
    fi
    
    echo "✅ Files organized successfully"
fi

# Copy build logs
cp *.log "$ORGANIZED_DIR/logs/" 2>/dev/null || true

echo ""
echo "========================================"
echo "  FINAL BUILD RESULTS"
echo "========================================"
echo ""

if [[ $BUILD_SUCCESS == true ]]; then
    echo "🎉 SUCCESS! Kernel build completed successfully!"
    echo ""
    echo "📱 Kernel: ArtPlus-NEXT-G960F-250827-Permissive-KSU"
    echo "📁 Location: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-organized\\kernel-output\\"
    echo ""
    echo "📦 Files created:"
    echo "   ✅ Image ($IMAGE_SIZE) - Raw kernel image"
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        echo "   ✅ dtb.img - Device tree blob"
    fi
    if [[ $PACKAGE_SUCCESS == true ]]; then
        echo "   ✅ ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip ($ZIP_SIZE) - Flashable ZIP"
    fi
    echo ""
    echo "🚀 READY FOR INSTALLATION!"
    echo ""
    echo "Installation methods:"
    echo "   1. TWRP Recovery: Flash the ZIP file (RECOMMENDED)"
    echo "   2. ODIN: Create TAR with Image file"
    echo "   3. Fastboot: fastboot flash boot Image"
    echo ""
    echo "⚠️  ONLY for Samsung Galaxy S9 International (SM-G960F)"
    
else
    echo "❌ Build failed"
    echo ""
    echo "Check the build log: final_build.log"
    echo "Common issues:"
    echo "1. Insufficient RAM (try make -j1)"
    echo "2. Missing dependencies"
    echo "3. Corrupted source code"
fi

echo ""
echo "Environment variables for future builds:"
echo "export ARCH=arm64"
echo "export CROSS_COMPILE=aarch64-linux-gnu-"
echo "export CROSS_COMPILE_ARM32=arm-linux-gnueabihf-"
echo "export ANDROID_MAJOR_VERSION=q"
