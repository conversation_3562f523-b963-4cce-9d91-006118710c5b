#!/bin/bash

# Navigate to your kernel build directory
cd ~/kernel-build-wsl || {
    echo "❌ Directory ~/kernel-build-wsl not found!"
    exit 1
}

# Create the diagnostic script
cat > check_build_requirements.sh << 'EOF'
cd ~/kernel-build-wsl

# Create diagnostic script first
cat > check_build_requirements.sh << 'EOF'
#!/bin/bash
echo "========================================"
echo "  🔍 KERNEL BUILD REQUIREMENTS CHECK"
echo "========================================"
echo ""

echo "=== Step 1: Checking System Tools ==="
echo ""

# List of required tools for kernel building
REQUIRED_TOOLS=(
    "make"
    "gcc" 
    "clang"
    "tar"
    "xz"
    "gzip"
    "bison"
    "flex"
    "bc"
    "python3"
    "perl"
    "rsync"
    "cpio"
    "aarch64-linux-gnu-gcc"
    "arm-linux-gnueabihf-gcc"
)

echo "📋 Checking required tools:"
MISSING_TOOLS=()
AVAILABLE_TOOLS=()

for tool in "${REQUIRED_TOOLS[@]}"; do
    if command -v "$tool" > /dev/null 2>&1; then
        VERSION=$(command -v "$tool" && $tool --version 2>/dev/null | head -1 || echo "version unknown")
        echo "  ✅ $tool: $(command -v $tool)"
        AVAILABLE_TOOLS+=("$tool")
    else
        echo "  ❌ $tool: NOT FOUND"
        MISSING_TOOLS+=("$tool")
    fi
done

echo ""
echo "📊 Summary:"
echo "  ✅ Available tools: ${#AVAILABLE_TOOLS[@]}"
echo "  ❌ Missing tools: ${#MISSING_TOOLS[@]}"

if [[ ${#MISSING_TOOLS[@]} -gt 0 ]]; then
    echo ""
    echo "🔧 Tools that need to be installed:"
    for tool in "${MISSING_TOOLS[@]}"; do
        echo "  - $tool"
    done
fi

echo ""
echo "=== Step 2: Checking Clang Compiler ==="
echo ""

CLANG_PATH="../compiler/clang-19.0.0-r530567"
if [[ -d "$CLANG_PATH" ]]; then
    echo "✅ Clang compiler directory found: $CLANG_PATH"
    
    if [[ -f "$CLANG_PATH/bin/clang" ]]; then
        CLANG_VERSION=$("$CLANG_PATH/bin/clang" --version | head -1)
        echo "✅ Clang executable found: $CLANG_VERSION"
        CLANG_AVAILABLE=true
    else
        echo "❌ Clang executable not found in $CLANG_PATH/bin/"
        CLANG_AVAILABLE=false
    fi
else
    echo "❌ Clang compiler directory not found"
    echo "   Expected location: $CLANG_PATH"
    CLANG_AVAILABLE=false
fi

echo ""
echo "=== Step 3: Checking Cross Compilers ==="
echo ""

# Check ARM64 cross compiler
if command -v aarch64-linux-gnu-gcc > /dev/null 2>&1; then
    ARM64_VERSION=$(aarch64-linux-gnu-gcc --version | head -1)
    echo "✅ ARM64 cross compiler: $ARM64_VERSION"
    ARM64_AVAILABLE=true
else
    echo "❌ ARM64 cross compiler not found (aarch64-linux-gnu-gcc)"
    ARM64_AVAILABLE=false
fi

# Check ARM32 cross compiler  
if command -v arm-linux-gnueabihf-gcc > /dev/null 2>&1; then
    ARM32_VERSION=$(arm-linux-gnueabihf-gcc --version | head -1)
    echo "✅ ARM32 cross compiler: $ARM32_VERSION"
    ARM32_AVAILABLE=true
else
    echo "❌ ARM32 cross compiler not found (arm-linux-gnueabihf-gcc)"
    ARM32_AVAILABLE=false
fi

echo ""
echo "=== Step 4: Checking Kernel Source ==="
echo ""

# Check kernel configuration
if [[ -f ".config" ]]; then
    echo "✅ Kernel configuration exists (.config)"
    CONFIG_AVAILABLE=true
else
    echo "❌ Kernel configuration missing (.config)"
    CONFIG_AVAILABLE=false
fi

# Check Makefile
if [[ -f "Makefile" ]]; then
    echo "✅ Kernel Makefile exists"
    MAKEFILE_AVAILABLE=true
else
    echo "❌ Kernel Makefile missing"
    MAKEFILE_AVAILABLE=false
fi

# Check arch directory
if [[ -d "arch/arm64" ]]; then
    echo "✅ ARM64 architecture support found"
    ARCH_AVAILABLE=true
else
    echo "❌ ARM64 architecture support missing"
    ARCH_AVAILABLE=false
fi

echo ""
echo "=== Step 5: Checking Previous Build Issues ==="
echo ""

# Check for problematic modules
if [[ -f ".config" ]]; then
    if grep -q "CONFIG_NETFILTER_XT_TARGET_TCPMSS=y" .config; then
        echo "⚠️  Problematic TCPMSS module enabled (known to cause build failures)"
        TCPMSS_ISSUE=true
    else
        echo "✅ TCPMSS module not problematic"
        TCPMSS_ISSUE=false
    fi
else
    echo "⚠️  Cannot check module configuration (no .config file)"
    TCPMSS_ISSUE=unknown
fi

# Check for previous build artifacts
if [[ -f "vmlinux" ]]; then
    echo "✅ Previous build artifacts found (vmlinux exists)"
    PREVIOUS_BUILD=true
else
    echo "ℹ️  No previous build artifacts (clean state)"
    PREVIOUS_BUILD=false
fi

echo ""
echo "========================================"
echo "  📋 REQUIREMENTS SUMMARY"
echo "========================================"
echo ""

# Calculate readiness score
TOTAL_CHECKS=7
PASSED_CHECKS=0

if [[ ${#MISSING_TOOLS[@]} -eq 0 ]]; then ((PASSED_CHECKS++)); fi
if [[ $CLANG_AVAILABLE == true ]]; then ((PASSED_CHECKS++)); fi
if [[ $ARM64_AVAILABLE == true ]]; then ((PASSED_CHECKS++)); fi
if [[ $ARM32_AVAILABLE == true ]]; then ((PASSED_CHECKS++)); fi
if [[ $CONFIG_AVAILABLE == true ]]; then ((PASSED_CHECKS++)); fi
if [[ $MAKEFILE_AVAILABLE == true ]]; then ((PASSED_CHECKS++)); fi
if [[ $ARCH_AVAILABLE == true ]]; then ((PASSED_CHECKS++)); fi

echo "🎯 Build Readiness: $PASSED_CHECKS/$TOTAL_CHECKS checks passed"
echo ""

if [[ $PASSED_CHECKS -eq $TOTAL_CHECKS ]]; then
    echo "🎉 **READY TO BUILD!**"
    echo "   All requirements are satisfied."
    echo "   You can proceed with kernel compilation."
    BUILD_READY=true
elif [[ $PASSED_CHECKS -ge 5 ]]; then
    echo "⚠️  **MOSTLY READY**"
    echo "   Most requirements satisfied, minor issues to fix."
    BUILD_READY=partial
else
    echo "❌ **NOT READY**"
    echo "   Several requirements missing, need setup first."
    BUILD_READY=false
fi

echo ""
echo "=== Installation Commands Needed ==="
echo ""

if [[ ${#MISSING_TOOLS[@]} -gt 0 ]]; then
    echo "📦 Install missing system tools:"
    echo "   sudo apt update"
    echo "   sudo apt install -y \\"
    
    # Group tools into packages
    PACKAGES=()
    for tool in "${MISSING_TOOLS[@]}"; do
        case $tool in
            "make") PACKAGES+=("build-essential") ;;
            "gcc") PACKAGES+=("build-essential") ;;
            "tar"|"gzip") PACKAGES+=("tar" "gzip") ;;
            "xz") PACKAGES+=("xz-utils") ;;
            "bison") PACKAGES+=("bison") ;;
            "flex") PACKAGES+=("flex") ;;
            "bc") PACKAGES+=("bc") ;;
            "python3") PACKAGES+=("python3") ;;
            "perl") PACKAGES+=("perl") ;;
            "rsync") PACKAGES+=("rsync") ;;
            "cpio") PACKAGES+=("cpio") ;;
            "aarch64-linux-gnu-gcc") PACKAGES+=("gcc-aarch64-linux-gnu") ;;
            "arm-linux-gnueabihf-gcc") PACKAGES+=("gcc-arm-linux-gnueabihf") ;;
        esac
    done
    
    # Remove duplicates and print
    UNIQUE_PACKAGES=($(printf "%s\n" "${PACKAGES[@]}" | sort -u))
    for package in "${UNIQUE_PACKAGES[@]}"; do
        echo "     $package \\"
    done
    echo ""
fi

if [[ $CLANG_AVAILABLE == false ]]; then
    echo "🔧 Clang compiler setup needed:"
    echo "   The Apollo script should download Clang automatically."
    echo "   If missing, run: ./apollo.sh and select Clang 19"
    echo ""
fi

echo "=== Next Steps ==="
echo ""

if [[ $BUILD_READY == true ]]; then
    echo "✅ **Ready to build kernel!**"
    echo "   Run: ./fix_and_build_kernel.sh"
elif [[ $BUILD_READY == partial ]]; then
    echo "⚠️  **Install missing tools first, then build:**"
    echo "   1. Install missing packages (see commands above)"
    echo "   2. Run: ./fix_and_build_kernel.sh"
else
    echo "❌ **Setup required before building:**"
    echo "   1. Install missing packages (see commands above)"
    echo "   2. Run this check script again"
    echo "   3. When ready, run: ./fix_and_build_kernel.sh"
fi

echo ""
echo "📝 This diagnostic completed. Review the results above."
EOF

chmod +x check_build_requirements.sh
echo "✅ Diagnostic script created!"
echo ""
echo "🔍 Run the diagnostic first:"
echo "   ./check_build_requirements.sh"
echo ""
echo "This will show you exactly what tools are missing and what needs to be installed."
EOF

# Make it executable
chmod +x check_build_requirements.sh

echo "✅ Diagnostic script created successfully!"
echo "🔍 You can now run it with: ./check_build_requirements.sh"
