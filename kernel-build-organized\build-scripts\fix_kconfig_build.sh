#!/bin/bash
# Fix kconfig build issues

echo "=== Fixing Kconfig Build Issues ==="

# Set environment
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export ANDROID_MAJOR_VERSION=q

# Step 1: Clean kconfig directory
echo "Cleaning kconfig build artifacts..."
make -C scripts/kconfig clean > /dev/null 2>&1 || true

# Step 2: Build kconfig with proper dependencies
echo "Building kconfig tools with all dependencies..."
if make -C scripts/kconfig > /dev/null 2>&1; then
    echo "✅ Kconfig tools built successfully!"
else
    echo "❌ Kconfig build failed, trying alternative approach..."
    
    # Try building individual components
    echo "Trying to build kconfig components individually..."
    cd scripts/kconfig
    
    # Build the parser first
    if make conf-objs > /dev/null 2>&1; then
        echo "✓ Kconfig objects built"
    fi
    
    # Try building with explicit linking
    if gcc -o conf conf.o confdata.o expr.o lexer.lex.o parser.tab.o preprocess.o symbol.o util.o > /dev/null 2>&1; then
        echo "✅ Manual kconfig build successful!"
    else
        echo "❌ Manual build also failed"
        echo "Trying with system kconfig tools..."
        
        # Install system kconfig tools as fallback
        sudo apt install -y kconfig-frontends > /dev/null 2>&1 || true
    fi
    
    cd ../..
fi

# Step 3: Test if kconfig works now
echo "Testing kconfig functionality..."
if [[ -f "scripts/kconfig/conf" ]] && scripts/kconfig/conf --help > /dev/null 2>&1; then
    echo "✅ Kconfig tools are working!"
    
    # Now try defconfig
    echo "Testing defconfig with working kconfig..."
    if make exynos9810_defconfig > /dev/null 2>&1; then
        echo "🎉 exynos9810_defconfig successful!"
        echo ""
        echo "Ready to build kernel:"
        echo "  export ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- ANDROID_MAJOR_VERSION=q"
        echo "  make -j\$(nproc)"
    else
        echo "❌ Defconfig still fails, trying alternatives..."
        for config in starlte_defconfig star2lte_defconfig crownlte_defconfig; do
            if make $config > /dev/null 2>&1; then
                echo "✅ $config works!"
                break
            fi
        done
    fi
else
    echo "❌ Kconfig tools still not working"
    echo "Try using the apollo.sh script instead:"
    echo "  ./apollo.sh"
fi
