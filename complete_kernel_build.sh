#!/bin/bash
# Complete the kernel build and find/create the final kernel package

echo "========================================"
echo "  Complete Kernel Build & Package"
echo "========================================"
echo ""

cd ~/kernel-build-wsl
echo "Working in: $(pwd)"
echo ""

# Set environment
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export ANDROID_MAJOR_VERSION=q

echo "Environment:"
echo "  ARCH=$ARCH"
echo "  CROSS_COMPILE=$CROSS_COMPILE"
echo "  ANDROID_MAJOR_VERSION=$ANDROID_MAJOR_VERSION"
echo ""

echo "=== Step 1: Analyzing Current Build State ==="

# Check what was built
echo "Current arch/arm64/boot/ contents:"
ls -la arch/arm64/boot/

echo ""
echo "Checking for partial build artifacts..."
if [[ -f "arch/arm64/boot/dtb.img" ]]; then
    echo "✅ Device tree built successfully"
else
    echo "❌ Device tree not built"
fi

if [[ -f ".config" ]]; then
    echo "✅ Kernel configuration exists"
else
    echo "❌ Kernel configuration missing"
fi

# Check build status
echo ""
echo "Checking build completeness..."
if [[ -f "vmlinux" ]]; then
    echo "✅ vmlinux exists (kernel compiled)"
    VMLINUX_SIZE=$(ls -lh vmlinux | awk '{print $5}')
    echo "   Size: $VMLINUX_SIZE"
else
    echo "❌ vmlinux missing (kernel not fully compiled)"
fi

echo ""
echo "=== Step 2: Completing Kernel Build ==="

# Try to complete the build
echo "Attempting to complete kernel build..."

# First, ensure we have the right configuration
if ! make starlte_defconfig > /dev/null 2>&1; then
    echo "❌ Failed to generate defconfig"
    exit 1
fi

echo "✅ Configuration set"

# Build the kernel image specifically
echo "Building kernel Image..."
if make -j$(nproc) Image > build_image.log 2>&1; then
    echo "✅ Kernel Image build completed"
    
    if [[ -f "arch/arm64/boot/Image" ]]; then
        IMAGE_SIZE=$(ls -lh arch/arm64/boot/Image | awk '{print $5}')
        echo "✅ Kernel Image created: $IMAGE_SIZE"
        IMAGE_SUCCESS=true
    else
        echo "❌ Image file still not found after build"
        IMAGE_SUCCESS=false
    fi
else
    echo "❌ Kernel Image build failed"
    echo "Last 10 lines of build log:"
    tail -10 build_image.log
    IMAGE_SUCCESS=false
fi

echo ""
echo "=== Step 3: Building Device Tree ==="

if make -j$(nproc) dtbs > build_dtbs.log 2>&1; then
    echo "✅ Device tree build completed"
else
    echo "❌ Device tree build failed"
    tail -5 build_dtbs.log
fi

echo ""
echo "=== Step 4: Creating Kernel Package ==="

if [[ $IMAGE_SUCCESS == true ]]; then
    echo "Creating kernel package..."
    
    # Create AnyKernel3-style package
    PACKAGE_DIR="AnyKernel3"
    mkdir -p "$PACKAGE_DIR"
    
    # Copy kernel image
    cp arch/arm64/boot/Image "$PACKAGE_DIR/"
    
    # Copy device tree if available
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        cp arch/arm64/boot/dtb.img "$PACKAGE_DIR/"
    fi
    
    # Create update-binary script
    cat > "$PACKAGE_DIR/META-INF/com/google/android/update-binary" << 'EOF'
#!/sbin/sh
# AnyKernel3 installer script

OUTFD=/proc/self/fd/$2
ZIPFILE="$3"

ui_print() {
  echo "ui_print $1" > $OUTFD
  echo "ui_print" > $OUTFD
}

ui_print "ArtPlus NEXT Kernel for Galaxy S9"
ui_print "Installing kernel..."

# Extract files
cd /tmp
unzip -o "$ZIPFILE"

# Flash kernel
dd if=/tmp/Image of=/dev/block/platform/11120000.ufs/by-name/boot

ui_print "Kernel installed successfully!"
ui_print "Reboot to complete installation"
EOF
    
    # Create updater-script
    mkdir -p "$PACKAGE_DIR/META-INF/com/google/android"
    cat > "$PACKAGE_DIR/META-INF/com/google/android/updater-script" << 'EOF'
# ArtPlus NEXT Kernel
ui_print("ArtPlus NEXT Kernel for Galaxy S9");
ui_print("Flashing kernel...");
package_extract_file("Image", "/dev/block/platform/11120000.ufs/by-name/boot");
ui_print("Done!");
EOF
    
    # Make update-binary executable
    chmod +x "$PACKAGE_DIR/META-INF/com/google/android/update-binary"
    
    # Create ZIP package
    cd "$PACKAGE_DIR"
    zip -r "../ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" . > /dev/null 2>&1
    cd ..
    
    if [[ -f "ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" ]]; then
        ZIP_SIZE=$(ls -lh ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip | awk '{print $5}')
        echo "✅ Kernel ZIP created: ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip ($ZIP_SIZE)"
        PACKAGE_SUCCESS=true
    else
        echo "❌ Failed to create ZIP package"
        PACKAGE_SUCCESS=false
    fi
else
    echo "❌ Cannot create package - kernel Image not available"
    PACKAGE_SUCCESS=false
fi

echo ""
echo "=== Step 5: Alternative Build Methods ==="

if [[ $IMAGE_SUCCESS == false ]]; then
    echo "Trying alternative build methods..."
    
    # Method 1: Use Apollo script
    echo "Method 1: Using Apollo script..."
    timeout 300s ./apollo.sh << 'APOLLO_INPUT' > apollo_complete.log 2>&1 || true
1
4
1
y
n
APOLLO_INPUT
    
    # Check if Apollo created anything
    if [[ -f "arch/arm64/boot/Image" ]]; then
        echo "✅ Apollo script completed the build!"
        IMAGE_SUCCESS=true
    else
        echo "❌ Apollo script didn't complete the build"
        echo "Apollo log (last 10 lines):"
        tail -10 apollo_complete.log
    fi
fi

echo ""
echo "=== Step 6: Final Organization ==="

# Copy everything to organized directory
ORGANIZED_DIR="/mnt/c/Sviluppo/kernelsu-galaxys9/kernel-build-organized"

if [[ $IMAGE_SUCCESS == true ]]; then
    echo "Copying kernel files to organized directory..."
    
    # Copy kernel image
    cp arch/arm64/boot/Image "$ORGANIZED_DIR/kernel-output/"
    
    # Copy device tree
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        cp arch/arm64/boot/dtb.img "$ORGANIZED_DIR/kernel-output/"
    fi
    
    # Copy package if created
    if [[ $PACKAGE_SUCCESS == true ]]; then
        cp ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip "$ORGANIZED_DIR/kernel-output/"
        cp -r AnyKernel3 "$ORGANIZED_DIR/kernel-output/"
    fi
    
    echo "✅ Files copied to organized directory"
fi

# Copy build logs
cp *.log "$ORGANIZED_DIR/logs/" 2>/dev/null || true

echo ""
echo "========================================"
echo "  Build Completion Results"
echo "========================================"
echo ""

if [[ $IMAGE_SUCCESS == true ]]; then
    echo "🎉 SUCCESS! Kernel build completed!"
    echo ""
    echo "📁 Kernel files location:"
    echo "   Windows: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-organized\\kernel-output\\"
    echo "   WSL: $ORGANIZED_DIR/kernel-output/"
    echo ""
    echo "📱 Files created:"
    echo "   ✅ Image - Raw kernel image"
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        echo "   ✅ dtb.img - Device tree blob"
    fi
    if [[ $PACKAGE_SUCCESS == true ]]; then
        echo "   ✅ ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip - Flashable package"
    fi
    echo ""
    echo "🚀 Ready for installation on Galaxy S9 (SM-G960F)!"
    echo ""
    echo "Installation methods:"
    if [[ $PACKAGE_SUCCESS == true ]]; then
        echo "   1. TWRP: Flash the ZIP file (recommended)"
    fi
    echo "   2. ODIN: Create TAR with Image file"
    echo "   3. Fastboot: fastboot flash boot Image"
    
else
    echo "❌ Build incomplete"
    echo ""
    echo "The kernel build did not complete successfully."
    echo "Check the build logs in: $ORGANIZED_DIR/logs/"
    echo ""
    echo "Possible issues:"
    echo "1. Insufficient memory/storage"
    echo "2. Missing dependencies"
    echo "3. Compiler errors"
    echo ""
    echo "Try:"
    echo "   make clean && make starlte_defconfig && make -j2 Image"
fi

echo ""
echo "All files organized in: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-organized\\"
