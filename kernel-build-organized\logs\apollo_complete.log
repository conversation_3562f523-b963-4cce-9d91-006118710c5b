[H[2J[3J----------------------------------------------
ArtPlus NEXT Build Script 250827
 
 
1) starlte    2) star2lte    3) crownlte
4) starltekor 5) star2ltekor 6) crownltekor
 
7) Build All/ZIP 8) Abort
----------------------------------------------
----------------------------------------------
 
1) Clang 12 (LLVM +LTO)
2) Clang 14 (LLVM +LTO)
3) Clang 18 (LLVM +LTO PGO Bolt MLGO Polly)
4) Clang 19 (^)
5) Neutron Clang 18 (^)
6) Neutron Clang 19 (^)
7) Neutron Clang 20 (BETA)
8) Other (Apollo/toolchain/clang-custom)
 
 
1) SELinux Permissive  2) SELinux Enforcing
 
 
 
 
 
 Galaxy S9 INTL
----------------------------------------------
 Generating defconfig for G960F
 
 Clean-up old config 
 Base	- exynos9810_defconfig 
 Device - starlte_defconfig 
 Region	- eur_defconfig 
 Apollo	- apollo_defconfig 
 Building SELinux Permissive Kernel
 Building KernelSU
 G960F config generated 
 
----------------------------------------------
 Apollo Kernel Build Options 
 
 Kernel		- ArtPlus-NEXT-G960F-250827-Permissive-KSU
 Device		- G960F
 Compiler	- ../compiler/clang-19.0.0-r530567
 Env		- Dirty Build
 SELinux	- Permissive
 KernelSU	- Version: 12030
 
----------------------------------------------
 
Building zImage for G960F
Make tmp_defconfig
  HOSTCC  scripts/basic/fixdep
  HOSTCC  scripts/basic/bin2c
  HOSTCC  scripts/kconfig/conf.o
  HOSTCC  scripts/kconfig/zconf.tab.o
  HOSTLD  scripts/kconfig/conf
arch/arm64/configs/tmp_defconfig:6111:warning: override: reassigning to symbol WQ_POWER_EFFICIENT_DEFAULT
arch/arm64/configs/tmp_defconfig:6120:warning: override: reassigning to symbol NLS_CODEPAGE_437
arch/arm64/configs/tmp_defconfig:6121:warning: override: reassigning to symbol NLS_CODEPAGE_737
arch/arm64/configs/tmp_defconfig:6122:warning: override: reassigning to symbol NLS_CODEPAGE_775
arch/arm64/configs/tmp_defconfig:6123:warning: override: reassigning to symbol NLS_CODEPAGE_850
arch/arm64/configs/tmp_defconfig:6124:warning: override: reassigning to symbol NLS_CODEPAGE_852
arch/arm64/configs/tmp_defconfig:6125:warning: override: reassigning to symbol NLS_CODEPAGE_855
arch/arm64/configs/tmp_defconfig:6126:warning: override: reassigning to symbol NLS_CODEPAGE_857
arch/arm64/configs/tmp_defconfig:6127:warning: override: reassigning to symbol NLS_CODEPAGE_860
arch/arm64/configs/tmp_defconfig:6128:warning: override: reassigning to symbol NLS_CODEPAGE_861
arch/arm64/configs/tmp_defconfig:6129:warning: override: reassigning to symbol NLS_CODEPAGE_862
arch/arm64/configs/tmp_defconfig:6130:warning: override: reassigning to symbol NLS_CODEPAGE_863
arch/arm64/configs/tmp_defconfig:6131:warning: override: reassigning to symbol NLS_CODEPAGE_864
arch/arm64/configs/tmp_defconfig:6132:warning: override: reassigning to symbol NLS_CODEPAGE_865
arch/arm64/configs/tmp_defconfig:6133:warning: override: reassigning to symbol NLS_CODEPAGE_866
arch/arm64/configs/tmp_defconfig:6134:warning: override: reassigning to symbol NLS_CODEPAGE_869
arch/arm64/configs/tmp_defconfig:6135:warning: override: reassigning to symbol NLS_CODEPAGE_936
arch/arm64/configs/tmp_defconfig:6136:warning: override: reassigning to symbol NLS_CODEPAGE_950
arch/arm64/configs/tmp_defconfig:6137:warning: override: reassigning to symbol NLS_CODEPAGE_932
arch/arm64/configs/tmp_defconfig:6138:warning: override: reassigning to symbol NLS_CODEPAGE_949
arch/arm64/configs/tmp_defconfig:6139:warning: override: reassigning to symbol NLS_CODEPAGE_874
arch/arm64/configs/tmp_defconfig:6140:warning: override: reassigning to symbol NLS_ISO8859_8
arch/arm64/configs/tmp_defconfig:6141:warning: override: reassigning to symbol NLS_CODEPAGE_1250
arch/arm64/configs/tmp_defconfig:6142:warning: override: reassigning to symbol NLS_CODEPAGE_1251
arch/arm64/configs/tmp_defconfig:6143:warning: override: reassigning to symbol NLS_ASCII
arch/arm64/configs/tmp_defconfig:6144:warning: override: reassigning to symbol NLS_ISO8859_1
arch/arm64/configs/tmp_defconfig:6145:warning: override: reassigning to symbol NLS_ISO8859_2
arch/arm64/configs/tmp_defconfig:6146:warning: override: reassigning to symbol NLS_ISO8859_3
arch/arm64/configs/tmp_defconfig:6147:warning: override: reassigning to symbol NLS_ISO8859_4
arch/arm64/configs/tmp_defconfig:6148:warning: override: reassigning to symbol NLS_ISO8859_5
arch/arm64/configs/tmp_defconfig:6149:warning: override: reassigning to symbol NLS_ISO8859_6
arch/arm64/configs/tmp_defconfig:6150:warning: override: reassigning to symbol NLS_ISO8859_7
arch/arm64/configs/tmp_defconfig:6151:warning: override: reassigning to symbol NLS_ISO8859_9
arch/arm64/configs/tmp_defconfig:6152:warning: override: reassigning to symbol NLS_ISO8859_13
arch/arm64/configs/tmp_defconfig:6153:warning: override: reassigning to symbol NLS_ISO8859_14
arch/arm64/configs/tmp_defconfig:6154:warning: override: reassigning to symbol NLS_ISO8859_15
arch/arm64/configs/tmp_defconfig:6155:warning: override: reassigning to symbol NLS_KOI8_R
arch/arm64/configs/tmp_defconfig:6156:warning: override: reassigning to symbol NLS_KOI8_U
arch/arm64/configs/tmp_defconfig:6157:warning: override: reassigning to symbol NLS_MAC_ROMAN
arch/arm64/configs/tmp_defconfig:6158:warning: override: reassigning to symbol NLS_MAC_CELTIC
arch/arm64/configs/tmp_defconfig:6159:warning: override: reassigning to symbol NLS_MAC_CENTEURO
arch/arm64/configs/tmp_defconfig:6160:warning: override: reassigning to symbol NLS_MAC_CROATIAN
arch/arm64/configs/tmp_defconfig:6161:warning: override: reassigning to symbol NLS_MAC_CYRILLIC
arch/arm64/configs/tmp_defconfig:6162:warning: override: reassigning to symbol NLS_MAC_GAELIC
arch/arm64/configs/tmp_defconfig:6163:warning: override: reassigning to symbol NLS_MAC_GREEK
arch/arm64/configs/tmp_defconfig:6164:warning: override: reassigning to symbol NLS_MAC_ICELAND
arch/arm64/configs/tmp_defconfig:6165:warning: override: reassigning to symbol NLS_MAC_INUIT
arch/arm64/configs/tmp_defconfig:6166:warning: override: reassigning to symbol NLS_MAC_ROMANIAN
arch/arm64/configs/tmp_defconfig:6167:warning: override: reassigning to symbol NLS_MAC_TURKISH
arch/arm64/configs/tmp_defconfig:6168:warning: override: reassigning to symbol NLS_UTF8
#
# configuration written to .config
#
Make Kernel with ../compiler/clang-19.0.0-r530567
scripts/kconfig/conf  --silentoldconfig Kconfig
  CHK     include/config/kernel.release
  CHK     include/generated/uapi/linux/version.h
  CHK     include/generated/utsrelease.h
  CHK     scripts/mod/devicetable-offsets.h
-- KernelSU Manager signature size: 0x033b
-- KernelSU Manager signature hash: c371061b19d8c7d7d6133c6a9bafe198fa944e50c1b31c9d8daa8d7f1fc2d2d6
  CHK     include/generated/timeconst.h
  CHK     include/generated/bounds.h
  CHK     include/generated/asm-offsets.h
  CALL    scripts/checksyscalls.sh
  CHK     include/generated/compile.h
  CHK     kernel/config_data.h
  UPD     kernel/config_data.h
  CHK     kernel/kheaders_data.tar.xz
  CC      kernel/stop_machine.o
  GEN     kernel/kheaders_data.tar.xz
./kernel/gen_kheaders.sh: line 61: cpio: command not found
make[1]: *** [kernel/Makefile:137: kernel/kheaders_data.tar.xz] Error 127
make[1]: *** Waiting for unfinished jobs....
  DTC     arch/arm64/boot/dts/exynos/exynos9810-starlte_eur_open_26.dtb
make: *** [Makefile:1260: kernel] Error 2
make: *** Waiting for unfinished jobs....
  CHK     drivers/gator_5.27/gator_src_md5.h
  CC      drivers/gator_5.27/gator_main.o
  AR      net/netfilter/netfilter.o
  AR      net/netfilter/nf_conntrack.o
  AR      net/netfilter/nf_conntrack_h323.o
  AR      net/netfilter/nf_nat.o
make[2]: *** No rule to make target 'net/netfilter/xt_TCPMSS.o', needed by 'net/netfilter/built-in.o'.  Stop.
make[1]: *** [scripts/Makefile.build:650: net/netfilter] Error 2
make[1]: *** Waiting for unfinished jobs....
make: *** [Makefile:1260: net] Error 2
-- KernelSU Manager signature size: 0x033b
-- KernelSU Manager signature hash: c371061b19d8c7d7d6133c6a9bafe198fa944e50c1b31c9d8daa8d7f1fc2d2d6
  AR      drivers/gator_5.27/gator.o
  AR      drivers/gator_5.27/built-in.o
  AR      drivers/built-in.o
