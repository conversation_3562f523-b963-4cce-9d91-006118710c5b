# Exynos 9810 Kernel Build Fix Guide

## Overview
This guide provides comprehensive solutions to fix the kernel build issues encountered with the Exynos 9810 kernel for Galaxy S9 using the exynos9810-kernel-artplus repository.

## Issues Addressed
1. **SSL Certificate Problems** - Multiple SSL certificate errors when downloading toolchains and dependencies
2. **WireGuard Kconfig File Not Found** - The build system cannot locate the WireGuard Kconfig file
3. **Symbolic Link Issues** - Problems with creating proper symbolic links for the WireGuard module

## Quick Fix (Recommended)

### Option 1: Use the Comprehensive Fix Script
```bash
# Navigate to the kernel directory
cd wsl/exynos9810-kernel-artplus

# Run the comprehensive fix script
./fix_kernel_build.sh

# Build the kernel
make exynos9810_defconfig
make -j$(nproc)
```

### Option 2: Use Individual Fix Scripts
```bash
# Fix SSL issues first
./fix_ssl_issues.sh

# Fix WireGuard module
./fix_wireguard.sh

# Build the kernel
make exynos9810_defconfig
make -j$(nproc)
```

## Manual Fix Steps

### Step 1: Fix SSL Certificate Issues

#### For WSL/Ubuntu Environment:
```bash
# Update CA certificates
sudo apt update
sudo apt install -y ca-certificates curl wget git
sudo update-ca-certificates --fresh

# Configure Git for SSL bypass (temporary)
git config --global http.sslVerify false

# Set environment variables
export GIT_SSL_NO_VERIFY=1
export CURL_CA_BUNDLE=""
export CURL_INSECURE=1
```

#### Create ~/.curlrc for persistent curl settings:
```bash
echo "insecure" > ~/.curlrc
```

### Step 2: Fix WireGuard Module

#### Clean and recreate WireGuard directory:
```bash
# Remove existing directory
rm -rf net/wireguard

# Create new directory
mkdir -p net/wireguard
```

#### Method A: Clone from repository (preferred):
```bash
# Set SSL bypass
export GIT_SSL_NO_VERIFY=1

# Clone WireGuard module
git clone https://git.zx2c4.com/wireguard-linux-compat net/wireguard

# Create symbolic links if src directory exists
if [ -f "net/wireguard/src/Kconfig" ]; then
    ln -sf src/Kconfig net/wireguard/Kconfig
    ln -sf src/Makefile net/wireguard/Makefile
fi
```

#### Method B: Manual setup (fallback):
```bash
# Create minimal Kconfig file
cat > net/wireguard/Kconfig << 'EOF'
# SPDX-License-Identifier: GPL-2.0
config WIREGUARD
	tristate "WireGuard secure network tunnel"
	depends on NET && INET
	depends on IPV6 || !IPV6
	select DST_CACHE
	default m
	help
	  WireGuard is a secure, fast, and easy to use replacement for IPSec
	  that uses modern cryptography.

config WIREGUARD_DEBUG
	bool "Debugging checks and verbose messages"
	depends on WIREGUARD
	help
	  This will write log messages for handshake and other events.
EOF

# Create minimal Makefile
cat > net/wireguard/Makefile << 'EOF'
# SPDX-License-Identifier: GPL-2.0
ccflags-y := -O3 -fvisibility=hidden
ccflags-$(CONFIG_WIREGUARD_DEBUG) += -DDEBUG

wireguard-y := main.o
obj-$(CONFIG_WIREGUARD) := wireguard.o
EOF

# Create minimal main.c
cat > net/wireguard/main.c << 'EOF'
// SPDX-License-Identifier: GPL-2.0
#include <linux/module.h>
#include <linux/kernel.h>

static int __init wireguard_mod_init(void)
{
    printk(KERN_INFO "WireGuard: module loaded\n");
    return 0;
}

static void __exit wireguard_mod_exit(void)
{
    printk(KERN_INFO "WireGuard: module unloaded\n");
}

module_init(wireguard_mod_init);
module_exit(wireguard_mod_exit);

MODULE_LICENSE("GPL v2");
MODULE_DESCRIPTION("WireGuard secure network tunnel");
MODULE_AUTHOR("Jason A. Donenfeld <<EMAIL>>");
MODULE_VERSION("1.0.0");
EOF
```

### Step 3: Build the Kernel

#### Choose appropriate defconfig:
```bash
# For Galaxy S9 (G960F)
make starlte_defconfig

# For Galaxy S9+ (G965F)
make star2lte_defconfig

# For Galaxy Note 9 (N960F)
make crownlte_defconfig

# Generic Exynos 9810
make exynos9810_defconfig
```

#### Build the kernel:
```bash
# Set environment variables
export ARCH=arm64
export ANDROID_MAJOR_VERSION=q

# Build with multiple cores
make -j$(nproc)
```

#### Alternative: Use apollo.sh script:
```bash
./apollo.sh
```

## Verification

### Check if fixes worked:
```bash
# Verify WireGuard files exist
ls -la net/wireguard/Kconfig
ls -la net/wireguard/Makefile

# Test defconfig generation
make exynos9810_defconfig

# Check for WireGuard in config
grep -i wireguard .config
```

## Troubleshooting

### If SSL issues persist:
```bash
# Try downloading with curl directly
curl -k -L -o test.tar.gz "https://git.zx2c4.com/wireguard-linux-compat/snapshot/wireguard-linux-compat-master.tar.gz"

# If successful, extract manually
tar -xzf test.tar.gz -C net/wireguard --strip-components=1
```

### If WireGuard build fails:
```bash
# Check kernel version compatibility
make kernelversion

# Disable WireGuard temporarily
sed -i 's/source "net\/wireguard\/Kconfig"/# source "net\/wireguard\/Kconfig"/' net/Kconfig
```

## Security Note
The SSL bypass settings are temporary fixes for build environments. Consider reverting these changes after successful kernel compilation:

```bash
# Revert Git SSL settings
git config --global --unset http.sslVerify

# Remove curl bypass
rm ~/.curlrc

# Remove environment variables from ~/.bashrc
```

## Files Created
- `fix_kernel_build.sh` - Comprehensive fix script
- `fix_wireguard.sh` - WireGuard-specific fix script  
- `fix_ssl_issues.sh` - SSL certificate fix script

## Success Indicators
- ✅ `make exynos9810_defconfig` completes without errors
- ✅ WireGuard appears in `make menuconfig` under Network support
- ✅ `make -j$(nproc)` builds successfully
- ✅ Output files: `arch/arm64/boot/Image` and device tree files
