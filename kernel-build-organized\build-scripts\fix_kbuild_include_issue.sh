#!/bin/bash
# Fix the specific Kbuild.include line 466 issue

echo "========================================"
echo "  Kbuild.include Line 466 Fix Script"
echo "========================================"
echo ""

# Navigate to WSL kernel directory
WSL_DIR="$HOME/kernel-build-wsl"
if [[ ! -d "$WSL_DIR" ]]; then
    echo "❌ WSL kernel directory not found: $WSL_DIR"
    echo "Please ensure the WSL fix script was run successfully"
    exit 1
fi

cd "$WSL_DIR"
echo "Working in: $(pwd)"
echo ""

# Check if Kbuild.include exists
if [[ ! -f "scripts/Kbuild.include" ]]; then
    echo "❌ scripts/Kbuild.include not found"
    exit 1
fi

echo "=== Step 1: Analyzing Kbuild.include Issue ==="

# Show the problematic area around line 466
echo "Content around line 466:"
sed -n '460,470p' scripts/Kbuild.include | cat -n
echo ""

# Check for specific issues
echo "=== Step 2: Identifying Specific Problems ==="

# Check line 466 specifically
LINE_466=$(sed -n '466p' scripts/Kbuild.include)
echo "Line 466 content: '$LINE_466'"

# Check for common Makefile issues
if [[ -z "$LINE_466" ]]; then
    echo "⚠️  Line 466 is empty"
elif [[ "$LINE_466" =~ ^[[:space:]]*$ ]]; then
    echo "⚠️  Line 466 contains only whitespace"
elif [[ "$LINE_466" =~ ^[[:space:]]*# ]]; then
    echo "✓ Line 466 is a comment"
elif [[ "$LINE_466" =~ ^\t ]]; then
    echo "✓ Line 466 starts with tab (correct for Makefile)"
elif [[ "$LINE_466" =~ ^[[:space:]] ]]; then
    echo "❌ Line 466 starts with spaces instead of tab (PROBLEM FOUND!)"
else
    echo "⚠️  Line 466 format needs investigation"
fi

# Check for fetch script references
echo ""
echo "Checking for fetch script references:"
if grep -n "fetch-latest" scripts/Kbuild.include; then
    echo "Found fetch script references"
else
    echo "No fetch script references found"
fi

echo ""
echo "=== Step 3: Applying Fixes ==="

# Create backup
if [[ ! -f "scripts/Kbuild.include.backup" ]]; then
    cp scripts/Kbuild.include scripts/Kbuild.include.backup
    echo "✓ Created backup: scripts/Kbuild.include.backup"
fi

# Fix 1: Convert spaces to tabs if needed
echo "Fixing potential space/tab issues..."
sed -i 's/^        /\t/' scripts/Kbuild.include  # Convert 8 spaces to tab
sed -i 's/^    /\t/' scripts/Kbuild.include      # Convert 4 spaces to tab

# Fix 2: Comment out problematic fetch scripts
echo "Commenting out fetch scripts..."
sed -i 's/^\(.*fetch-latest-wireguard\.sh.*\)$/#\1/' scripts/Kbuild.include
sed -i 's/^\(.*fetch-latest-kernelsu\.sh.*\)$/#\1/' scripts/Kbuild.include

# Fix 3: Ensure proper line endings
echo "Fixing line endings..."
dos2unix scripts/Kbuild.include 2>/dev/null || true

# Fix 4: Remove any problematic empty lines or malformed syntax
echo "Cleaning up syntax issues..."

# Create a temporary fixed version
cat > scripts/Kbuild.include.tmp << 'EOF'
# This file contains common rules and variables for kernel builds
# Fixed version to resolve line 466 issues

# Original content will be preserved, but problematic lines fixed
EOF

# Copy original content, fixing issues as we go
while IFS= read -r line; do
    # Skip empty lines that might cause issues
    if [[ -z "$line" ]] && [[ $(wc -l < scripts/Kbuild.include.tmp) -gt 460 ]] && [[ $(wc -l < scripts/Kbuild.include.tmp) -lt 470 ]]; then
        continue
    fi
    
    # Fix fetch script lines
    if [[ "$line" =~ fetch-latest.*\.sh ]]; then
        echo "# $line" >> scripts/Kbuild.include.tmp
    else
        echo "$line" >> scripts/Kbuild.include.tmp
    fi
done < scripts/Kbuild.include.backup

# Replace original with fixed version
mv scripts/Kbuild.include.tmp scripts/Kbuild.include

echo "✓ Applied syntax fixes"
echo ""

echo "=== Step 4: Verification ==="

# Check the fixed line 466
echo "Fixed line 466:"
sed -n '466p' scripts/Kbuild.include | cat -n

# Test basic make syntax
echo ""
echo "Testing make syntax..."
if make help > /dev/null 2>&1; then
    echo "✅ Basic make syntax test passed"
else
    echo "❌ Make syntax test still fails"
    echo "Trying alternative fix..."
    
    # Alternative fix: Create minimal Kbuild.include
    cat > scripts/Kbuild.include << 'EOF'
# Minimal Kbuild.include to resolve build issues
# This is a simplified version that should work

# Basic build functions
cc-option = $(call try-run,\
	$(CC) $(1) -c -x c /dev/null -o "$$TMP",$(1),$(2))

# Include original content but skip problematic sections
EOF
    
    # Append safe parts of original
    if [[ -f "scripts/Kbuild.include.backup" ]]; then
        grep -v "fetch-latest" scripts/Kbuild.include.backup >> scripts/Kbuild.include || true
    fi
fi

echo ""
echo "=== Step 5: Testing Build System ==="

# Set environment
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export ANDROID_MAJOR_VERSION=q

echo "Testing defconfig generation..."
DEFCONFIGS=("exynos9810_defconfig" "starlte_defconfig" "star2lte_defconfig" "crownlte_defconfig")
SUCCESS=false

for config in "${DEFCONFIGS[@]}"; do
    echo "Testing $config..."
    if make $config > /dev/null 2>&1; then
        echo "✅ $config successful!"
        SUCCESS=true
        WORKING_CONFIG=$config
        break
    else
        echo "❌ $config failed"
    fi
done

echo ""
echo "=== Step 6: Testing Apollo Script ==="
if [[ $SUCCESS == true ]]; then
    echo "✅ Defconfig working, testing Apollo script..."
    
    # Test apollo script with timeout
    timeout 30s ./apollo.sh << 'APOLLO_INPUT' > apollo_test.log 2>&1 || true
1
4
1
y
n
APOLLO_INPUT
    
    if grep -q "missing separator" apollo_test.log; then
        echo "❌ Apollo script still has Kbuild.include issues"
        echo "Last few lines of apollo output:"
        tail -10 apollo_test.log
    else
        echo "✅ Apollo script appears to be working!"
        echo "Apollo test output:"
        tail -5 apollo_test.log
    fi
    
    rm -f apollo_test.log
else
    echo "❌ Defconfig still failing, Apollo test skipped"
fi

echo ""
echo "========================================"
echo "  Fix Results Summary"
echo "========================================"
echo ""

if [[ $SUCCESS == true ]]; then
    echo "🎉 Kbuild.include fix successful!"
    echo ""
    echo "✅ Fixed Issues:"
    echo "  - Line 466 syntax errors"
    echo "  - Space/tab conversion"
    echo "  - Fetch script conflicts"
    echo "  - Line ending problems"
    echo ""
    echo "Working configuration: $WORKING_CONFIG"
    echo ""
    echo "Ready to build kernel:"
    echo "  cd ~/kernel-build-wsl"
    echo "  export ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- ANDROID_MAJOR_VERSION=q"
    echo "  ./apollo.sh"
    echo ""
    echo "Or manual build:"
    echo "  make $WORKING_CONFIG"
    echo "  make -j\$(nproc)"
    
else
    echo "❌ Issues remain"
    echo ""
    echo "The Kbuild.include fix was applied but defconfig still fails."
    echo "This suggests deeper kernel configuration issues."
    echo ""
    echo "Troubleshooting options:"
    echo "1. Check kernel version compatibility:"
    echo "   cat Makefile | head -5"
    echo ""
    echo "2. Try different kernel source:"
    echo "   git status"
    echo "   git log --oneline -5"
    echo ""
    echo "3. Manual investigation:"
    echo "   make exynos9810_defconfig V=1"
fi

echo ""
echo "Backup files created:"
echo "  scripts/Kbuild.include.backup - Original file"
echo ""
echo "To revert changes:"
echo "  cp scripts/Kbuild.include.backup scripts/Kbuild.include"
