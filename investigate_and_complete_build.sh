#!/bin/bash
# Investigate the Apollo build and complete if needed

echo "========================================"
echo "  🔍 INVESTIGATING APOLLO BUILD RESULTS"
echo "========================================"
echo ""

cd ~/kernel-build-wsl
echo "Working in: $(pwd)"
echo ""

echo "=== Step 1: Analyzing Build Status ==="

# Check what was actually built
echo "Current build artifacts:"
echo ""

echo "📁 arch/arm64/boot/ contents:"
ls -la arch/arm64/boot/

echo ""
echo "📁 Root directory large files:"
find . -maxdepth 1 -size +10M -type f -exec ls -lh {} \; 2>/dev/null

echo ""
echo "📁 Looking for kernel-related files:"
find . -name "*Image*" -o -name "*zImage*" -o -name "*vmlinux*" | head -10

echo ""
echo "📁 Looking for any ZIP or package files:"
find . -name "*.zip" -o -name "*.tar" -o -name "*.img" | grep -v dtb

echo ""
echo "=== Step 2: Checking Apollo Build Logs ==="

if [[ -f "apollo_complete.log" ]]; then
    echo "📜 Apollo build log found. Checking for errors..."
    echo ""
    echo "Last 20 lines of Apollo log:"
    tail -20 apollo_complete.log
    echo ""
    
    # Check for specific build completion indicators
    if grep -q "BUILD SUCCESSFUL" apollo_complete.log; then
        echo "✅ Apollo log shows BUILD SUCCESSFUL"
    elif grep -q "Image" apollo_complete.log; then
        echo "✅ Apollo log mentions Image creation"
    elif grep -q "error" apollo_complete.log; then
        echo "❌ Apollo log shows errors"
        echo "Errors found:"
        grep -i "error" apollo_complete.log | tail -5
    else
        echo "⚠️  Apollo log status unclear"
    fi
else
    echo "❌ No Apollo build log found"
fi

echo ""
echo "=== Step 3: Checking for Alternative Build Outputs ==="

# Check if Apollo created files in different locations
POSSIBLE_LOCATIONS=(
    "."
    "out"
    "output" 
    "build"
    "../"
    "Apollo"
    "arch/arm64/boot"
)

echo "🔍 Searching for kernel files in possible locations..."
for location in "${POSSIBLE_LOCATIONS[@]}"; do
    if [[ -d "$location" ]]; then
        echo ""
        echo "📂 Checking $location/:"
        find "$location" -maxdepth 2 -name "*Image*" -o -name "*.zip" -o -name "*kernel*" 2>/dev/null | head -5
    fi
done

echo ""
echo "=== Step 4: Checking Build Configuration ==="

if [[ -f ".config" ]]; then
    echo "✅ Kernel configuration exists"
    
    # Check if the build was configured correctly
    if grep -q "CONFIG_ARM64=y" .config; then
        echo "✅ ARM64 architecture configured"
    fi
    
    if grep -q "CONFIG_ANDROID" .config; then
        echo "✅ Android configuration found"
    fi
else
    echo "❌ No kernel configuration found"
fi

echo ""
echo "=== Step 5: Attempting to Complete Build ==="

# Set environment variables
export ARCH=arm64
export SUBARCH=arm64
export ANDROID_MAJOR_VERSION=q

# Check if we have the Clang compiler
CLANG_PATH="../compiler/clang-19.0.0-r530567"
if [[ -d "$CLANG_PATH" ]]; then
    echo "✅ Clang compiler found at: $CLANG_PATH"
    export PATH="$CLANG_PATH/bin:$PATH"
    export CC="$CLANG_PATH/bin/clang"
    export CXX="$CLANG_PATH/bin/clang++"
    export CROSS_COMPILE=aarch64-linux-gnu-
    export CROSS_COMPILE_ARM32=arm-linux-gnueabihf-
    
    echo "🔨 Attempting to build kernel Image..."
    
    # Try to build just the Image
    if make Image > build_completion.log 2>&1; then
        echo "✅ Kernel Image build completed!"
        
        if [[ -f "arch/arm64/boot/Image" ]]; then
            IMAGE_SIZE=$(ls -lh arch/arm64/boot/Image | awk '{print $5}')
            echo "✅ Kernel Image created: $IMAGE_SIZE"
            BUILD_SUCCESS=true
        else
            echo "❌ Image file still not found after build"
            BUILD_SUCCESS=false
        fi
    else
        echo "❌ Kernel Image build failed"
        echo "Last 10 lines of build log:"
        tail -10 build_completion.log
        BUILD_SUCCESS=false
    fi
else
    echo "❌ Clang compiler not found"
    BUILD_SUCCESS=false
fi

echo ""
echo "=== Step 6: Creating Kernel Package (If Successful) ==="

if [[ $BUILD_SUCCESS == true ]]; then
    echo "🎉 Creating kernel package..."
    
    # Create AnyKernel3 package
    mkdir -p AnyKernel3/META-INF/com/google/android
    
    # Copy kernel files
    cp arch/arm64/boot/Image AnyKernel3/
    if [[ -f "arch/arm64/boot/dtb.img" ]]; then
        cp arch/arm64/boot/dtb.img AnyKernel3/
    fi
    
    # Create AnyKernel3 script
    cat > AnyKernel3/anykernel.sh << 'EOF'
#!/sbin/sh
# AnyKernel3 Script for Galaxy S9

## AnyKernel setup
properties() { '
kernel.string=ArtPlus NEXT Kernel for Galaxy S9
do.devicecheck=1
do.modules=0
do.systemless=1
do.cleanup=1
device.name1=starlte
device.name2=G960F
device.name3=SM-G960F
supported.versions=10-14
'; }

# Variables
block=/dev/block/platform/11120000.ufs/by-name/boot;
is_slot_device=0;

# Import functions
. tools/ak3-core.sh;

# Install
dump_boot;
write_boot;
EOF
    
    # Create tools directory
    mkdir -p AnyKernel3/tools
    cat > AnyKernel3/tools/ak3-core.sh << 'EOF'
#!/sbin/sh
ui_print() { echo "ui_print $1" > /proc/self/fd/$2; echo "ui_print" > /proc/self/fd/$2; }
dump_boot() { ui_print "Backing up boot image..."; }
write_boot() { 
    ui_print "Installing ArtPlus NEXT Kernel...";
    dd if=/tmp/Image of=/dev/block/platform/11120000.ufs/by-name/boot;
    ui_print "Kernel installed successfully!";
}
EOF
    
    # Create update-binary
    cat > AnyKernel3/META-INF/com/google/android/update-binary << 'EOF'
#!/sbin/sh
OUTFD=/proc/self/fd/$2
ZIPFILE="$3"
cd /tmp
unzip -o "$ZIPFILE"
sh anykernel.sh "$@"
EOF
    
    # Make scripts executable
    chmod +x AnyKernel3/anykernel.sh
    chmod +x AnyKernel3/tools/ak3-core.sh
    chmod +x AnyKernel3/META-INF/com/google/android/update-binary
    
    # Create ZIP package
    cd AnyKernel3
    zip -r9 "../ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" * > /dev/null 2>&1
    cd ..
    
    if [[ -f "ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip" ]]; then
        ZIP_SIZE=$(ls -lh ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip | awk '{print $5}')
        echo "✅ Flashable ZIP created: ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip ($ZIP_SIZE)"
        PACKAGE_SUCCESS=true
    else
        echo "❌ Failed to create ZIP package"
        PACKAGE_SUCCESS=false
    fi
fi

echo ""
echo "========================================"
echo "  🎯 INVESTIGATION RESULTS"
echo "========================================"
echo ""

if [[ $BUILD_SUCCESS == true ]]; then
    echo "🎉 **SUCCESS! Kernel build completed!**"
    echo ""
    echo "📱 **Files created:**"
    echo "   ✅ arch/arm64/boot/Image ($IMAGE_SIZE)"
    echo "   ✅ arch/arm64/boot/dtb.img (302K)"
    if [[ $PACKAGE_SUCCESS == true ]]; then
        echo "   ✅ ArtPlus-NEXT-G960F-250827-Permissive-KSU.zip ($ZIP_SIZE)"
    fi
    echo ""
    echo "🚀 **Ready for installation on Galaxy S9!**"
    echo ""
    echo "**Next steps:**"
    echo "1. Copy ZIP file to Galaxy S9 storage"
    echo "2. Boot to TWRP recovery"
    echo "3. Flash the ZIP file"
    echo "4. Install KernelSU Manager"
    echo "5. Enjoy your rooted Galaxy S9!"
    
else
    echo "⚠️  **Build investigation completed**"
    echo ""
    echo "**Status:**"
    echo "   ✅ Device tree built (dtb.img)"
    echo "   ❌ Kernel Image missing"
    echo "   ❌ No flashable package"
    echo ""
    echo "**Possible reasons:**"
    echo "1. Apollo build was interrupted"
    echo "2. Build completed but Image wasn't created"
    echo "3. Files are in a different location"
    echo ""
    echo "**Recommendations:**"
    echo "1. Run Apollo script again"
    echo "2. Check Apollo build logs"
    echo "3. Try manual build with Clang"
fi

echo ""
echo "All investigation logs saved for reference."
