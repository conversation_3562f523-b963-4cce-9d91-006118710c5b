#!/bin/bash
# Organize all project files into proper structure

echo "========================================"
echo "  📁 PROJECT FILE ORGANIZATION"
echo "========================================"
echo ""

# Define directories
WINDOWS_BASE="/mnt/c/Sviluppo/kernelsu-galaxys9"
WSL_KERNEL="$HOME/kernel-build-wsl"
FINAL_ORGANIZED="$WINDOWS_BASE/kernel-build-complete"

echo "🎯 Target organization: $FINAL_ORGANIZED"
echo "📂 Windows path: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-complete\\"
echo ""

# Create organized directory structure
echo "=== Creating Directory Structure ==="
mkdir -p "$FINAL_ORGANIZED"/{diagnostic-tools,build-scripts,documentation,kernel-source,logs,guides,tools}

echo "✅ Directory structure created:"
echo "   📁 diagnostic-tools/    - Diagnostic and analysis scripts"
echo "   📁 build-scripts/       - All build and fix scripts"  
echo "   📁 documentation/       - Reports and analysis files"
echo "   📁 kernel-source/       - Essential kernel source files"
echo "   📁 logs/               - Build logs and diagnostic output"
echo "   📁 guides/             - Step-by-step guides"
echo "   📁 tools/              - Utility scripts"
echo ""

echo "=== Organizing Diagnostic Tools ==="

# Copy diagnostic script
cp comprehensive_build_diagnostic.sh "$FINAL_ORGANIZED/diagnostic-tools/"
echo "✅ comprehensive_build_diagnostic.sh"

# Create quick diagnostic script
cat > "$FINAL_ORGANIZED/diagnostic-tools/quick_check.sh" << 'EOF'
#!/bin/bash
# Quick system check before building
echo "🔍 Quick Build Requirements Check"
echo ""

# Check essential tools
TOOLS=("make" "gcc" "clang" "tar" "xz" "aarch64-linux-gnu-gcc")
MISSING=0

for tool in "${TOOLS[@]}"; do
    if command -v "$tool" > /dev/null 2>&1; then
        echo "✅ $tool"
    else
        echo "❌ $tool - MISSING"
        ((MISSING++))
    fi
done

echo ""
if [[ $MISSING -eq 0 ]]; then
    echo "🎉 All essential tools available!"
    echo "Run: ./comprehensive_build_diagnostic.sh for detailed analysis"
else
    echo "⚠️  $MISSING tools missing"
    echo "Run: sudo apt install build-essential gcc-aarch64-linux-gnu xz-utils"
fi
EOF

chmod +x "$FINAL_ORGANIZED/diagnostic-tools/quick_check.sh"
echo "✅ quick_check.sh (created)"

echo ""
echo "=== Organizing Build Scripts ==="

# List of all build scripts to organize
BUILD_SCRIPTS=(
    "fix_kernel_build.sh"
    "fix_wireguard.sh"
    "fix_ssl_issues.sh" 
    "comprehensive_kernel_fix.sh"
    "quick_fix.sh"
    "wsl_diagnostic.sh"
    "wsl_kernel_fix.sh"
    "fix_defconfig_issues.sh"
    "fix_kconfig_build.sh"
    "fix_kbuild_include_issue.sh"
    "test_fixes.sh"
    "test_kernel_build_complete.sh"
    "organize_and_find_kernel.sh"
    "complete_kernel_build.sh"
    "fix_final_build_issues.sh"
    "final_organization_and_next_steps.sh"
    "investigate_and_complete_build.sh"
    "fix_and_build_kernel.sh"
)

script_count=0
for script in "${BUILD_SCRIPTS[@]}"; do
    # Check multiple locations
    LOCATIONS=("$WSL_KERNEL" "$WINDOWS_BASE" "$WINDOWS_BASE/wsl/exynos9810-kernel-artplus")
    
    for location in "${LOCATIONS[@]}"; do
        if [[ -f "$location/$script" ]]; then
            cp "$location/$script" "$FINAL_ORGANIZED/build-scripts/"
            echo "✅ $script"
            ((script_count++))
            break
        fi
    done
done

echo "📊 Total build scripts organized: $script_count"

echo ""
echo "=== Organizing Documentation ==="

# Documentation files
DOCS=(
    "complete_kernel_build_analysis_report.md"
    "galaxy_s9_kernel_installation_guide.md"
    "kernel_build_issues_report.md"
    "kernel_build_fix_guide.md"
    "kernel_build_advanced_fix_report.md"
    "wsl_kernel_build_fix_report.md"
    "final_wsl_kernel_build_report.md"
    "fix_wsl_service_error.md"
)

doc_count=0
for doc in "${DOCS[@]}"; do
    LOCATIONS=("$WINDOWS_BASE" "$WSL_KERNEL" "$WINDOWS_BASE/wsl/exynos9810-kernel-artplus")
    
    for location in "${LOCATIONS[@]}"; do
        if [[ -f "$location/$doc" ]]; then
            cp "$location/$doc" "$FINAL_ORGANIZED/documentation/"
            echo "✅ $doc"
            ((doc_count++))
            break
        fi
    done
done

echo "📊 Total documentation files: $doc_count"

echo ""
echo "=== Creating Comprehensive Guides ==="

# Create master README
cat > "$FINAL_ORGANIZED/README.md" << 'EOF'
# Galaxy S9 Kernel Build Project

## 🎯 Project Status: BUILD ANALYSIS COMPLETE

### Current Situation
- ✅ **All troubleshooting completed** - Comprehensive analysis done
- ❌ **Apollo build failed** - Missing system tools identified  
- 🔧 **Ready for final build** - All issues documented and solutions prepared

### Quick Start
1. **Run diagnostic**: `diagnostic-tools/comprehensive_build_diagnostic.sh`
2. **Install missing tools**: Follow diagnostic recommendations
3. **Build kernel**: Use Apollo script or manual build
4. **Install on Galaxy S9**: Follow installation guide

### Directory Structure
```
kernel-build-complete/
├── diagnostic-tools/     # Check system requirements
├── build-scripts/        # All troubleshooting scripts  
├── documentation/        # Complete analysis reports
├── kernel-source/        # Essential kernel files
├── logs/                # Build logs and diagnostics
├── guides/              # Step-by-step instructions
└── tools/               # Utility scripts
```

### Target Device
**Samsung Galaxy S9 International (SM-G960F)**
- Kernel: ArtPlus-NEXT-G960F-250827-Permissive-KSU
- Features: KernelSU root, SELinux Permissive
- Compiler: Clang 19.0.0-r530567

### Next Steps
1. Run comprehensive diagnostic
2. Install missing system tools
3. Complete kernel build
4. Flash to Galaxy S9

**All troubleshooting work is complete - ready for final build!**
EOF

# Create step-by-step guide
cat > "$FINAL_ORGANIZED/guides/STEP_BY_STEP_BUILD_GUIDE.md" << 'EOF'
# Step-by-Step Galaxy S9 Kernel Build Guide

## Phase 1: System Preparation

### Step 1: Run Comprehensive Diagnostic
```bash
cd /mnt/c/Sviluppo/kernelsu-galaxys9/kernel-build-complete/diagnostic-tools/
./comprehensive_build_diagnostic.sh
```

**What this does:**
- Checks all required build tools
- Verifies cross-compilers
- Analyzes kernel source
- Identifies missing components
- Provides installation commands

### Step 2: Install Missing Tools
Based on diagnostic results, install missing packages:
```bash
sudo apt update
sudo apt install -y build-essential gcc-aarch64-linux-gnu gcc-arm-linux-gnueabihf \
    xz-utils tar gzip bison flex bc python3 perl rsync cpio git curl wget clang
```

### Step 3: Verify Installation
```bash
./quick_check.sh
```

## Phase 2: Kernel Build

### Method 1: Apollo Script (Recommended)
```bash
cd ~/kernel-build-wsl
export ANDROID_MAJOR_VERSION=q
./apollo.sh

# Select:
# 1) starlte (Galaxy S9)
# 4) Clang 19
# 1) SELinux Permissive
# y) Enable KernelSU
# y) Clean build
```

### Method 2: Manual Build
```bash
cd ~/kernel-build-wsl

# Set environment
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export CROSS_COMPILE_ARM32=arm-linux-gnueabihf-
export CC=../compiler/clang-19.0.0-r530567/bin/clang
export ANDROID_MAJOR_VERSION=q

# Build
make starlte_defconfig
make -j$(nproc) Image dtbs
```

## Phase 3: Installation

### Prerequisites
- Samsung Galaxy S9 International (SM-G960F) **ONLY**
- Unlocked bootloader
- TWRP recovery installed
- Full device backup

### Installation Steps
1. Copy kernel ZIP to device storage
2. Boot to TWRP recovery
3. Install → Select ZIP file
4. Swipe to confirm flash
5. Reboot system
6. Install KernelSU Manager APK

## Troubleshooting
- **Build fails**: Check diagnostic results, install missing tools
- **Boot loop**: Flash stock kernel via ODIN
- **No root**: Reinstall KernelSU Manager

See documentation/ folder for detailed troubleshooting guides.
EOF

# Create installation guide
cat > "$FINAL_ORGANIZED/guides/GALAXY_S9_INSTALLATION.md" << 'EOF'
# Galaxy S9 Kernel Installation Guide

## ⚠️ CRITICAL WARNINGS
- **Device**: Samsung Galaxy S9 International (SM-G960F) **ONLY**
- **Bootloader**: Must be unlocked (voids warranty)
- **Recovery**: TWRP or custom recovery required
- **Backup**: Full NANDroid backup mandatory

## Installation Methods

### Method 1: TWRP Recovery (Recommended)
1. **Prepare files**: Copy kernel ZIP to device storage
2. **Boot to TWRP**: Volume Up + Bixby + Power
3. **Install**: Select ZIP file and swipe to confirm
4. **Reboot**: First boot takes 5-10 minutes

### Method 2: ODIN (Advanced)
1. **Create TAR**: Package Image file for ODIN
2. **Download mode**: Volume Down + Bixby + Power  
3. **Flash**: Load TAR in AP slot and start

## Post-Installation
1. **Install KernelSU Manager**: Download from GitHub releases
2. **Verify root**: Check app shows "Working" status
3. **Test functions**: Camera, calls, WiFi, fingerprint

## Troubleshooting
- **Boot loop**: Flash stock kernel
- **No root**: Reinstall KernelSU Manager
- **Performance issues**: Check CPU governor settings

## Recovery Options
- **Stock firmware**: Download from SamMobile
- **TWRP restore**: Use NANDroid backup
- **Emergency recovery**: Download mode + ODIN

**Only proceed if you understand the risks and have proper backups!**
EOF

echo "✅ Comprehensive guides created"

echo ""
echo "=== Copying Kernel Source (Essential Files) ==="

if [[ -d "$WSL_KERNEL" ]]; then
    echo "Copying essential kernel files..."
    
    # Copy key files only (not entire source to save space)
    ESSENTIAL_FILES=("Makefile" "Kconfig" ".config" "apollo.sh")
    ESSENTIAL_DIRS=("arch/arm64" "scripts" "include/linux")
    
    for file in "${ESSENTIAL_FILES[@]}"; do
        if [[ -f "$WSL_KERNEL/$file" ]]; then
            cp "$WSL_KERNEL/$file" "$FINAL_ORGANIZED/kernel-source/"
            echo "✅ $file"
        fi
    done
    
    for dir in "${ESSENTIAL_DIRS[@]}"; do
        if [[ -d "$WSL_KERNEL/$dir" ]]; then
            mkdir -p "$FINAL_ORGANIZED/kernel-source/$(dirname $dir)"
            cp -r "$WSL_KERNEL/$dir" "$FINAL_ORGANIZED/kernel-source/$dir"
            echo "✅ $dir/"
        fi
    done
else
    echo "⚠️  WSL kernel directory not accessible"
fi

echo ""
echo "=== Copying Build Logs ==="

LOG_FILES=("*.log" "apollo_complete.log" "final_build.log" "build_image.log")
log_count=0

for pattern in "${LOG_FILES[@]}"; do
    for location in "$WSL_KERNEL" "$WINDOWS_BASE"; do
        if [[ -d "$location" ]]; then
            find "$location" -maxdepth 1 -name "$pattern" -type f -exec cp {} "$FINAL_ORGANIZED/logs/" \; 2>/dev/null
            ((log_count++))
        fi
    done
done

echo "📊 Build logs copied: $log_count files"

echo ""
echo "=== Creating Project Summary ==="

cat > "$FINAL_ORGANIZED/PROJECT_STATUS.md" << EOF
# Project Status Summary

## 📊 Current Status: READY FOR FINAL BUILD

### Completed Work ✅
- **SSL Certificate Issues**: Resolved with bypass configuration
- **WSL Filesystem Issues**: Fixed by moving to native filesystem
- **Kbuild.include Syntax**: Fixed Makefile line 466 error
- **Cross-compiler Setup**: ARM64/ARM32 compilers configured
- **WireGuard Module**: Local configuration created
- **Comprehensive Analysis**: All issues documented and solutions prepared

### Identified Issues ❌
- **Missing System Tools**: xz-utils, tar utilities needed
- **Apollo Build Failure**: Incomplete due to missing tools
- **Netfilter Modules**: TCPMSS configuration issues
- **Kernel Image**: Not created in previous build attempt

### Files Organized 📁
- **$(ls -1 "$FINAL_ORGANIZED/build-scripts/" | wc -l) Build Scripts**: All troubleshooting tools preserved
- **$(ls -1 "$FINAL_ORGANIZED/documentation/" | wc -l) Documentation Files**: Complete analysis and guides
- **Diagnostic Tools**: Comprehensive system checking
- **Step-by-step Guides**: Clear instructions for completion

### Next Actions 🚀
1. **Run Diagnostic**: \`diagnostic-tools/comprehensive_build_diagnostic.sh\`
2. **Install Missing Tools**: Follow diagnostic recommendations  
3. **Build Kernel**: Use Apollo script with proper environment
4. **Install on Galaxy S9**: Follow installation guide

### Target Kernel 📱
- **Name**: ArtPlus-NEXT-G960F-250827-Permissive-KSU
- **Device**: Samsung Galaxy S9 International (SM-G960F)
- **Features**: KernelSU root, SELinux Permissive, Clang 19 optimizations

**All preparatory work is complete - ready for final build execution!**

Generated: $(date)
EOF

echo "✅ Project summary created"

echo ""
echo "========================================"
echo "  🎉 ORGANIZATION COMPLETE!"
echo "========================================"
echo ""

echo "📁 **All files organized in:**"
echo "   **Windows**: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-complete\\"
echo "   **WSL**: $FINAL_ORGANIZED"
echo ""

echo "📊 **Organization Summary:**"
echo "   📋 Diagnostic Tools: 2 scripts"
echo "   🔧 Build Scripts: $script_count files"
echo "   📚 Documentation: $doc_count files"
echo "   📖 Guides: 3 comprehensive guides"
echo "   📜 Build Logs: $log_count files"
echo "   📂 Kernel Source: Essential files"
echo ""

echo "🎯 **Next Steps:**"
echo "   1. Navigate to: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-complete\\"
echo "   2. Read: README.md for overview"
echo "   3. Run: diagnostic-tools/comprehensive_build_diagnostic.sh"
echo "   4. Follow: guides/STEP_BY_STEP_BUILD_GUIDE.md"
echo ""

echo "**All troubleshooting work is preserved and organized for easy access!**"
