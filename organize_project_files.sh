#!/bin/bash
# Organize all project files into proper structure

echo "========================================"
echo "  📁 PROJECT FILE ORGANIZATION"
echo "========================================"
echo ""

# Define directories
WINDOWS_BASE="/mnt/c/Sviluppo/kernelsu-galaxys9"
WSL_KERNEL="$HOME/kernel-build-wsl"
FINAL_ORGANIZED="$WINDOWS_BASE/kernel-build-complete"

echo "🎯 Target organization: $FINAL_ORGANIZED"
echo "📂 Windows path: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-complete\\"
echo ""

# Create organized directory structure
echo "=== Creating Directory Structure ==="
mkdir -p "$FINAL_ORGANIZED"/{diagnostic-tools,build-scripts,documentation,kernel-source,logs,guides,tools}

echo "✅ Directory structure created:"
echo "   📁 diagnostic-tools/    - Diagnostic and analysis scripts"
echo "   📁 build-scripts/       - All build and fix scripts"  
echo "   📁 documentation/       - Reports and analysis files"
echo "   📁 kernel-source/       - Essential kernel source files"
echo "   📁 logs/               - Build logs and diagnostic output"
echo "   📁 guides/             - Step-by-step guides"
echo "   📁 tools/              - Utility scripts"
echo ""

echo "=== Organizing Diagnostic Tools ==="

# Copy diagnostic script
cp "$WINDOWS_BASE/comprehensive_build_diagnostic.sh" "$FINAL_ORGANIZED/diagnostic-tools/"
echo "✅ comprehensive_build_diagnostic.sh"

# Create quick diagnostic script
cat > "$FINAL_ORGANIZED/diagnostic-tools/quick_check.sh" << 'EOF'
#!/bin/bash
# Quick system check before building
echo "🔍 Quick Build Requirements Check"
echo ""

# Check essential tools
TOOLS=("make" "gcc" "clang" "tar" "xz" "aarch64-linux-gnu-gcc")
MISSING=0

for tool in "${TOOLS[@]}"; do
    if command -v "$tool" > /dev/null 2>&1; then
        echo "✅ $tool"
    else
        echo "❌ $tool - MISSING"
        ((MISSING++))
    fi
done

echo ""
if [[ $MISSING -eq 0 ]]; then
    echo "🎉 All essential tools available!"
    echo "Run: ./comprehensive_build_diagnostic.sh for detailed analysis"
else
    echo "⚠️  $MISSING tools missing"
    echo "Run: sudo apt install build-essential gcc-aarch64-linux-gnu xz-utils"
fi
EOF

chmod +x "$FINAL_ORGANIZED/diagnostic-tools/quick_check.sh"
echo "✅ quick_check.sh (created)"

echo ""
echo "=== Creating Comprehensive Guides ==="

# Create master README
cat > "$FINAL_ORGANIZED/README.md" << 'EOF'
# Galaxy S9 Kernel Build Project

## 🎯 Project Status: BUILD ANALYSIS COMPLETE

### Current Situation
- ✅ **All troubleshooting completed** - Comprehensive analysis done
- ❌ **Apollo build failed** - Missing system tools identified  
- 🔧 **Ready for final build** - All issues documented and solutions prepared

### Quick Start
1. **Run diagnostic**: `diagnostic-tools/comprehensive_build_diagnostic.sh`
2. **Install missing tools**: Follow diagnostic recommendations
3. **Build kernel**: Use Apollo script or manual build
4. **Install on Galaxy S9**: Follow installation guide

### Directory Structure
```
kernel-build-complete/
├── diagnostic-tools/     # Check system requirements
├── build-scripts/        # All troubleshooting scripts  
├── documentation/        # Complete analysis reports
├── kernel-source/        # Essential kernel files
├── logs/                # Build logs and diagnostics
├── guides/              # Step-by-step instructions
└── tools/               # Utility scripts
```

### Target Device
**Samsung Galaxy S9 International (SM-G960F)**
- Kernel: ArtPlus-NEXT-G960F-250827-Permissive-KSU
- Features: KernelSU root, SELinux Permissive
- Compiler: Clang 19.0.0-r530567

### Next Steps
1. Run comprehensive diagnostic
2. Install missing system tools
3. Complete kernel build
4. Flash to Galaxy S9

**All troubleshooting work is complete - ready for final build!**
EOF

# Create step-by-step guide
cat > "$FINAL_ORGANIZED/guides/STEP_BY_STEP_BUILD_GUIDE.md" << 'EOF'
# Step-by-Step Galaxy S9 Kernel Build Guide

## Phase 1: System Preparation

### Step 1: Run Comprehensive Diagnostic
```bash
cd /mnt/c/Sviluppo/kernelsu-galaxys9/kernel-build-complete/diagnostic-tools/
./comprehensive_build_diagnostic.sh
```

### Step 2: Install Missing Tools
Based on diagnostic results, install missing packages:
```bash
sudo apt update
sudo apt install -y build-essential gcc-aarch64-linux-gnu gcc-arm-linux-gnueabihf \
    xz-utils tar gzip bison flex bc python3 perl rsync cpio git curl wget clang
```

### Step 3: Verify Installation
```bash
./quick_check.sh
```

## Phase 2: Kernel Build

### Method 1: Apollo Script (Recommended)
```bash
cd ~/kernel-build-wsl
export ANDROID_MAJOR_VERSION=q
./apollo.sh

# Select:
# 1) starlte (Galaxy S9)
# 4) Clang 19
# 1) SELinux Permissive
# y) Enable KernelSU
# y) Clean build
```

## Phase 3: Installation

### Prerequisites
- Samsung Galaxy S9 International (SM-G960F) **ONLY**
- Unlocked bootloader
- TWRP recovery installed
- Full device backup

### Installation Steps
1. Copy kernel ZIP to device storage
2. Boot to TWRP recovery
3. Install → Select ZIP file
4. Swipe to confirm flash
5. Reboot system
6. Install KernelSU Manager APK
EOF

echo "✅ Comprehensive guides created"

echo ""
echo "========================================"
echo "  🎉 ORGANIZATION COMPLETE!"
echo "========================================"
echo ""

echo "📁 **All files organized in:**"
echo "   **Windows**: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-complete\\"
echo "   **WSL**: $FINAL_ORGANIZED"
echo ""

echo "🎯 **Next Steps:**"
echo "   1. Navigate to: C:\\Sviluppo\\kernelsu-galaxys9\\kernel-build-complete\\"
echo "   2. Read: README.md for overview"
echo "   3. Run: diagnostic-tools/comprehensive_build_diagnostic.sh"
echo "   4. Follow: guides/STEP_BY_STEP_BUILD_GUIDE.md"
echo ""

echo "**All troubleshooting work is preserved and organized for easy access!**"
