#!/bin/bash
# Comprehensive Kernel Build Diagnostic Script
# This script checks all requirements before attempting to build

echo "========================================"
echo "  🔍 KERNEL BUILD DIAGNOSTIC TOOL"
echo "  Version 1.0 - Complete Analysis"
echo "========================================"
echo ""

# Initialize counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
CRITICAL_ISSUES=0
WARNINGS=0

# Create log file
LOG_FILE="diagnostic_$(date +%Y%m%d_%H%M%S).log"
exec > >(tee -a "$LOG_FILE")
exec 2>&1

echo "📝 Diagnostic log: $LOG_FILE"
echo "🕐 Started at: $(date)"
echo ""

echo "=== SECTION 1: SYSTEM ENVIRONMENT ==="
echo ""

# Check operating system
echo "🖥️  Operating System:"
if [[ -f /etc/os-release ]]; then
    source /etc/os-release
    echo "   OS: $PRETTY_NAME"
    echo "   Version: $VERSION"
    ((TOTAL_CHECKS++))
    ((PASSED_CHECKS++))
else
    echo "   ❌ Cannot determine OS version"
    ((TOTAL_CHECKS++))
    ((CRITICAL_ISSUES++))
fi

# Check WSL environment
echo ""
echo "🐧 WSL Environment:"
if [[ -n "$WSL_DISTRO_NAME" ]]; then
    echo "   ✅ Running in WSL: $WSL_DISTRO_NAME"
    echo "   WSL Version: $(cat /proc/version | grep -o 'WSL[0-9]*' || echo 'WSL1')"
    WSL_ENV=true
else
    echo "   ℹ️  Not running in WSL (native Linux)"
    WSL_ENV=false
fi

# Check available memory and disk space
echo ""
echo "💾 System Resources:"
MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
DISK_FREE_GB=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')

echo "   RAM: ${MEMORY_GB}GB total"
echo "   Free disk space: ${DISK_FREE_GB}GB"

if [[ $MEMORY_GB -lt 4 ]]; then
    echo "   ⚠️  Warning: Less than 4GB RAM (kernel build may be slow)"
    ((WARNINGS++))
fi

if [[ $DISK_FREE_GB -lt 10 ]]; then
    echo "   ❌ Critical: Less than 10GB free space needed"
    ((CRITICAL_ISSUES++))
fi

echo ""
echo "=== SECTION 2: BUILD TOOLS ANALYSIS ==="
echo ""

# Essential build tools
ESSENTIAL_TOOLS=(
    "make:build-essential"
    "gcc:build-essential" 
    "g++:build-essential"
    "tar:tar"
    "xz:xz-utils"
    "gzip:gzip"
    "bison:bison"
    "flex:flex"
    "bc:bc"
    "python3:python3"
    "perl:perl"
    "rsync:rsync"
    "cpio:cpio"
    "git:git"
    "curl:curl"
    "wget:wget"
)

echo "🔧 Essential Build Tools:"
MISSING_TOOLS=()
MISSING_PACKAGES=()

for tool_info in "${ESSENTIAL_TOOLS[@]}"; do
    tool=$(echo "$tool_info" | cut -d: -f1)
    package=$(echo "$tool_info" | cut -d: -f2)
    
    ((TOTAL_CHECKS++))
    if command -v "$tool" > /dev/null 2>&1; then
        version_info=$(command -v "$tool")
        echo "   ✅ $tool: $version_info"
        ((PASSED_CHECKS++))
    else
        echo "   ❌ $tool: NOT FOUND"
        MISSING_TOOLS+=("$tool")
        MISSING_PACKAGES+=("$package")
        ((CRITICAL_ISSUES++))
    fi
done

echo ""
echo "🎯 Cross-Compilation Tools:"

# ARM64 cross compiler
((TOTAL_CHECKS++))
if command -v aarch64-linux-gnu-gcc > /dev/null 2>&1; then
    ARM64_VERSION=$(aarch64-linux-gnu-gcc --version | head -1)
    echo "   ✅ ARM64: $ARM64_VERSION"
    ((PASSED_CHECKS++))
    ARM64_AVAILABLE=true
else
    echo "   ❌ ARM64 cross compiler missing (aarch64-linux-gnu-gcc)"
    MISSING_PACKAGES+=("gcc-aarch64-linux-gnu")
    ((CRITICAL_ISSUES++))
    ARM64_AVAILABLE=false
fi

# ARM32 cross compiler
((TOTAL_CHECKS++))
if command -v arm-linux-gnueabihf-gcc > /dev/null 2>&1; then
    ARM32_VERSION=$(arm-linux-gnueabihf-gcc --version | head -1)
    echo "   ✅ ARM32: $ARM32_VERSION"
    ((PASSED_CHECKS++))
    ARM32_AVAILABLE=true
else
    echo "   ❌ ARM32 cross compiler missing (arm-linux-gnueabihf-gcc)"
    MISSING_PACKAGES+=("gcc-arm-linux-gnueabihf")
    ((CRITICAL_ISSUES++))
    ARM32_AVAILABLE=false
fi

echo ""
echo "=== SECTION 3: CLANG COMPILER ANALYSIS ==="
echo ""

# Check for downloaded Clang compiler
CLANG_LOCATIONS=(
    "../compiler/clang-19.0.0-r530567"
    "compiler/clang-19.0.0-r530567"
    "../clang-19.0.0-r530567"
)

CLANG_FOUND=false
CLANG_PATH=""

echo "🔍 Searching for Clang 19 compiler:"
for location in "${CLANG_LOCATIONS[@]}"; do
    echo "   Checking: $location"
    if [[ -d "$location" ]]; then
        if [[ -f "$location/bin/clang" ]]; then
            CLANG_VERSION=$("$location/bin/clang" --version | head -1)
            echo "   ✅ Found: $CLANG_VERSION"
            echo "   📁 Location: $(realpath $location)"
            CLANG_FOUND=true
            CLANG_PATH="$location"
            ((PASSED_CHECKS++))
            break
        else
            echo "   ⚠️  Directory exists but clang binary missing"
        fi
    else
        echo "   ❌ Not found"
    fi
done

((TOTAL_CHECKS++))
if [[ $CLANG_FOUND == false ]]; then
    echo "   ❌ Clang 19 compiler not found in expected locations"
    echo "   ℹ️  Apollo script should download this automatically"
    ((CRITICAL_ISSUES++))
fi

# Check system Clang as fallback
echo ""
echo "🔄 System Clang (fallback):"
((TOTAL_CHECKS++))
if command -v clang > /dev/null 2>&1; then
    SYSTEM_CLANG_VERSION=$(clang --version | head -1)
    echo "   ✅ System Clang: $SYSTEM_CLANG_VERSION"
    ((PASSED_CHECKS++))
    SYSTEM_CLANG_AVAILABLE=true
else
    echo "   ❌ System Clang not available"
    MISSING_PACKAGES+=("clang")
    SYSTEM_CLANG_AVAILABLE=false
fi

echo ""
echo "=== SECTION 4: KERNEL SOURCE ANALYSIS ==="
echo ""

# Check kernel source structure
echo "📂 Kernel Source Structure:"

KERNEL_DIRS=("arch" "drivers" "net" "scripts" "include" "kernel" "mm" "fs")
KERNEL_FILES=("Makefile" "Kconfig" "apollo.sh")

for dir in "${KERNEL_DIRS[@]}"; do
    ((TOTAL_CHECKS++))
    if [[ -d "$dir" ]]; then
        file_count=$(find "$dir" -type f | wc -l)
        echo "   ✅ $dir/: $file_count files"
        ((PASSED_CHECKS++))
    else
        echo "   ❌ $dir/: Missing"
        ((CRITICAL_ISSUES++))
    fi
done

for file in "${KERNEL_FILES[@]}"; do
    ((TOTAL_CHECKS++))
    if [[ -f "$file" ]]; then
        echo "   ✅ $file: Present"
        ((PASSED_CHECKS++))
    else
        echo "   ❌ $file: Missing"
        ((CRITICAL_ISSUES++))
    fi
done

# Check kernel configuration
echo ""
echo "⚙️  Kernel Configuration:"
((TOTAL_CHECKS++))
if [[ -f ".config" ]]; then
    echo "   ✅ .config exists"
    
    # Check specific configurations
    if grep -q "CONFIG_ARM64=y" .config; then
        echo "   ✅ ARM64 architecture enabled"
    else
        echo "   ⚠️  ARM64 architecture not configured"
        ((WARNINGS++))
    fi
    
    if grep -q "CONFIG_ANDROID" .config; then
        echo "   ✅ Android configuration found"
    else
        echo "   ⚠️  Android configuration not found"
        ((WARNINGS++))
    fi
    
    ((PASSED_CHECKS++))
else
    echo "   ❌ .config missing (run defconfig first)"
    ((CRITICAL_ISSUES++))
fi

echo ""
echo "=== SECTION 5: PREVIOUS BUILD ANALYSIS ==="
echo ""

# Check for previous build artifacts
echo "🔍 Previous Build Artifacts:"

BUILD_ARTIFACTS=(
    "vmlinux:Compiled kernel"
    "arch/arm64/boot/Image:Kernel image"
    "arch/arm64/boot/dtb.img:Device tree"
    "modules.builtin:Built-in modules"
    "System.map:Symbol map"
)

PREVIOUS_BUILD_EXISTS=false
for artifact_info in "${BUILD_ARTIFACTS[@]}"; do
    artifact=$(echo "$artifact_info" | cut -d: -f1)
    description=$(echo "$artifact_info" | cut -d: -f2)
    
    if [[ -f "$artifact" ]]; then
        size=$(ls -lh "$artifact" | awk '{print $5}')
        echo "   ✅ $description: $artifact ($size)"
        PREVIOUS_BUILD_EXISTS=true
    else
        echo "   ❌ $description: Not found"
    fi
done

if [[ $PREVIOUS_BUILD_EXISTS == true ]]; then
    echo "   ℹ️  Previous build artifacts found (may need cleaning)"
else
    echo "   ℹ️  Clean state - no previous build artifacts"
fi

# Check for build logs
echo ""
echo "📜 Build Logs Analysis:"
LOG_FILES=("apollo_complete.log" "final_build.log" "build_image.log")

for log in "${LOG_FILES[@]}"; do
    if [[ -f "$log" ]]; then
        lines=$(wc -l < "$log")
        echo "   ✅ $log: $lines lines"
        
        # Check for errors in logs
        error_count=$(grep -c -i "error" "$log" 2>/dev/null || echo "0")
        if [[ $error_count -gt 0 ]]; then
            echo "      ⚠️  Contains $error_count error messages"
            ((WARNINGS++))
        fi
    else
        echo "   ❌ $log: Not found"
    fi
done

echo ""
echo "========================================"
echo "  📊 DIAGNOSTIC SUMMARY"
echo "========================================"
echo ""

# Calculate scores
SUCCESS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

echo "🎯 **Overall Assessment:**"
echo "   Total Checks: $TOTAL_CHECKS"
echo "   Passed: $PASSED_CHECKS"
echo "   Success Rate: $SUCCESS_RATE%"
echo "   Critical Issues: $CRITICAL_ISSUES"
echo "   Warnings: $WARNINGS"
echo ""

# Determine build readiness
if [[ $CRITICAL_ISSUES -eq 0 ]] && [[ $SUCCESS_RATE -ge 90 ]]; then
    BUILD_STATUS="READY"
    STATUS_ICON="🎉"
    STATUS_COLOR="GREEN"
elif [[ $CRITICAL_ISSUES -le 2 ]] && [[ $SUCCESS_RATE -ge 70 ]]; then
    BUILD_STATUS="MOSTLY_READY"
    STATUS_ICON="⚠️"
    STATUS_COLOR="YELLOW"
else
    BUILD_STATUS="NOT_READY"
    STATUS_ICON="❌"
    STATUS_COLOR="RED"
fi

echo "$STATUS_ICON **BUILD STATUS: $BUILD_STATUS**"
echo ""

# Generate installation commands
if [[ ${#MISSING_PACKAGES[@]} -gt 0 ]]; then
    echo "📦 **Required Package Installation:**"
    echo ""
    
    # Remove duplicates
    UNIQUE_PACKAGES=($(printf "%s\n" "${MISSING_PACKAGES[@]}" | sort -u))
    
    echo "   sudo apt update"
    echo "   sudo apt install -y \\"
    for package in "${UNIQUE_PACKAGES[@]}"; do
        echo "     $package \\"
    done
    echo ""
fi

# Generate next steps
echo "🚀 **Next Steps:**"
echo ""

case $BUILD_STATUS in
    "READY")
        echo "   ✅ All requirements satisfied!"
        echo "   1. Proceed with kernel build"
        echo "   2. Run: ./apollo.sh (recommended)"
        echo "   3. Or run manual build script"
        ;;
    "MOSTLY_READY")
        echo "   ⚠️  Minor issues to resolve:"
        echo "   1. Install missing packages (see above)"
        echo "   2. Address any warnings"
        echo "   3. Run diagnostic again to verify"
        echo "   4. Proceed with kernel build"
        ;;
    "NOT_READY")
        echo "   ❌ Critical issues must be resolved:"
        echo "   1. Install missing packages (see above)"
        echo "   2. Fix critical issues identified"
        echo "   3. Run diagnostic again"
        echo "   4. Repeat until status is READY"
        ;;
esac

echo ""
echo "📝 **Detailed Report Saved:** $LOG_FILE"
echo "🕐 **Completed at:** $(date)"
echo ""
echo "========================================"

# Return appropriate exit code
case $BUILD_STATUS in
    "READY") exit 0 ;;
    "MOSTLY_READY") exit 1 ;;
    "NOT_READY") exit 2 ;;
esac
