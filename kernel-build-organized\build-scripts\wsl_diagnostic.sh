#!/bin/bash
# WSL Kernel Build Diagnostic Script
# Identifies WSL-specific issues affecting kernel builds

echo "========================================"
echo "  WSL Kernel Build Diagnostic"
echo "========================================"
echo ""

# Check WSL environment
echo "=== Environment Check ==="
if [[ -f /proc/version ]] && grep -q Microsoft /proc/version; then
    echo "✓ Running in WSL"
    grep Microsoft /proc/version
else
    echo "❌ Not running in WSL or WSL not detected"
fi

echo "Current directory: $(pwd)"
echo "User: $(whoami)"
echo "Home: $HOME"
echo ""

# Check filesystem type
echo "=== Filesystem Check ==="
CURRENT_FS=$(df -T . | tail -1 | awk '{print $2}')
echo "Current filesystem type: $CURRENT_FS"

if [[ "$CURRENT_FS" == "9p" ]] || [[ "$(pwd)" == /mnt/c/* ]]; then
    echo "⚠️  WARNING: You're on Windows filesystem (/mnt/c/)"
    echo "   This can cause line ending and permission issues"
    echo "   Recommendation: Copy source to WSL native filesystem (~/ or /home/<USER>"
else
    echo "✓ Using WSL native filesystem"
fi
echo ""

# Check line endings in critical files
echo "=== Line Ending Check ==="
CRITICAL_FILES=("Makefile" "scripts/Kbuild.include" "scripts/fetch-latest-wireguard.sh")

for file in "${CRITICAL_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        # Check for CRLF line endings
        if file "$file" | grep -q CRLF; then
            echo "❌ $file has Windows CRLF line endings"
        elif file "$file" | grep -q "with very long lines"; then
            echo "⚠️  $file may have line ending issues"
        else
            echo "✓ $file appears to have Unix line endings"
        fi
    else
        echo "❌ $file not found"
    fi
done
echo ""

# Check toolchain availability
echo "=== Toolchain Check ==="
TOOLCHAINS=("aarch64-linux-android-gcc" "aarch64-linux-gnu-gcc" "gcc")

for toolchain in "${TOOLCHAINS[@]}"; do
    if command -v $toolchain > /dev/null; then
        echo "✓ $toolchain found: $(which $toolchain)"
        if [[ "$toolchain" != "gcc" ]]; then
            echo "   Version: $($toolchain --version 2>/dev/null | head -1 || echo 'Version check failed')"
        fi
    else
        echo "❌ $toolchain not found"
    fi
done

# Check for Android NDK
echo ""
echo "Android NDK locations:"
NDK_PATHS=("$HOME/android-ndk-r26d" "$HOME/Android/Sdk/ndk" "/opt/android-ndk")
for ndk_path in "${NDK_PATHS[@]}"; do
    if [[ -d "$ndk_path" ]]; then
        echo "✓ Found NDK at: $ndk_path"
    else
        echo "❌ No NDK at: $ndk_path"
    fi
done
echo ""

# Check required packages
echo "=== Package Check ==="
REQUIRED_PACKAGES=("build-essential" "bc" "bison" "flex" "libssl-dev" "dos2unix")

for package in "${REQUIRED_PACKAGES[@]}"; do
    if dpkg -l | grep -q "^ii  $package "; then
        echo "✓ $package installed"
    else
        echo "❌ $package not installed"
    fi
done
echo ""

# Check file permissions
echo "=== Permission Check ==="
SCRIPT_FILES=("scripts/fetch-latest-wireguard.sh" "apollo.sh" "build_kernel.sh")

for script in "${SCRIPT_FILES[@]}"; do
    if [[ -f "$script" ]]; then
        PERMS=$(ls -l "$script" | cut -d' ' -f1)
        if [[ "$PERMS" == *"x"* ]]; then
            echo "✓ $script is executable ($PERMS)"
        else
            echo "❌ $script is not executable ($PERMS)"
        fi
    else
        echo "❌ $script not found"
    fi
done
echo ""

# Check environment variables
echo "=== Environment Variables ==="
ENV_VARS=("ANDROID_MAJOR_VERSION" "ARCH" "CROSS_COMPILE" "PATH")

for var in "${ENV_VARS[@]}"; do
    if [[ -n "${!var}" ]]; then
        if [[ "$var" == "PATH" ]]; then
            echo "✓ $var is set (length: ${#PATH})"
        else
            echo "✓ $var=${!var}"
        fi
    else
        echo "❌ $var not set"
    fi
done
echo ""

# Test make command
echo "=== Make Test ==="
if command -v make > /dev/null; then
    echo "✓ make found: $(which make)"
    echo "   Version: $(make --version | head -1)"
    
    # Test basic make syntax
    echo "Testing basic Makefile syntax..."
    if make -n help > /dev/null 2>&1; then
        echo "✓ Basic make syntax test passed"
    else
        echo "❌ Basic make syntax test failed"
        echo "   This suggests line ending or Makefile syntax issues"
    fi
else
    echo "❌ make not found"
fi
echo ""

# Check specific error from log
echo "=== Specific Error Check ==="
if [[ -f "scripts/Kbuild.include" ]]; then
    echo "Checking scripts/Kbuild.include line 466..."
    
    # Show line 466 and surrounding lines
    sed -n '464,468p' scripts/Kbuild.include | cat -n
    
    # Check for tabs vs spaces
    if sed -n '466p' scripts/Kbuild.include | grep -q $'\t'; then
        echo "✓ Line 466 contains tabs"
    else
        echo "❌ Line 466 may have spaces instead of tabs"
    fi
    
    # Check line ending of that specific line
    LINE_466=$(sed -n '466p' scripts/Kbuild.include)
    if [[ "$LINE_466" == *$'\r' ]]; then
        echo "❌ Line 466 has CRLF line ending"
    else
        echo "✓ Line 466 appears to have Unix line ending"
    fi
else
    echo "❌ scripts/Kbuild.include not found"
fi
echo ""

# Summary and recommendations
echo "========================================"
echo "  Diagnostic Summary"
echo "========================================"
echo ""

ISSUES_FOUND=0

# Check for common issues
if [[ "$(pwd)" == /mnt/c/* ]]; then
    echo "🔴 CRITICAL: Working on Windows filesystem"
    echo "   Solution: Copy source to ~/kernel-build"
    ((ISSUES_FOUND++))
fi

if ! command -v aarch64-linux-gnu-gcc > /dev/null && ! command -v aarch64-linux-android-gcc > /dev/null; then
    echo "🔴 CRITICAL: No ARM64 cross-compiler found"
    echo "   Solution: sudo apt install gcc-aarch64-linux-gnu"
    ((ISSUES_FOUND++))
fi

if ! dpkg -l | grep -q "^ii  dos2unix "; then
    echo "🟡 WARNING: dos2unix not installed"
    echo "   Solution: sudo apt install dos2unix"
    ((ISSUES_FOUND++))
fi

if [[ $ISSUES_FOUND -eq 0 ]]; then
    echo "✅ No major issues detected"
    echo "   The build problems may be kernel-specific rather than WSL-specific"
else
    echo "🔧 $ISSUES_FOUND issue(s) found that need attention"
fi

echo ""
echo "Recommended next steps:"
echo "1. Run: chmod +x wsl_kernel_fix.sh"
echo "2. Run: ./wsl_kernel_fix.sh"
echo "3. This will address all detected WSL-specific issues"
